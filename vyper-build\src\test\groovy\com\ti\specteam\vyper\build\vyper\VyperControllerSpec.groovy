package com.ti.specteam.vyper.build.vyper

import com.ti.specteam.foundational.smarttable.SmartResult
import com.ti.specteam.vyper.actions.*
import com.ti.specteam.vyper.atss.attribute.AttributeService
import com.ti.specteam.vyper.atss.child.Child
import com.ti.specteam.vyper.atss.child.ChildService
import com.ti.specteam.vyper.atss.device.AtssMaterialService
import com.ti.specteam.vyper.atss.facility.AtssFacilityService
import com.ti.specteam.vyper.atss.facility.FacilityService
import com.ti.specteam.vyper.atss.operations.OperationRepository
import com.ti.specteam.vyper.build.ComponentService
import com.ti.specteam.vyper.build.componentmap.ComponentMap
import com.ti.specteam.vyper.build.componentmap.ComponentMapService
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.build.sandbox.PgsSandboxService
import com.ti.specteam.vyper.dashboard.DashBoardService
import com.ti.specteam.vyper.device.DeviceReportRepository
import com.ti.specteam.vyper.device.DeviceService
import com.ti.specteam.vyper.entity.build.BuildEntityService
import com.ti.specteam.vyper.entity.praBuildReport.PraBuildReportEntity
import com.ti.specteam.vyper.entity.vyper.VyperEntityService
import com.ti.specteam.vyper.packageniche.PackageNicheService
import com.ti.specteam.vyper.praBuildReport.PraBuildReportService
import com.ti.specteam.vyper.security.user.UserUtilsService
import com.ti.specteam.vyper.workflow.RejectReasonsConfigService
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import spock.lang.Specification

import javax.servlet.ServletOutputStream
import javax.servlet.http.HttpServletResponse
/**
 * <AUTHOR> Woods
 */
class VyperControllerSpec extends Specification {

    VyperEntityService vyperEntityService = Mock(VyperEntityService)
    OperationRepository operationRepository = Mock(OperationRepository)
    ComponentService componentService = Mock(ComponentService)
    AttributeService attributeService = Mock(AttributeService)
    ChangeTitleAction changeTitleAction = Mock(ChangeTitleAction)
    ChangeDescriptionAction changeDescriptionAction = Mock(ChangeDescriptionAction)
    ChangeScswrControlNumberAction changeScswrControlNumberAction = Mock(ChangeScswrControlNumberAction)
    ChangeBuildtypeAction changeBuildtypeAction = Mock(ChangeBuildtypeAction)
    AddOwnerAction addOwnerAction = Mock(AddOwnerAction)
    RemoveOwnerAction removeOwnerAction = Mock(RemoveOwnerAction)
    UploadTestAction uploadTestAction = Mock(UploadTestAction)
    ChangeMaterialAction changeMaterialAction = Mock(ChangeMaterialAction)
    ChangeWorkflowAction changeWorkflowAction = Mock(ChangeWorkflowAction)
    ChangeFacilityAction changeFacilityAction = Mock(ChangeFacilityAction)
    AddCommentAction addCommentAction = Mock(AddCommentAction)
    ChangeEslAction changeEslAction = Mock(ChangeEslAction)
    ChangePackConfigAction changePackConfigAction = Mock(ChangePackConfigAction)
    AddBuildAction addBuildAction = Mock(AddBuildAction)
    ChangeArmarcAction changeArmarcAction = Mock(ChangeArmarcAction)
    ChangeAtssAction changeAtssAction = Mock(ChangeAtssAction)
    ChangeDiesAction changeDiesAction = Mock(ChangeDiesAction)
    ChangeComponentsAction changeComponentsAction = Mock(ChangeComponentsAction)
    ChangeSymbolizationAction changeSymbolizationAction = Mock(ChangeSymbolizationAction)
    AddFlowOperationAction addFlowOperationAction = Mock(AddFlowOperationAction)
    RemoveFlowOperationAction removeFlowOperationAction = Mock(RemoveFlowOperationAction)
    AddFlowComponentAction addFlowComponentAction = Mock(AddFlowComponentAction)
    RemoveFlowComponentAction removeFlowComponentAction = Mock(RemoveFlowComponentAction)
    ChangeSelectionAction changeSelectionAction = Mock(ChangeSelectionAction)
    PgsSandboxService pgsSandboxService = Mock(PgsSandboxService)
    ChangeTurnkeyAction changeTurnkeyAction = Mock(ChangeTurnkeyAction)
    ChangeChangelinkChangeAction changeChangelinkChangeAction = Mock(ChangeChangelinkChangeAction)
    ChangeChangelinkPcnAction changeChangelinkPcnAction = Mock(ChangeChangelinkPcnAction)
    ChangeFlowOperationAction changeFlowOperationAction = Mock(ChangeFlowOperationAction)
    ChangeFlowComponentAction changeFlowComponentAction = Mock(ChangeFlowComponentAction)
    ChangePkgNicheAction changePkgNicheAction = Mock(ChangePkgNicheAction)
    CanApproveAction canApproveAction = Mock(CanApproveAction)
    CompareAtssAction compareAtssAction = Mock(CompareAtssAction)
    ToggleOperationValidateAction toggleOperationValidateAction = Mock(ToggleOperationValidateAction)
    CompareTravelerAction compareTravelerAction = Mock(CompareTravelerAction)
    DashBoardService dashBoardService = Mock(DashBoardService)
    ChangeDryBakeAction changeDryBakeAction = Mock(ChangeDryBakeAction)
    FacilityService facilityService = Mock(FacilityService)
    DeviceService deviceService = Mock(DeviceService)
    RefreshPgsAction refreshPgsAction = Mock(RefreshPgsAction)
    RefreshBomTemplateAction refreshBomTemplateAction = Mock(RefreshBomTemplateAction)
    RefreshFlowAction refreshFlowAction = Mock(RefreshFlowAction)
    RejectReasonsConfigService rejectReasonsConfigService = Mock(RejectReasonsConfigService)
    ReviewBomTemplateAction reviewBomTemplateAction = Mock(ReviewBomTemplateAction)
    FillComponentVyperAction fillComponentVyperAction = Mock(FillComponentVyperAction)
    FillComponentAtssAction fillComponentAtssAction = Mock(FillComponentAtssAction)
    ComponentMapService componentMapService = Mock(ComponentMapService)
    VyperCopyBuildAction vyperCopyBuildAction = Mock(VyperCopyBuildAction)
    ChangeBackgrindAction changeBackgrindAction = Mock(ChangeBackgrindAction)
    FillComponentClearAction fillComponentClearAction = Mock(FillComponentClearAction)
    AddBuildCopyAction addBuildCopyAction = Mock(AddBuildCopyAction)
    ChangeOperationCommentAction changeOperationCommentAction = Mock(ChangeOperationCommentAction)
    AtssMaterialService atssMaterialService = Mock(AtssMaterialService)
    AtssFacilityService atssFacilityService = Mock(AtssFacilityService)
    ChangeWaferSawMethodAction changeWaferSawMethodAction = Mock(ChangeWaferSawMethodAction)
    PackageNicheService packageNicheService = Mock(PackageNicheService)
    CreateVyperAndCopyBuildAction createVyperAndCopyBuildAction = Mock(CreateVyperAndCopyBuildAction)
    ChildService childService = Mock(ChildService)
    BuildService buildService = Mock(BuildService)
    BuildEntityService buildEntityService = Mock(BuildEntityService)
    DownloadVyperExcelAction downloadVyperExcelAction = Mock(DownloadVyperExcelAction)
    RestoreFlowOperationAction restoreFlowOperationAction = Mock(RestoreFlowOperationAction)
    UpdateBuildAction updateBuildAction = Mock(UpdateBuildAction)
    PraBuildReportService praBuildReportService = Mock(PraBuildReportService)
    ApproveDiagramAction approveDiagramAction = Mock(ApproveDiagramAction)
    DeviceReportRepository deviceReportRepository = Mock(DeviceReportRepository)
    UserUtilsService userUtilsService = Mock(UserUtilsService)
    ReviewPkgNicheBomTemplateAction reviewPkgNicheBomTemplateAction = Mock(ReviewPkgNicheBomTemplateAction)
    ListPackConfigAction listPackConfigAction = Mock(ListPackConfigAction)
    CheckArmarcAction checkArmarcAction = Mock(CheckArmarcAction)
    RefreshAttributesAction refreshAttributesAction = Mock(RefreshAttributesAction)

    VyperController controller = new VyperController(
            vyperEntityService,
            operationRepository,
            componentService,
            attributeService,
            changeTitleAction,
            changeDescriptionAction,
            changeScswrControlNumberAction,
            changeBuildtypeAction,
            addOwnerAction,
            removeOwnerAction,
            uploadTestAction,
            changeMaterialAction,
            changeWorkflowAction,
            changeFacilityAction,
            addCommentAction,
            changeEslAction,
            changePackConfigAction,
            addBuildAction,
            changeArmarcAction,
            changeAtssAction,
            changeDiesAction,
            changeComponentsAction,
            changeSymbolizationAction,
            addFlowOperationAction,
            removeFlowOperationAction,
            addFlowComponentAction,
            removeFlowComponentAction,
            changeSelectionAction,
            pgsSandboxService,
            changeTurnkeyAction,
            changeChangelinkChangeAction,
            changeChangelinkPcnAction,
            changeFlowOperationAction,
            changeFlowComponentAction,
            changePkgNicheAction,
            canApproveAction,
            compareAtssAction,
            toggleOperationValidateAction,
            compareTravelerAction,
            dashBoardService,
            changeDryBakeAction,
            facilityService,
            deviceService,
            refreshPgsAction,
            refreshBomTemplateAction,
            refreshFlowAction,
            rejectReasonsConfigService,
            reviewBomTemplateAction,
            fillComponentVyperAction,
            fillComponentAtssAction,
            componentMapService,
            vyperCopyBuildAction,
            changeBackgrindAction,
            fillComponentClearAction,
            addBuildCopyAction,
            changeOperationCommentAction,
            atssMaterialService,
            atssFacilityService,
            changeWaferSawMethodAction,
            packageNicheService,
            createVyperAndCopyBuildAction,
            childService,
            buildService,
            buildEntityService,
            downloadVyperExcelAction,
            restoreFlowOperationAction,
            updateBuildAction,
            praBuildReportService,
            approveDiagramAction,
            deviceReportRepository,
            userUtilsService,
            reviewPkgNicheBomTemplateAction,
            listPackConfigAction,
            checkArmarcAction,
            refreshAttributesAction,
    )

    Vyper vyper1 = new Vyper()
    Build build1 = new Build()

    def setup() {
        0 * _
    }

    def "changeTitle delegates to the service"() {

        def changeTitleForm1 = new ChangeTitleForm(
                vyperNumber: "VYPER1234567",
                title: "THE_TITLE")
        def vyper1 = new Vyper()

        when:
        def ret = controller.changeTitle(changeTitleForm1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeTitleAction.execute(changeTitleForm1) >> vyper1

        and:
        ret.statusCode == HttpStatus.OK
    }

    def "changeDescription delegates to the service"() {

        def changeDescriptionForm1 = new ChangeDescriptionForm(
                vyperNumber: "VYPER1234567",
                description: "THE_DESCRIPTION")

        when:
        def ret = controller.changeDescription(changeDescriptionForm1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeDescriptionAction.execute(changeDescriptionForm1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "material delegates to the action"() {

        def changeMaterialForm1 = new ChangeMaterialForm(
                vyperNumber: "VYPER1234567",
                buildNumber: "VYPER1234567-1234",
                material: "THE_MATERIAL"
        )
        when:
        def ret = controller.material(changeMaterialForm1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeMaterialAction.execute(changeMaterialForm1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "upload delegates to the action"() {

        def uploadTestForm1 = new UploadTestForm(
                vyperNumber: "VYPER1234567",
                buildNumber: "VYPER1234567-1234",
                content: "THE_CONTENT"
        )
        when:
        def ret = controller.testUpload(uploadTestForm1)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * uploadTestAction.execute(uploadTestForm1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "download (build not found) throws BuildNotFoundException"() {

        HttpServletResponse response = Mock(HttpServletResponse)

        when:
        controller.testDownload("VYPER1234567", "VYPER1234567-1234", response)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * buildService.fetchBuild("VYPER1234567-1234") >> { throw new BuildNotFoundException("") }

        and:
        thrown BuildNotFoundException
    }

    def "download sets the headers and content of the response"() {

        HttpServletResponse response = Mock(HttpServletResponse)
        Build build1 = new Build(buildNumber: "VYPER1234567-1234")
        build1.test.content = "THE_CONTENT"

        ServletOutputStream sos = Mock(ServletOutputStream)

        when:
        controller.testDownload("VYPER1234567", "VYPER1234567-1234", response)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * buildService.fetchBuild("VYPER1234567-1234") >> build1
        1 * response.setHeader('Content-disposition', 'attachment; filename="VYPER1234567-1234 - test flow.txt";')
        1 * response.setContentType('text/plain')
        _ * response.getOutputStream() >> sos
        1 * sos.print("THE_CONTENT")
    }

    def "workflow delegates to the action"() {
        ChangeWorkflowForm form1 = new ChangeWorkflowForm(buildNumber: "VYPER1234567-1234")

        when:
        def ret = controller.workflow(form1)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * changeWorkflowAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeFacility delegates to the action"() {
        ChangeFacilityForm form1 = new ChangeFacilityForm()

        when:
        def ret = controller.changeFacility(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeFacilityAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "comment delegates to the action"() {
        AddCommentForm form1 = new AddCommentForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.comment(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * addCommentAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "esl delegates to the action"() {
        ChangeEslForm form1 = new ChangeEslForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.esl(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeEslAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "listPackConfigs delegates to the action"() {
        def form1 = new BuildNumberForm(buildNumber: "VYPER0000000-0000")

        when:
        def ret = controller.listPackConfigs(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VYPER0000000-0000")
        1 * listPackConfigAction.execute(form1) >> ["VALUE1", "VALUE2"]

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == ["VALUE1", "VALUE2"]
    }

    def "packConfig delegates to the action"() {
        ChangePackConfigForm form1 = new ChangePackConfigForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.packConfig(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changePackConfigAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "dryBake delegates to the action"() {
        ChangeDryBakeForm form1 = new ChangeDryBakeForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.dryBake(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeDryBakeAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "turnkey delegates to the action"() {
        ChangeTurnkeyForm form1 = new ChangeTurnkeyForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.turnkey(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeTurnkeyAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "addBuild delegates to the action"() {
        AddBuildForm form1 = new AddBuildForm()
        Build build1 = new Build()

        when:
        def ret = controller.addBuild(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * addBuildAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1

    }

    def "changeArmarc delegates to the action"() {
        ChangeArmArcForm form1 = new ChangeArmArcForm()

        when:
        def ret = controller.changeArmarc(form1)

        then:
        1 * changeArmarcAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeAtss delegates to the action"() {
        ChangeAtssForm form1 = new ChangeAtssForm()

        when:
        def ret = controller.changeAtss(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeAtssAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeDies delegates to the action"() {
        ChangeDiesForm form1 = new ChangeDiesForm()

        when:
        def ret = controller.changeDies(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeDiesAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeComponents delegates to the action"() {
        ChangeComponentsForm form1 = new ChangeComponentsForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changeComponents(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeComponentsAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeSymbolization delegates to the action"() {
        ChangeSymbolizationForm form1 = new ChangeSymbolizationForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changeSymbolization(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeSymbolizationAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "addOperation delegates to the action"() {
        def form1 = new AddFlowOperationForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.addOperation(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * addFlowOperationAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "removeOperation delegates to the action"() {
        def form1 = new RemoveFlowOperationForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.removeOperation(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * removeFlowOperationAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "addComponent delegates to the action"() {
        def form1 = new AddFlowComponentForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.addComponent(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * addFlowComponentAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "removeComponent delegates to the action"() {
        def form1 = new RemoveFlowComponentForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.removeComponent(form1)

        then:
        1 * removeFlowComponentAction.execute(form1) >> build1
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeSelection delegates to the action"() {
        def form1 = new ChangeSelectionForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changeSelection(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeSelectionAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changelinkChange delegates to the action"() {
        def form1 = new ChangeChangeLinkChangeForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changelinkChange(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeChangelinkChangeAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changelinkPcn delegates to the action"() {
        def form1 = new ChangeChangeLinkPcnForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changelinkPcn(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeChangelinkPcnAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeFlowOperation delegates to the action"() {
        def form1 = new ChangeFlowOperationForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changeOperation(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeFlowOperationAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeComponent delegates to the action"() {
        def form1 = new ChangeFlowComponentForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.changeComponent(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeFlowComponentAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "pkgNiche delegates to the action"() {
        def form1 = new ChangePkgNicheForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.pkgNiche(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changePkgNicheAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "canApprove delegates to the action"() {
        def form1 = new CanApproveForm(buildNumber: "VBUILD1234567-1234")
        def result1 = new CanApproveResult()

        when:
        def ret = controller.canApprove(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * canApproveAction.execute(form1) >> result1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == result1
    }

    def "atApproveCallBack parses json and delegates to the action"() {

        HashMap<String, Object> json = new HashMap<>()
        json.put("taskName", "VBUILD1234567-1234")
        json.put("stateName", "APPROVED")

        when:
        def ret = controller.atApproveCallBack(json)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * changeWorkflowAction.atApproverCallBack("VBUILD1234567-1234", "APPROVED") >> vyper1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == vyper1
    }

    def "compare atss delegates to the action"() {
        def form1 = new CompareAtssForm(facility: "THE_FACILITY", buildNumber: "THE_BUILDNUMBER")
        def result1 = new CompareTravelerResult()

        when:
        def ret = controller.compareAtss(form1)

        then:
        1 * userUtilsService.validateUserByBuild("THE_BUILDNUMBER")
        1 * userUtilsService.validateUserByFacility("THE_FACILITY")
        1 * compareAtssAction.execute(form1) >> result1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == result1
    }

    def "compareTraveler delegates to the action"() {
        def form1 = new CompareTravelerForm(buildNumber1: "VBUILD1234567-1111", buildNumber2: "VBUILD1234567-2222")
        def result1 = new CompareTravelerResult()

        when:
        def ret = controller.compareTraveler(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1111")
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-2222")
        1 * compareTravelerAction.execute(form1) >> result1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == result1
    }

    def "operationValidate delegates to the action"() {
        def form1 = new ToggleOperationValidateForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.operationValidate(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * toggleOperationValidateAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "atssFacilities delegates to the service"() {
        when:
        def ret = controller.atssFacilities()

        then:
        1 * facilityService.list() >> ["ABC"]

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == ["ABC"]
    }

    def "projectDevice delegates to the service"() {

        def pageable1 = Mock(Pageable)
        def filter1 = "material|contains|abc"
        def page1 = Mock(Page)
        def smartResult1 = new SmartResult(page: page1)

        when:
        def ret = controller.projectDevice(pageable1, [filter1])

        then:
        1 * deviceService.search(pageable1, [filter1]) >> smartResult1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == page1
    }

    def "RefreshPgsForm delegates to the action"() {
        def form1 = new RefreshPgsForm()

        when:
        def ret = controller.refreshPgs(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * refreshPgsAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "refreshBomTemplate delegates to the action"() {
        def form1 = new RefreshBomTemplateForm()

        when:
        def ret = controller.refreshBomTemplate(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * refreshBomTemplateAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "searchReasons delegates to the action"() {
        def r1 = ''

        when:
        def ret = controller.searchReasons("THE_GROUP")

        then:
        1 * rejectReasonsConfigService.searchReasons("THE_GROUP") >> [r1]

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == [r1]
    }

    def "reviewBomTemplate delegates to the action"() {
        def form1 = new ReviewBomTemplateForm()

        when:
        def ret = controller.reviewBomTemplate(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * reviewBomTemplateAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "fillComponentVyper delegates to the action"() {
        def form1 = new FillComponentVyperForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.fillComponentVyper(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * fillComponentVyperAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "fillComponentAtss delegates to the action"() {
        def form1 = new FillComponentAtssForm(buildNumber: "VBUILD1234567-1234", facility: "THE_FACILITY")

        when:
        def ret = controller.fillComponentAtss(form1)

        then:
        1 * userUtilsService.validateUserByFacility("THE_FACILITY")
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * fillComponentAtssAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "vyperCopyBuild delegates to the action"() {
        def form1 = new VyperCopyBuildForm()

        when:
        def ret = controller.vyperCopyBuild(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * vyperCopyBuildAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "fillComponentClear delegates to the action"() {
        def form1 = new FillComponentClearForm(buildNumber: "VBUILD1234567-1234")

        when:
        def ret = controller.fillComponentClear(form1)

        then:
        1 * userUtilsService.validateUserByBuild("VBUILD1234567-1234")
        1 * fillComponentClearAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "changeOperationComment delegates to the action"() {
        def form1 = new ChangeOperationCommentForm()

        when:
        def ret = controller.changeOperationComment(form1)

        then:
        1 * userUtilsService.validateUser()
        1 * changeOperationCommentAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "attributes delegates to the action"() {
        def componentMap1 = new ComponentMap(atssComponentName: "ATSS_NAME")
        def item1 = [[a: 1]]

        when:
        def ret = controller.attributes("THE_FACILITY", "THE_NAME", ["THE_VALUE"])

        then:
        1 * userUtilsService.validateUserByFacility('THE_FACILITY')
        1 * componentMapService.findByName("THE_NAME") >> componentMap1
        1 * attributeService.findByName("THE_FACILITY", "ATSS_NAME", ["THE_VALUE"]) >> item1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == item1
    }

    def "attributes delegates to the action: missing component map"() {
        def item1 = [[a: 1]]

        when:
        def ret = controller.attributes("THE_FACILITY", "THE_NAME", ["THE_VALUE"])

        then:
        1 * userUtilsService.validateUserByFacility('THE_FACILITY')
        1 * componentMapService.findByName("THE_NAME") >> null
        1 * attributeService.findByName("THE_FACILITY", "THE_NAME", ["THE_VALUE"]) >> item1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == item1
    }

    def "child delegates to the action"() {
        def componentMap1 = new ComponentMap(atssComponentName: "ATSS_NAME")
        def child1 = Child.builder().build()

        when:
        def ret = controller.child("THE_FACILITY", "THE_NAME", ["THE_VALUE"])

        then:
        1 * userUtilsService.validateUserByFacility('THE_FACILITY')
        1 * componentMapService.findByName("THE_NAME") >> componentMap1
        1 * childService.findByName("THE_FACILITY", "ATSS_NAME", ["THE_VALUE"]) >> child1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == child1
    }

    def "child delegates to the action: missing component map"() {
        def child1 = Child.builder().build()

        when:
        def ret = controller.child("THE_FACILITY", "THE_NAME", ["THE_VALUE"])

        then:
        1 * userUtilsService.validateUserByFacility('THE_FACILITY')
        1 * componentMapService.findByName("THE_NAME") >> null
        1 * childService.findByName("THE_FACILITY", "THE_NAME", ["THE_VALUE"]) >> child1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == child1
    }

    def "waferSawMethod delegates to the action"() {

        def changeWaferSawMethodForm1 = new ChangeWaferSawMethodForm(buildNumber: "VYPER1234567-1234")

        when:
        def ret = controller.waferSawMethod(changeWaferSawMethodForm1)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * changeWaferSawMethodAction.execute(changeWaferSawMethodForm1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "download Vyper Excel calls action"() {
        HttpServletResponse response = Mock(HttpServletResponse)
        ServletOutputStream servletOut = Mock(ServletOutputStream)
        when:
        controller.downloadVyperExcel("VYPER1234567", response)

        then:
        1 * downloadVyperExcelAction.execute("VYPER1234567", _)
        1 * response.setHeader("Content-disposition", "attachment; filename=\"VYPER1234567.xlsx\";")
        1 * response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        1 * response.getOutputStream() >> servletOut
        1 * servletOut.write(_)

    }

    def "restore operation delegates to the action"() {
        def restoreFlowOperationForm = new RestoreFlowOperationForm(buildNumber: "VYPER1234567-1234")

        when:
        def ret = controller.restoreOperation(restoreFlowOperationForm)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * restoreFlowOperationAction.execute(restoreFlowOperationForm) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "praReport delegates to the action"() {
        Page<PraBuildReportEntity> page = Page.empty()
        SmartResult result = Mock(SmartResult)

        when:
        def ret = controller.praReport(Pageable.ofSize(10), [])

        then:
        1 * praBuildReportService.search(_, _) >> result
        1 * result.getPage() >> page

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == page
    }

    def "diagramApprove delegates to the diagramAction"() {
        DiagramApproveForm diagramApproveForm = new DiagramApproveForm(buildNumber: "VYPER1234567-1234")

        when:
        def ret = controller.diagramApprove(diagramApproveForm)

        then:
        1 * userUtilsService.validateUserByBuild('VYPER1234567-1234')
        1 * approveDiagramAction.execute(diagramApproveForm) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

    def "checkArmarc delegates to the action"() {
        CheckArmarcForm form1 = new CheckArmarcForm(buildNumber: 'VBUILD1111111-1111')

        when:
        def ret = controller.checkArmarc(form1)

        then:
        1 * userUtilsService.validateUserByBuild('VBUILD1111111-1111')
        1 * checkArmarcAction.execute(form1) >> build1

        and:
        ret.statusCode == HttpStatus.OK
        ret.body == build1
    }

}
