<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.dashboard.BatchProcessing.Repository.SCSWRDao">
    <!-- This is used to pull a single page of filtered/sorted data -->
    <select id="getColumns" parameterType="map"
            resultType="org.apache.commons.collections.map.CaseInsensitiveMap">
        select column_name, data_type from redbull_requests_col
    </select>
    
    <sql id="selectSwrStat">
        select DISTINCT r.swr_id, r.current_status, r.title, r.device_name, rr.uploaded_by from requests r,
            ${tableName} rr where r.swr_id = rr.swr_id
        <if test="swrStat != null">
            AND rr.CURRENT_STATUS IN (${swrStat})
        </if>
        <if test="submitSwrStat != null">
            AND ${submitSwrStat}
        </if>
    </sql>
    
    <sql id="selectRequestRedbull">
        select SWR_ID,
        CURRENT_STATUS,
        TITLE,
        ATSITE,
        SBE1SITE,
        CHARGE,
        PDBFACILITY,
        To_char(NEED_DATE, 'MM/DD/YYYY') NEED_DATE,
        PRIORITY,
        COPY_EMAIL,
        SHIP_TYPE,
        GEC_QTY,
        ASYREQ,
        DIE_LOT,
        DEVICE_NAME,
        PTSS_TRAVEL,
        DIE_NAME,
        PARENT_DEVICE,
        PIN,
        PKG,
        DIE_REV,
        BLD_QTY,
        WAFER_USE,
        DIE_DESIGNATOR,
        DIE_SIZE,
        FAB_CODE,
        FABTECH,
        WAFER_DIAMETER,
        WAFER_THICKNESS,
        THICKUM,
        BACKGRIND,
        BACKGUM,
        WIRE_DIAMETER,
        WAFER_NUM,
        PROBED,
        INKLESS,
        INKLESS_METHOD,
        MAP_AVAIL,
        MAP_LOC,
        BIN_NUM,
        DUALDIE,
        DDIENAME,
        DDIEREV,
        DDIEDSG,
        DDIESIZE,
        DDFABCODE,
        TRAVEL_STAT,
        LEADFRAMES1,
        MOLD_COMPOUND1,
        MOUNT_COMPOUND1,
        WIRE,
        BASE_OUTLINE1,
        BP_METAL1,
        HEADER1,
        LID1,
        MB_FILE_LOC,
        MB_LOC,
        USE_GEC,
        CHG_BOND,
        CHG_DIEDEV,
        CHG_DIEREV,
        CHG_LF,
        CHG_MOLD,
        CHG_MOUNT,
        CHG_OTHER,
        CHG_SAW,
        CHG_OTHER_DESC,
        SYMBOL_SPEC,
        SYMTYPE,
        TESTREQ,
        TEST_SYSTEM,
        TEST_TEMP,
        TEST_PROG,
        LIBRARY,
        OTHER_TEST,
        BURNIN,
        REPORT,
        TEST_LIB,
        BRDAVAIL,
        CORUAVAIL,
        PGMAVAIL,
        PACKOPT,
        STICKERTYPE,
        SOF,
        DISPMTL,
        DISPWAF,
        HOW_SHIP_REM,
        RETPACKPC,
        SALEABLE,
        VISIBLE_DQ,
        ECCNREQ,
        COMMENT_PURPOSE,
        COMMENT_REQ,
        COMMENT_WAF,
        COMMENT_ASY,
        COMMENT_TST,
        COMMENT_PAK,
        COMMENT_FINAL,
        DDIE_LOT,
        DWAFER_DIAMETER,
        DWAFER_THICKNESS,
        DSAP_LOT,
        SOLDER,
        CHIPCAP,
        BOAC,
        CHG_FAB,
        COMMENT_MQ,
        SBEWAYBILL,
        PO,
        DFABTECH,
        DBACKGRIND,
        DWAFER_USE,
        DWAFER_NUM,
        DBACKUM,
        DTHICKUM,
        DBOAC,
        PROBED2,
        INKLESS2,
        INKLESS_METHOD2,
        MAP_AVAIL2,
        MAP_LOC2,
        USE_GEC2,
        RMR_RETEST,
        PB_FREE,
        SCRIBEWIDTH,
        DSCRIBEWIDTH,
        SCRIBEUM,
        DSCRIBEUM,
        RELTESTS,
        COMMENT_REL,
        BUMPREQ,
        BUMP_MASK,
        BUMP_MATL,
        BUMP_REPASS,
        BUMP_GP_MAP,
        COMMENT_BUMP,
        NEW_PGM,
        NEW_HW,
        CHK_ASSYMQ,
        CHK_TESTMQ,
        CHK_PRODQUAL,
        MARKET_CATEGORY,
        SWR_TYPE,
        DTALOGAVAIL,
        CORNER_LOT,
        LINE_ITEM,
        RTP2_SUBMITTED,
        RTP_BASESET,
        TEST_CONFIG,
        PACK_REQ,
        PACK_REQ_PN,
        PACK_REQ_PN_TAPE,
        PACK_REQ_PN_COVER,
        UPLOAD_DTTM,
        UPLOADED_BY
      from 
        requests_redbull
      where (swr_id, upload_dttm, uploaded_by) in (
        select swr_id, max(upload_dttm) upload_dttm, uploaded_by
        from requests_redbull
        where uploaded_by = #{userId}
          and swr_id = #{swrId}
        group by swr_id, uploaded_by
      )
    </sql>

    <sql id="selectRequest">
        select to_char(SWR_ID) SWR_ID,
        CURRENT_STATUS,
        TITLE,
        ATSITE,
        SBE1SITE,
        CHARGE,
        PDBFACILITY,
        <!--to_char(NEED_DATE, 'MM/DD/YYYY') NEED_DATE,-->
        to_char(NVL(UPDATED, TARGET), 'MM/DD/YYYY') NEED_DATE,
        PRIORITY,
        COPY_EMAIL,
        SHIP_TYPE,
        GEC_QTY,
        ASYREQ,
        DIE_LOT,
        DEVICE_NAME,
        PTSS_TRAVEL,
        DIE_NAME,
        PARENT_DEVICE,
        PIN,
        PKG,
        DIE_REV,
        BLD_QTY,
        WAFER_USE,
        DIE_DESIGNATOR,
        DIE_SIZE,
        FAB_CODE,
        FABTECH,
        WAFER_DIAMETER,
        WAFER_THICKNESS,
        THICKUM,
        BACKGRIND,
        BACKGUM,
        WIRE_DIAMETER,
        WAFER_NUM,
        PROBED,
        INKLESS,
        INKLESS_METHOD,
        MAP_AVAIL,
        MAP_LOC,
        BIN_NUM,
        DUALDIE,
        DDIENAME,
        DDIEREV,
        DDIEDSG,
        DDIESIZE,
        DDFABCODE,
        TRAVEL_STAT,
        LEADFRAMES1,
        MOLD_COMPOUND1,
        MOUNT_COMPOUND1,
        WIRE,
        BASE_OUTLINE1,
        BP_METAL1,
        HEADER1,
        LID1,
        MB_FILE_LOC,
        MB_LOC,
        USE_GEC,
        CHG_BOND,
        CHG_DIEDEV,
        CHG_DIEREV,
        CHG_LF,
        CHG_MOLD,
        CHG_MOUNT,
        CHG_OTHER,
        CHG_SAW,
        CHG_OTHER_DESC,
        SYMBOL_SPEC,
        SYMTYPE,
        TESTREQ,
        TEST_SYSTEM,
        TEST_TEMP,
        TEST_PROG,
        LIBRARY,
        OTHER_TEST,
        BURNIN,
        REPORT,
        TEST_LIB,
        BRDAVAIL,
        CORUAVAIL,
        PGMAVAIL,
        PACKOPT,
        STICKERTYPE,
        SOF,
        DISPMTL,
        DISPWAF,
        HOW_SHIP_REM,
        RETPACKPC,
        SALEABLE,
        VISIBLE_DQ,
        ECCNREQ,
        COMMENT_PURPOSE,
        COMMENT_REQ,
        COMMENT_WAF,
        COMMENT_ASY,
        COMMENT_TST,
        COMMENT_PAK,
        COMMENT_FINAL,
        DDIE_LOT,
        DWAFER_DIAMETER,
        DWAFER_THICKNESS,
        DSAP_LOT,
        SOLDER,
        CHIPCAP,
        BOAC,
        CHG_FAB,
        COMMENT_MQ,
        SBEWAYBILL,
        PO,
        DFABTECH,
        DBACKGRIND,
        DWAFER_USE,
        DWAFER_NUM,
        DBACKUM,
        DTHICKUM,
        DBOAC,
        PROBED2,
        INKLESS2,
        INKLESS_METHOD2,
        MAP_AVAIL2,
        MAP_LOC2,
        USE_GEC2,
        RMR_RETEST,
        PB_FREE,
        SCRIBEWIDTH,
        DSCRIBEWIDTH,
        SCRIBEUM,
        DSCRIBEUM,
        RELTESTS,
        COMMENT_REL,
        BUMPREQ,
        BUMP_MASK,
        BUMP_MATL,
        BUMP_REPASS,
        BUMP_GP_MAP,
        COMMENT_BUMP,
        NEW_PGM,
        NEW_HW,
        CHK_ASSYMQ,
        CHK_TESTMQ,
        CHK_PRODQUAL,
        MARKET_CATEGORY,
        SWR_TYPE,
        DTALOGAVAIL,
        CORNER_LOT,
        LINE_ITEM,
        RTP2_SUBMITTED,
        RTP_BASESET,
        TEST_CONFIG,
        REQUESTOR_EMPID,
        PACK_REQ,
        PACK_REQ_PN,
        PACK_REQ_PN_TAPE,
        PACK_REQ_PN_COVER
      from requests
    </sql>

    <select id="getRequest" parameterType="String" resultType="java.util.LinkedHashMap">
      <include refid="selectRequest"/>  left join
        (
          select
            swrid, updated, target
          from forecast_dates
          where status = 'SBE_Shipped_Paperwork'
            and swrid = #{swrId}
        ) fd
        on (requests.swr_id = fd.swrid)
       where swr_id = #{swrId}
    </select>
    
    <select id="getRequests" parameterType="String" resultType="java.util.LinkedHashMap">
      <include refid="selectRequest"/>,
        (select swrid, updated, target from forecast_dates where status = 'SBE_Shipped_Paperwork'
        and swrid in (${swrIds})) fd
         where (swr_id in (${swrIds})
        AND requests.swr_id = fd.swrid(+)
        <if test="siteExt != null and siteExt.size > 0" >
            and (
            <foreach collection="siteExt" item="item" index="key" separator=" or ">
                <foreach collection="item" item="value" index="idx" >
                    ${idx} = #{value}
                </foreach>
            </foreach>
            )
        </if>
        )
    </select>
    
    <select id="getRequestRedbull" resultType="java.util.LinkedHashMap">
        <include refid="selectRequestRedbull"/>
    </select>
    
    <select id="getATSites" resultType="String">
        select distinct at_site from scswr.list_box 
        where at_site is not null order by at_site
    </select>
    
    <select id="validateSwrId" parameterType="String" resultType="int">
        select count(*) from requests where swr_id = #{swrId}
    </select>

    <select id="getDropdownValuesFromSql" parameterType="String" resultType="String">
    SELECT *
    FROM (
      ${sqlValueSource}
    )
    </select>
    
    <select id="getSBE" resultType="String">
        select distinct key from scswr.list_box
        where key is not null 
        order by key
    </select>
    
    <select id="getSBE1" resultType="String">
        select distinct sbe1 from scswr.list_box
        where sbe1 is not null 
        order by sbe1
    </select>
    
    <select id="getSwrType" resultType="String">
        select distinct swr_type_desc from scswr.swr_type
        where swr_type_desc is not null 
    </select>
    
    <select id="getUserSecurity" resultType="java.util.LinkedHashMap">
        select site_ext, sitecode_ovr, site_type_ovr, admin_level from security where empid = #{userId}
    </select>
    
    <select id="requestsColumns" resultType="java.util.LinkedHashMap">
        select column_name, label from scswr_columns
        order by label
    </select>
    
    <select id="confirmAtSite" resultType="int">
        select count(1) from list_box where at_site = #{atSite}
    </select>
    
    <select id="confirmSbeSite" resultType="int">
        select count(1) from list_box where sbe1 = #{sbe1}
    </select>
    
    <select id="getMarketCat" resultType="String">
        select market_cat_desc from scswr.market_category
    </select>
    
    <insert id="createRequestsHistoryRecordByRequestRecord">
        insert into scswr.requests_history (SWR_ID,
        CURRENT_STATUS,
        TITLE,
        ATSITE,
        SBE1SITE,
        SITELOCATION,
        ATCAT,
        ATPE,
        PROFCNTR,
        COST_CENTER,
        CHARGE,
        PDBFACILITY,
        REQUESTOR_EMPID,
        REQUESTOR_EMAIL,
        REQUESTOR_NAME,
        REQUESTOR_PHONE,
        REQUEST_DATE,
        NEED_DATE,
        PRIORITY,
        COMPLEXITY,
        COPY_EMAIL,
        SHIP_TYPE,
        GEC_QTY,
        SBEINVOICE,
        SBEDELNOTE,
        ASYREQ,
        DIE_LOT,
        GENERIC_DEVICE,
        DEVICE_NAME,
        GENERIC_DIE,
        DIE_NAME,
        PARENT_DEVICE,
        PIN,
        PKG,
        DIE_REV,
        BLD_QTY,
        WAFER_USE,
        DIE_DESIGNATOR,
        DIE_SIZE,
        FAB_CODE,
        FABTECH,
        WAFER_DIAMETER,
        WAFER_THICKNESS,
        THICKUM,
        BACKGRIND,
        BACKGUM,
        WIRE_DIAMETER,
        WAFER_NUM,
        PROBED,
        INKLESS,
        INKLESS_METHOD,
        MAP_AVAIL,
        MAP_LOC,
        BIN_NUM,
        DUALDIE,
        DDIENAME,
        DDIEREV,
        DDIEDSG,
        DDIESIZE,
        DDFABCODE,
        PTSS_TRAVEL,
        TRAVEL_STAT,
        LEADFRAMES1,
        MOLD_COMPOUND1,
        MOUNT_COMPOUND1,
        BASE_OUTLINE1,
        BP_METAL1,
        HEADER1,
        LID1,
        MB_DIAG1,
        DATA_FORMAT,
        ZIP,
        ARC_FILE_LOC,
        ARC_LOC,
        MB_FILE_LOC,
        MB_LOC,
        USE_GEC,
        CHG_BOND,
        CHG_DIEDEV,
        CHG_DIEREV,
        CHG_LF,
        CHG_MOLD,
        CHG_MOUNT,
        CHG_OTHER,
        CHG_SAW,
        CHG_OTHER_DESC,
        SYMBOL_SPEC,
        SYMTYPE,
        TESTREQ,
        TEST_SYSTEM,
        TEST_TEMP,
        TEST_PROG,
        LIBRARY,
        OTHER_TEST,
        BURNIN,
        REPORT,
        TEST_LIB,
        BRDAVAIL,
        CORUAVAIL,
        PGMAVAIL,
        PACKOPT,
        STICKER,
        STICKERTYPE,
        SOF,
        DISPMTL,
        DISPWAF,
        HOW_SHIP_REM,
        RETPACKPC,
        SALEABLE,
        VISIBLE_DQ,
        ECCNREQ,
        ECCNCODE,
        MQUPLOAD,
        MQSTATUS,
        BATCH,
        ITEM_ID,
        AT_ESTSTART_DATE,
        AT_ESTFINISH_DATE,
        MAILSTN,
        CHK_MMSETUP,
        CHK_PTSSREQ,
        CHK_EPOXY,
        CHK_LEADFRAME,
        CHK_GOLDWIRE,
        CHK_MOLDCOMP,
        CHK_INKLESS,
        CHK_BACKGRD,
        CHK_WAFERSPEC,
        CHK_SYMBOLOK,
        CHK_TESTPRG,
        CHK_TESTPER,
        CHK_SPECTEST,
        CHK_ANYSPEC,
        CHK_SUBCONASY,
        MTLONSITE,
        SHIPCOMPLETE,
        SYMBOL_FORMAT,
        COMMENT_PURPOSE,
        COMMENT_REQ,
        COMMENT_WAF,
        COMMENT_ASY,
        COMMENT_TST,
        COMMENT_PAK,
        COMMENT_FINAL,
        COMMENT_SBESHIP,
        COMMENT_ATACCEPT,
        COMMENT_ATSTART,
        COMMENT_ATSHIP,
        COMMENT_SBERCV,
        COMMENT_REQFNL,
        DATESHIP,
        CHK_MBDIAG,
        DDIE_LOT,
        DWAFER_DIAMETER,
        DWAFER_THICKNESS,
        DSAP_LOT,
        SAP_LOT,
        COMMENT_SIGNOFF,
        SOLDER,
        CHIPCAP,
        BOAC,
        CHG_FAB,
        COMMENT_MQ,
        PACKDIM,
        PACKCLASS,
        SBEWAYBILL,
        PO,
        DFABTECH,
        DBACKGRIND,
        DWAFER_USE,
        DWAFER_NUM,
        DBACKUM,
        DTHICKUM,
        TRIPLEDIE,
        TDIENAME,
        TDIEREV,
        TDIESIZE,
        TBOAC,
        TDIEDSG,
        TDFABCODE,
        TFABTECH,
        TDIE_LOT,
        TWAFER_DIAMETER,
        TWAFER_THICKNESS,
        TBACKGRIND,
        TWAFER_USE,
        TBACKUM,
        TTHICKUM,
        TWAFER_NUM,
        DBOAC,
        MECHSAMPLE,
        PROBED2,
        PROBED3,
        INKLESS2,
        INKLESS3,
        INKLESS_METHOD2,
        INKLESS_METHOD3,
        MAP_AVAIL2,
        MAP_AVAIL3,
        MAP_LOC2,
        MAP_LOC3,
        BIN_NUM2,
        BIN_NUM3,
        USE_GEC2,
        USE_GEC3,
        RMR_RETEST,
        PB_FREE,
        SCRIBEWIDTH,
        DSCRIBEWIDTH,
        TSCRIBEWIDTH,
        SCRIBEUM,
        DSCRIBEUM,
        TSCRIBEUM,
        SAP_2D_SALEABLE,
        SAP_2D_VISIBLEDQ,
        MQTESTS,
        RELTESTS,
        COMMENT_REL,
        BUMPREQ,
        BUMP_MASK,
        BUMP_MATL,
        BUMP_REPASS,
        BUMP_GP_MAP,
        BACKSIDE_COATING_REQ,
        BACKSIDE_COATING_THICK,
        BACKSIDE_COATING_THICKUM,
        COMMENT_BUMP,
        NEW_PGM,
        NEW_HW,
        CHK_ASSYMQ,
        CHK_TESTMQ,
        CHK_PRODQUAL,
        MARKET_CATEGORY,
        SWR_TYPE,
        DTALOGAVAIL,
        REASON_CODE,
        TESTNA,
        TESTNANEW,
        MQ_REPORT_STATUS,
        CORNER_LOT,
        LINE_ITEM,
        RTP2_SUBMITTED,
        RTP_BASESET,
        CC_MGR_APPROVAL,
        ARM_ID,
        BIN_NUM4,
        INKLESS_METHOD4,
        INKLESS4,
        MAP_AVAIL4,
        MAP_LOC4,
        PROBED4,
        FOBACKGRIND,
        FOBOAC,
        FODFABCODE,
        FODIE_LOT,
        FODIEDSG,
        FODIENAME,
        FODIEREV,
        FODIESIZE,
        FOFABTECH,
        QUADDIE,
        FOSCRIBEWIDTH,
        FOWAFER_DIAMETER,
        FOWAFER_NUM,
        FOWAFER_THICKNESS,
        FOWAFER_USE,
        USE_GEC4,
        BIN_NUM5,
        INKLESS_METHOD5,
        INKLESS5,
        MAP_AVAIL5,
        MAP_LOC5,
        PROBED5,
        FIBACKGRIND,
        FIBOAC,
        FIDFABCODE,
        FIDIE_LOT,
        FIDIEDSG,
        FIDIENAME,
        FIDIEREV,
        FIDIESIZE,
        FIFABTECH,
        QUINTDIE,
        FISCRIBEWIDTH,
        FIWAFER_DIAMETER,
        FIWAFER_NUM,
        FIWAFER_THICKNESS,
        FIWAFER_USE,
        USE_GEC5,
        FITHICKUM,
        FISCRIBEUM,
        FIBACKUM,
        FOTHICKUM,
        FOSCRIBEUM,
        FOBACKUM,
        IS_APL,
        IS_MCM,
        ISO_VALUE,
        WIRE,
        TEST_CONFIG,
        PACK_REQ,
        PACK_REQ_PN,
        PACK_REQ_PN_TAPE,
        PACK_REQ_PN_COVER,
        CHANGE_DATE)
        select SWR_ID,
        CURRENT_STATUS,
        TITLE,
        ATSITE,
        SBE1SITE,
        SITELOCATION,
        ATCAT,
        ATPE,
        PROFCNTR,
        COST_CENTER,
        CHARGE,
        PDBFACILITY,
        REQUESTOR_EMPID,
        REQUESTOR_EMAIL,
        REQUESTOR_NAME,
        REQUESTOR_PHONE,
        REQUEST_DATE,
        NEED_DATE,
        PRIORITY,
        COMPLEXITY,
        COPY_EMAIL,
        SHIP_TYPE,
        GEC_QTY,
        SBEINVOICE,
        SBEDELNOTE,
        ASYREQ,
        DIE_LOT,
        GENERIC_DEVICE,
        DEVICE_NAME,
        GENERIC_DIE,
        DIE_NAME,
        PARENT_DEVICE,
        PIN,
        PKG,
        DIE_REV,
        BLD_QTY,
        WAFER_USE,
        DIE_DESIGNATOR,
        DIE_SIZE,
        FAB_CODE,
        FABTECH,
        WAFER_DIAMETER,
        WAFER_THICKNESS,
        THICKUM,
        BACKGRIND,
        BACKGUM,
        WIRE_DIAMETER,
        WAFER_NUM,
        PROBED,
        INKLESS,
        INKLESS_METHOD,
        MAP_AVAIL,
        MAP_LOC,
        BIN_NUM,
        DUALDIE,
        DDIENAME,
        DDIEREV,
        DDIEDSG,
        DDIESIZE,
        DDFABCODE,
        PTSS_TRAVEL,
        TRAVEL_STAT,
        LEADFRAMES1,
        MOLD_COMPOUND1,
        MOUNT_COMPOUND1,
        BASE_OUTLINE1,
        BP_METAL1,
        HEADER1,
        LID1,
        MB_DIAG1,
        DATA_FORMAT,
        ZIP,
        ARC_FILE_LOC,
        ARC_LOC,
        MB_FILE_LOC,
        MB_LOC,
        USE_GEC,
        CHG_BOND,
        CHG_DIEDEV,
        CHG_DIEREV,
        CHG_LF,
        CHG_MOLD,
        CHG_MOUNT,
        CHG_OTHER,
        CHG_SAW,
        CHG_OTHER_DESC,
        SYMBOL_SPEC,
        SYMTYPE,
        TESTREQ,
        TEST_SYSTEM,
        TEST_TEMP,
        TEST_PROG,
        LIBRARY,
        OTHER_TEST,
        BURNIN,
        REPORT,
        TEST_LIB,
        BRDAVAIL,
        CORUAVAIL,
        PGMAVAIL,
        PACKOPT,
        STICKER,
        STICKERTYPE,
        SOF,
        DISPMTL,
        DISPWAF,
        HOW_SHIP_REM,
        RETPACKPC,
        SALEABLE,
        VISIBLE_DQ,
        ECCNREQ,
        ECCNCODE,
        MQUPLOAD,
        MQSTATUS,
        BATCH,
        ITEM_ID,
        AT_ESTSTART_DATE,
        AT_ESTFINISH_DATE,
        MAILSTN,
        CHK_MMSETUP,
        CHK_PTSSREQ,
        CHK_EPOXY,
        CHK_LEADFRAME,
        CHK_GOLDWIRE,
        CHK_MOLDCOMP,
        CHK_INKLESS,
        CHK_BACKGRD,
        CHK_WAFERSPEC,
        CHK_SYMBOLOK,
        CHK_TESTPRG,
        CHK_TESTPER,
        CHK_SPECTEST,
        CHK_ANYSPEC,
        CHK_SUBCONASY,
        MTLONSITE,
        SHIPCOMPLETE,
        SYMBOL_FORMAT,
        COMMENT_PURPOSE,
        COMMENT_REQ,
        COMMENT_WAF,
        COMMENT_ASY,
        COMMENT_TST,
        COMMENT_PAK,
        COMMENT_FINAL,
        COMMENT_SBESHIP,
        COMMENT_ATACCEPT,
        COMMENT_ATSTART,
        COMMENT_ATSHIP,
        COMMENT_SBERCV,
        COMMENT_REQFNL,
        DATESHIP,
        CHK_MBDIAG,
        DDIE_LOT,
        DWAFER_DIAMETER,
        DWAFER_THICKNESS,
        DSAP_LOT,
        SAP_LOT,
        COMMENT_SIGNOFF,
        SOLDER,
        CHIPCAP,
        BOAC,
        CHG_FAB,
        COMMENT_MQ,
        PACKDIM,
        PACKCLASS,
        SBEWAYBILL,
        PO,
        DFABTECH,
        DBACKGRIND,
        DWAFER_USE,
        DWAFER_NUM,
        DBACKUM,
        DTHICKUM,
        TRIPLEDIE,
        TDIENAME,
        TDIEREV,
        TDIESIZE,
        TBOAC,
        TDIEDSG,
        TDFABCODE,
        TFABTECH,
        TDIE_LOT,
        TWAFER_DIAMETER,
        TWAFER_THICKNESS,
        TBACKGRIND,
        TWAFER_USE,
        TBACKUM,
        TTHICKUM,
        TWAFER_NUM,
        DBOAC,
        MECHSAMPLE,
        PROBED2,
        PROBED3,
        INKLESS2,
        INKLESS3,
        INKLESS_METHOD2,
        INKLESS_METHOD3,
        MAP_AVAIL2,
        MAP_AVAIL3,
        MAP_LOC2,
        MAP_LOC3,
        BIN_NUM2,
        BIN_NUM3,
        USE_GEC2,
        USE_GEC3,
        RMR_RETEST,
        PB_FREE,
        SCRIBEWIDTH,
        DSCRIBEWIDTH,
        TSCRIBEWIDTH,
        SCRIBEUM,
        DSCRIBEUM,
        TSCRIBEUM,
        SAP_2D_SALEABLE,
        SAP_2D_VISIBLEDQ,
        MQTESTS,
        RELTESTS,
        COMMENT_REL,
        BUMPREQ,
        BUMP_MASK,
        BUMP_MATL,
        BUMP_REPASS,
        BUMP_GP_MAP,
        BACKSIDE_COATING_REQ,
        BACKSIDE_COATING_THICK,
        BACKSIDE_COATING_THICKUM,
        COMMENT_BUMP,
        NEW_PGM,
        NEW_HW,
        CHK_ASSYMQ,
        CHK_TESTMQ,
        CHK_PRODQUAL,
        MARKET_CATEGORY,
        SWR_TYPE,
        DTALOGAVAIL,
        REASON_CODE,
        TESTNA,
        TESTNANEW,
        MQ_REPORT_STATUS,
        CORNER_LOT,
        LINE_ITEM,
        RTP2_SUBMITTED,
        RTP_BASESET,
        CC_MGR_APPROVAL,
        ARM_ID,
        BIN_NUM4,
        INKLESS_METHOD4,
        INKLESS4,
        MAP_AVAIL4,
        MAP_LOC4,
        PROBED4,
        FOBACKGRIND,
        FOBOAC,
        FODFABCODE,
        FODIE_LOT,
        FODIEDSG,
        FODIENAME,
        FODIEREV,
        FODIESIZE,
        FOFABTECH,
        QUADDIE,
        FOSCRIBEWIDTH,
        FOWAFER_DIAMETER,
        FOWAFER_NUM,
        FOWAFER_THICKNESS,
        FOWAFER_USE,
        USE_GEC4,
        BIN_NUM5,
        INKLESS_METHOD5,
        INKLESS5,
        MAP_AVAIL5,
        MAP_LOC5,
        PROBED5,
        FIBACKGRIND,
        FIBOAC,
        FIDFABCODE,
        FIDIE_LOT,
        FIDIEDSG,
        FIDIENAME,
        FIDIEREV,
        FIDIESIZE,
        FIFABTECH,
        QUINTDIE,
        FISCRIBEWIDTH,
        FIWAFER_DIAMETER,
        FIWAFER_NUM,
        FIWAFER_THICKNESS,
        FIWAFER_USE,
        USE_GEC5,
        FITHICKUM,
        FISCRIBEUM,
        FIBACKUM,
        FOTHICKUM,
        FOSCRIBEUM,
        FOBACKUM,
        IS_APL,
        IS_MCM,
        ISO_VALUE,
        WIRE,
        TEST_CONFIG,
        PACK_REQ,
        PACK_REQ_PN,
        PACK_REQ_PN_TAPE,
        PACK_REQ_PN_COVER,
        SYSDATE
      from requests where swr_id = #{swrId}
    </insert>
        
        
    <update id="mergeRequestsRedbullIntoRequestsBySwrIdAndUserId">
        MERGE into scswr.requests a
        USING (<include refid="selectRequestRedbull" />) b ON (a.SWR_ID = b.swr_id)
        WHEN MATCHED THEN
        UPDATE SET 
        a.TITLE = b.TITLE,
        a.ATSITE = b.ATSITE,
        a.SBE1SITE = b.SBE1SITE,
        a.CHARGE = b.CHARGE,
        a.PDBFACILITY = b.PDBFACILITY,
        <!--a.NEED_DATE = b.NEED_DATE,-->
        a.PRIORITY = b.PRIORITY,
        a.COPY_EMAIL = b.COPY_EMAIL,
        a.SHIP_TYPE = b.SHIP_TYPE,
        a.GEC_QTY = b.GEC_QTY,
        a.ASYREQ = b.ASYREQ,
        a.DIE_LOT = b.DIE_LOT,
        a.DEVICE_NAME = b.DEVICE_NAME,
        a.PTSS_TRAVEL = b.PTSS_TRAVEL,
        a.DIE_NAME = b.DIE_NAME,
        a.PARENT_DEVICE = b.PARENT_DEVICE,
        a.PIN = b.PIN,
        a.PKG = b.PKG,
        a.DIE_REV = b.DIE_REV,
        a.BLD_QTY = b.BLD_QTY,
        a.WAFER_USE = b.WAFER_USE,
        a.DIE_DESIGNATOR = b.DIE_DESIGNATOR,
        a.DIE_SIZE = b.DIE_SIZE,
        a.FAB_CODE = b.FAB_CODE,
        a.FABTECH = b.FABTECH,
        a.WAFER_DIAMETER = b.WAFER_DIAMETER,
        a.WAFER_THICKNESS = b.WAFER_THICKNESS,
        a.THICKUM = b.THICKUM,
        a.BACKGRIND = b.BACKGRIND,
        a.BACKGUM = b.BACKGUM,
        a.WIRE_DIAMETER = b.WIRE_DIAMETER,
        a.WAFER_NUM = b.WAFER_NUM,
        a.PROBED = b.PROBED,
        a.INKLESS = b.INKLESS,
        a.INKLESS_METHOD = b.INKLESS_METHOD,
        a.MAP_AVAIL = b.MAP_AVAIL,
        a.MAP_LOC = b.MAP_LOC,
        a.BIN_NUM = b.BIN_NUM,
        a.DUALDIE = b.DUALDIE,
        a.DDIENAME = b.DDIENAME,
        a.DDIEREV = b.DDIEREV,
        a.DDIEDSG = b.DDIEDSG,
        a.DDIESIZE = b.DDIESIZE,
        a.DDFABCODE = b.DDFABCODE,
        a.TRAVEL_STAT = b.TRAVEL_STAT,
        a.LEADFRAMES1 = b.LEADFRAMES1,
        a.MOLD_COMPOUND1 = b.MOLD_COMPOUND1,
        a.MOUNT_COMPOUND1 = b.MOUNT_COMPOUND1,
        a.WIRE = b.WIRE,
        a.BASE_OUTLINE1 = b.BASE_OUTLINE1,
        a.BP_METAL1 = b.BP_METAL1,
        a.HEADER1 = b.HEADER1,
        a.LID1 = b.LID1,
        a.MB_FILE_LOC = b.MB_FILE_LOC,
        a.MB_LOC = b.MB_LOC,
        a.USE_GEC = b.USE_GEC,
        a.CHG_BOND = b.CHG_BOND,
        a.CHG_DIEDEV = b.CHG_DIEDEV,
        a.CHG_DIEREV = b.CHG_DIEREV,
        a.CHG_LF = b.CHG_LF,
        a.CHG_MOLD = b.CHG_MOLD,
        a.CHG_MOUNT = b.CHG_MOUNT,
        a.CHG_OTHER = b.CHG_OTHER,
        a.CHG_SAW = b.CHG_SAW,
        a.CHG_OTHER_DESC = b.CHG_OTHER_DESC,
        a.SYMBOL_SPEC = b.SYMBOL_SPEC,
        a.SYMTYPE = b.SYMTYPE,
        a.TESTREQ = b.TESTREQ,
        a.TEST_SYSTEM = b.TEST_SYSTEM,
        a.TEST_TEMP = b.TEST_TEMP,
        a.TEST_PROG = b.TEST_PROG,
        a.LIBRARY = b.LIBRARY,
        a.OTHER_TEST = b.OTHER_TEST,
        a.BURNIN = b.BURNIN,
        a.REPORT = b.REPORT,
        a.TEST_LIB = b.TEST_LIB,
        a.BRDAVAIL = b.BRDAVAIL,
        a.CORUAVAIL = b.CORUAVAIL,
        a.PGMAVAIL = b.PGMAVAIL,
        a.PACKOPT = b.PACKOPT,
        a.STICKERTYPE = b.STICKERTYPE,
        a.SOF = b.SOF,
        a.DISPMTL = b.DISPMTL,
        a.DISPWAF = b.DISPWAF,
        a.HOW_SHIP_REM = b.HOW_SHIP_REM,
        a.RETPACKPC = b.RETPACKPC,
        a.SALEABLE = b.SALEABLE,
        a.VISIBLE_DQ = b.VISIBLE_DQ,
        a.ECCNREQ = b.ECCNREQ,
        a.COMMENT_PURPOSE = b.COMMENT_PURPOSE,
        a.COMMENT_REQ = b.COMMENT_REQ,
        a.COMMENT_WAF = b.COMMENT_WAF,
        a.COMMENT_ASY = b.COMMENT_ASY,
        a.COMMENT_TST = b.COMMENT_TST,
        a.COMMENT_PAK = b.COMMENT_PAK,
        a.COMMENT_FINAL = b.COMMENT_FINAL,
        a.DDIE_LOT = b.DDIE_LOT,
        a.DWAFER_DIAMETER = b.DWAFER_DIAMETER,
        a.DWAFER_THICKNESS = b.DWAFER_THICKNESS,
        a.DSAP_LOT = b.DSAP_LOT,
        a.SOLDER = b.SOLDER,
        a.CHIPCAP = b.CHIPCAP,
        a.BOAC = b.BOAC,
        a.CHG_FAB = b.CHG_FAB,
        a.COMMENT_MQ = b.COMMENT_MQ,
        a.SBEWAYBILL = b.SBEWAYBILL,
        a.PO = b.PO,
        a.DFABTECH = b.DFABTECH,
        a.DBACKGRIND = b.DBACKGRIND,
        a.DWAFER_USE = b.DWAFER_USE,
        a.DWAFER_NUM = b.DWAFER_NUM,
        a.DBACKUM = b.DBACKUM,
        a.DTHICKUM = b.DTHICKUM,
        a.DBOAC = b.DBOAC,
        a.PROBED2 = b.PROBED2,
        a.INKLESS2 = b.INKLESS2,
        a.INKLESS_METHOD2 = b.INKLESS_METHOD2,
        a.MAP_AVAIL2 = b.MAP_AVAIL2,
        a.MAP_LOC2 = b.MAP_LOC2,
        a.USE_GEC2 = b.USE_GEC2,
        a.RMR_RETEST = b.RMR_RETEST,
        a.PB_FREE = b.PB_FREE,
        a.SCRIBEWIDTH = b.SCRIBEWIDTH,
        a.DSCRIBEWIDTH = b.DSCRIBEWIDTH,
        a.SCRIBEUM = b.SCRIBEUM,
        a.DSCRIBEUM = b.DSCRIBEUM,
        a.RELTESTS = b.RELTESTS,
        a.COMMENT_REL = b.COMMENT_REL,
        a.BUMPREQ = b.BUMPREQ,
        a.BUMP_MASK = b.BUMP_MASK,
        a.BUMP_MATL = b.BUMP_MATL,
        a.BUMP_REPASS = b.BUMP_REPASS,
        a.BUMP_GP_MAP = b.BUMP_GP_MAP,
        a.COMMENT_BUMP = b.COMMENT_BUMP,
        a.NEW_PGM = b.NEW_PGM,
        a.NEW_HW = b.NEW_HW,
        a.CHK_ASSYMQ = b.CHK_ASSYMQ,
        a.CHK_TESTMQ = b.CHK_TESTMQ,
        a.CHK_PRODQUAL = b.CHK_PRODQUAL,
        a.MARKET_CATEGORY = b.MARKET_CATEGORY,
        a.SWR_TYPE = b.SWR_TYPE,
        a.DTALOGAVAIL = b.DTALOGAVAIL,
        a.CORNER_LOT = b.CORNER_LOT,
        a.LINE_ITEM = b.LINE_ITEM,
        a.RTP2_SUBMITTED = b.RTP2_SUBMITTED,
        a.RTP_BASESET = b.RTP_BASESET,
        a.TEST_CONFIG = b.TEST_CONFIG,
        a.PACK_REQ = b.PACK_REQ,
        a.PACK_REQ_PN = b.PACK_REQ_PN,
        a.PACK_REQ_PN_TAPE = b.PACK_REQ_PN_TAPE,
        a.PACK_REQ_PN_COVER = b.PACK_REQ_PN_COVER
      </update>
    
    <delete id="delRedbullMax">
        delete from requests_redbull
        where uploaded_by = #{userId}
        and swr_id = #{swrId}
        and upload_dttm = 
        (select max(upload_dttm) from requests_redbull where swr_id = #{swrId}
        and uploaded_by = #{userId})
    </delete>

    <delete id="deleteRedbullRecords">
        delete from requests_redbull
        where uploaded_by = #{userId}
        and swr_id = #{swrId}
    </delete>
    
    <select id="getCurrentStatusBySwrId" resultType="String">
        select curr_status(#{swrId}) from dual
    </select>

    <select id="getSignoffFlagFromLdapRefBySwrId" resultType="String">
        select signoff from (
            select signoff from ldapxref where sitecode = 
                            (select sbe1site
                            from requests where swr_id = #{swrId})
                            order by org) where rownum = 1
    </select>

    <update id="pkgSwrHistoryUpdateStatus" statementType="CALLABLE">
        declare
        begin
            scswr.pkg_swr_history.update_status(#{swrId}, #{newStatus}, #{reasonCode, jdbcType=VARCHAR}, #{changeReason}, #{uid}, #{result, jdbcType=INTEGER, mode=OUT});
        end;
    </update>
    
    <insert id="insertRequests" parameterType="hashmap">

        INSERT into scswr.requests_redbull
        (SWR_ID,
        CURRENT_STATUS,
        TITLE,
        ATSITE,
        SBE1SITE,
        CHARGE,
        PDBFACILITY,
        NEED_DATE,
        PRIORITY,
        COPY_EMAIL,
        SHIP_TYPE,
        GEC_QTY,
        ASYREQ,
        DIE_LOT,
        DEVICE_NAME,
        PTSS_TRAVEL,
        DIE_NAME,
        PARENT_DEVICE,
        PIN,
        PKG,
        DIE_REV,
        BLD_QTY,
        WAFER_USE,
        DIE_DESIGNATOR,
        DIE_SIZE,
        FAB_CODE,
        FABTECH,
        WAFER_DIAMETER,
        WAFER_THICKNESS,
        THICKUM,
        BACKGRIND,
        BACKGUM,
        WIRE_DIAMETER,
        WAFER_NUM,
        PROBED,
        INKLESS,
        INKLESS_METHOD,
        MAP_AVAIL,
        MAP_LOC,
        BIN_NUM,
        DUALDIE,
        DDIENAME,
        DDIEREV,
        DDIEDSG,
        DDIESIZE,
        DDFABCODE,
        TRAVEL_STAT,
        LEADFRAMES1,
        MOLD_COMPOUND1,
        MOUNT_COMPOUND1,
        WIRE,
        BASE_OUTLINE1,
        BP_METAL1,
        HEADER1,
        LID1,
        MB_FILE_LOC,
        MB_LOC,
        USE_GEC,
        CHG_BOND,
        CHG_DIEDEV,
        CHG_DIEREV,
        CHG_LF,
        CHG_MOLD,
        CHG_MOUNT,
        CHG_OTHER,
        CHG_SAW,
        CHG_OTHER_DESC,
        SYMBOL_SPEC,
        SYMTYPE,
        TESTREQ,
        TEST_SYSTEM,
        TEST_TEMP,
        TEST_PROG,
        LIBRARY,
        OTHER_TEST,
        BURNIN,
        REPORT,
        TEST_LIB,
        BRDAVAIL,
        CORUAVAIL,
        PGMAVAIL,
        PACKOPT,
        STICKERTYPE,
        SOF,
        DISPMTL,
        DISPWAF,
        HOW_SHIP_REM,
        RETPACKPC,
        SALEABLE,
        VISIBLE_DQ,
        ECCNREQ,
        COMMENT_PURPOSE,
        COMMENT_REQ,
        COMMENT_WAF,
        COMMENT_ASY,
        COMMENT_TST,
        COMMENT_PAK,
        COMMENT_FINAL,
        DDIE_LOT,
        DWAFER_DIAMETER,
        DWAFER_THICKNESS,
        DSAP_LOT,
        SOLDER,
        CHIPCAP,
        BOAC,
        CHG_FAB,
        COMMENT_MQ,
        SBEWAYBILL,
        PO,
        DFABTECH,
        DBACKGRIND,
        DWAFER_USE,
        DWAFER_NUM,
        DBACKUM,
        DTHICKUM,
        DBOAC,
        PROBED2,
        INKLESS2,
        INKLESS_METHOD2,
        MAP_AVAIL2,
        MAP_LOC2,
        USE_GEC2,
        RMR_RETEST,
        PB_FREE,
        SCRIBEWIDTH,
        DSCRIBEWIDTH,
        SCRIBEUM,
        DSCRIBEUM,
        RELTESTS,
        COMMENT_REL,
        BUMPREQ,
        BUMP_MASK,
        BUMP_MATL,
        BUMP_REPASS,
        BUMP_GP_MAP,
        COMMENT_BUMP,
        NEW_PGM,
        NEW_HW,
        CHK_ASSYMQ,
        CHK_TESTMQ,
        CHK_PRODQUAL,
        MARKET_CATEGORY,
        SWR_TYPE,
        DTALOGAVAIL,
        CORNER_LOT,
        LINE_ITEM,
        RTP2_SUBMITTED,
        RTP_BASESET,
        TEST_CONFIG,
        PACK_REQ,
        PACK_REQ_PN,
        PACK_REQ_PN_TAPE,
        PACK_REQ_PN_COVER,
        MQTESTS,
        UPLOAD_DTTM,
        UPLOADED_BY
        )
        values (#{SWR_ID,jdbcType=VARCHAR},
        #{CURRENT_STATUS,jdbcType=VARCHAR},
        #{TITLE,jdbcType=VARCHAR},
        #{ATSITE,jdbcType=VARCHAR},
        #{SBE1SITE,jdbcType=VARCHAR},
        #{CHARGE,jdbcType=VARCHAR},
        #{PDBFACILITY,jdbcType=VARCHAR},
        to_date(#{NEED_DATE,jdbcType=DATE}, 'MM/DD/YYYY'),
<!--        to_date(#{SBE_SHIPPED_PAPERWORK_UPDATE,jdbcType=DATE}, 'MM/DD/YYYY'),-->
        #{PRIORITY,jdbcType=VARCHAR},
        #{COPY_EMAIL,jdbcType=VARCHAR},
        #{SHIP_TYPE,jdbcType=VARCHAR},
        #{GEC_QTY,jdbcType=VARCHAR},
        #{ASYREQ,jdbcType=VARCHAR},
        #{DIE_LOT,jdbcType=VARCHAR},
        #{DEVICE_NAME,jdbcType=VARCHAR},
        #{PTSS_TRAVEL,jdbcType=VARCHAR},
        #{DIE_NAME,jdbcType=VARCHAR},
        #{PARENT_DEVICE,jdbcType=VARCHAR},
        #{PIN,jdbcType=VARCHAR},
        #{PKG,jdbcType=VARCHAR},
        #{DIE_REV,jdbcType=VARCHAR},
        #{BLD_QTY,jdbcType=VARCHAR},
        #{WAFER_USE,jdbcType=VARCHAR},
        #{DIE_DESIGNATOR,jdbcType=VARCHAR},
        #{DIE_SIZE,jdbcType=VARCHAR},
        #{FAB_CODE,jdbcType=VARCHAR},
        #{FABTECH,jdbcType=VARCHAR},
        #{WAFER_DIAMETER,jdbcType=VARCHAR},
        #{WAFER_THICKNESS,jdbcType=VARCHAR},
        #{THICKUM,jdbcType=VARCHAR},
        #{BACKGRIND,jdbcType=VARCHAR},
        #{BACKGUM,jdbcType=VARCHAR},
        #{WIRE_DIAMETER,jdbcType=VARCHAR},
        #{WAFER_NUM,jdbcType=VARCHAR},
        #{PROBED,jdbcType=VARCHAR},
        #{INKLESS,jdbcType=VARCHAR},
        #{INKLESS_METHOD,jdbcType=VARCHAR},
        #{MAP_AVAIL,jdbcType=VARCHAR},
        #{MAP_LOC,jdbcType=VARCHAR},
        #{BIN_NUM,jdbcType=VARCHAR},
        #{DUALDIE,jdbcType=VARCHAR},
        #{DDIENAME,jdbcType=VARCHAR},
        #{DDIEREV,jdbcType=VARCHAR},
        #{DDIEDSG,jdbcType=VARCHAR},
        #{DDIESIZE,jdbcType=VARCHAR},
        #{DDFABCODE,jdbcType=VARCHAR},
        #{TRAVEL_STAT,jdbcType=VARCHAR},
        #{LEADFRAMES1,jdbcType=VARCHAR},
        #{MOLD_COMPOUND1,jdbcType=VARCHAR},
        #{MOUNT_COMPOUND1,jdbcType=VARCHAR},
        #{WIRE,jdbcType=VARCHAR},
        #{BASE_OUTLINE1,jdbcType=VARCHAR},
        #{BP_METAL1,jdbcType=VARCHAR},
        #{HEADER1,jdbcType=VARCHAR},
        #{LID1,jdbcType=VARCHAR},
        #{MB_FILE_LOC,jdbcType=VARCHAR},
        #{MB_LOC,jdbcType=VARCHAR},
        #{USE_GEC,jdbcType=VARCHAR},
        #{CHG_BOND,jdbcType=VARCHAR},
        #{CHG_DIEDEV,jdbcType=VARCHAR},
        #{CHG_DIEREV,jdbcType=VARCHAR},
        #{CHG_LF,jdbcType=VARCHAR},
        #{CHG_MOLD,jdbcType=VARCHAR},
        #{CHG_MOUNT,jdbcType=VARCHAR},
        #{CHG_OTHER,jdbcType=VARCHAR},
        #{CHG_SAW,jdbcType=VARCHAR},
        #{CHG_OTHER_DESC,jdbcType=VARCHAR},
        #{SYMBOL_SPEC,jdbcType=VARCHAR},
        #{SYMTYPE,jdbcType=VARCHAR},
        #{TESTREQ,jdbcType=VARCHAR},
        #{TEST_SYSTEM,jdbcType=VARCHAR},
        #{TEST_TEMP,jdbcType=VARCHAR},
        #{TEST_PROG,jdbcType=VARCHAR},
        #{LIBRARY,jdbcType=VARCHAR},
        #{OTHER_TEST,jdbcType=VARCHAR},
        #{BURNIN,jdbcType=VARCHAR},
        #{REPORT,jdbcType=VARCHAR},
        #{TEST_LIB,jdbcType=VARCHAR},
        #{BRDAVAIL,jdbcType=VARCHAR},
        #{CORUAVAIL,jdbcType=VARCHAR},
        #{PGMAVAIL,jdbcType=VARCHAR},
        #{PACKOPT,jdbcType=VARCHAR},
        #{STICKERTYPE,jdbcType=VARCHAR},
        #{SOF,jdbcType=VARCHAR},
        #{DISPMTL,jdbcType=VARCHAR},
        #{DISPWAF,jdbcType=VARCHAR},
        #{HOW_SHIP_REM,jdbcType=VARCHAR},
        #{RETPACKPC,jdbcType=VARCHAR},
        #{SALEABLE,jdbcType=VARCHAR},
        #{VISIBLE_DQ,jdbcType=VARCHAR},
        #{ECCNREQ,jdbcType=VARCHAR},
        #{COMMENT_PURPOSE,jdbcType=VARCHAR},
        #{COMMENT_REQ,jdbcType=VARCHAR},
        #{COMMENT_WAF,jdbcType=VARCHAR},
        #{COMMENT_ASY,jdbcType=VARCHAR},
        #{COMMENT_TST,jdbcType=VARCHAR},
        #{COMMENT_PAK,jdbcType=VARCHAR},
        #{COMMENT_FINAL,jdbcType=VARCHAR},
        #{DDIE_LOT,jdbcType=VARCHAR},
        #{DWAFER_DIAMETER,jdbcType=VARCHAR},
        #{DWAFER_THICKNESS,jdbcType=VARCHAR},
        #{DSAP_LOT,jdbcType=VARCHAR},
        #{SOLDER,jdbcType=VARCHAR},
        #{CHIPCAP,jdbcType=VARCHAR},
        #{BOAC,jdbcType=VARCHAR},
        #{CHG_FAB,jdbcType=VARCHAR},
        #{COMMENT_MQ,jdbcType=VARCHAR},
        #{SBEWAYBILL,jdbcType=VARCHAR},
        #{PO,jdbcType=VARCHAR},
        #{DFABTECH,jdbcType=VARCHAR},
        #{DBACKGRIND,jdbcType=VARCHAR},
        #{DWAFER_USE,jdbcType=VARCHAR},
        #{DWAFER_NUM,jdbcType=VARCHAR},
        #{DBACKUM,jdbcType=VARCHAR},
        #{DTHICKUM,jdbcType=VARCHAR},
        #{DBOAC,jdbcType=VARCHAR},
        #{PROBED2,jdbcType=VARCHAR},
        #{INKLESS2,jdbcType=VARCHAR},
        #{INKLESS_METHOD2,jdbcType=VARCHAR},
        #{MAP_AVAIL2,jdbcType=VARCHAR},
        #{MAP_LOC2,jdbcType=VARCHAR},
        #{USE_GEC2,jdbcType=VARCHAR},
        #{RMR_RETEST,jdbcType=VARCHAR},
        #{PB_FREE,jdbcType=VARCHAR},
        #{SCRIBEWIDTH,jdbcType=VARCHAR},
        #{DSCRIBEWIDTH,jdbcType=VARCHAR},
        #{SCRIBEUM,jdbcType=VARCHAR},
        #{DSCRIBEUM,jdbcType=VARCHAR},
        #{RELTESTS,jdbcType=VARCHAR},
        #{COMMENT_REL,jdbcType=VARCHAR},
        #{BUMPREQ,jdbcType=VARCHAR},
        #{BUMP_MASK,jdbcType=VARCHAR},
        #{BUMP_MATL,jdbcType=VARCHAR},
        #{BUMP_REPASS,jdbcType=VARCHAR},
        #{BUMP_GP_MAP,jdbcType=VARCHAR},
        #{COMMENT_BUMP,jdbcType=VARCHAR},
        #{NEW_PGM,jdbcType=VARCHAR},
        #{NEW_HW,jdbcType=VARCHAR},
        #{CHK_ASSYMQ,jdbcType=VARCHAR},
        #{CHK_TESTMQ,jdbcType=VARCHAR},
        #{CHK_PRODQUAL,jdbcType=VARCHAR},
        #{MARKET_CATEGORY,jdbcType=VARCHAR},
        #{SWR_TYPE,jdbcType=VARCHAR},
        #{DTALOGAVAIL,jdbcType=VARCHAR},
        #{CORNER_LOT,jdbcType=VARCHAR},
        #{LINE_ITEM,jdbcType=VARCHAR},
        #{RTP2_SUBMITTED,jdbcType=VARCHAR},
        #{RTP_BASESET,jdbcType=VARCHAR},
        #{TEST_CONFIG,jdbcType=VARCHAR},
        #{PACK_REQ,jdbcType=VARCHAR},
        #{PACK_REQ_PN,jdbcType=VARCHAR},
        #{PACK_REQ_PN_TAPE,jdbcType=VARCHAR},
        #{PACK_REQ_PN_COVER,jdbcType=VARCHAR},
        #{MQTESTS,jdbcType=VARCHAR},
        sysdate,
        #{UPLOADED_BY,jdbcType=VARCHAR}
        )
       
    </insert>
    
    
    <select id="getSbeView" parameterType="String" resultType="java.util.LinkedHashMap">
        SELECT * FROM TABLE (print_table ('select *
         from sbe_forecast_revised_view where uploaded_by = ''${uid}''
        <if test="swrId != '' and swrId != null">
            and swr_id = ''${swrId}''
        </if>
        '))
    </select>
    
    <select id="getSwrIdUpdateList" resultType="String">
        select distinct swr_id from SBE_FORECAST_REVISED_VW where uploaded_by = #{uid}
        order by swr_id desc
    </select>

    <select id="getBpDiffColumns" resultType="String">
      SELECT DB_COLUMN_NAME 
      FROM BP_EXCEL_CFG
      WHERE DB_COLUMN_NAME != 'SWR_ID'
      ORDER BY EXCEL_INDEX
    </select>

    <resultMap id="BpDiffMap" type="com.ti.specteam.vswr.dashboard.domain.BpDiff">
      <id column="swr_id" property="swrId"/>
      <id column="uploaded_by" property="uploadedBy"/>
      <result column="upload_dttm" property="uploadDttm"/>
      <result column="column_name" property="columnName"/>
      <result column="curr_value" property="currValue"/>
      <result column="bp_value" property="bpValue"/>
      <result column="is_diff" property="isDiff"/>
    </resultMap>

    <sql id="selectBpDiffs">
      SELECT
        R.COLUMN_NAME,
        R.SWR_ID,
        RR.UPLOADED_BY,
        RR.UPLOAD_DTTM,
        R.COLUMN_VALUE CURR_VALUE,
        RR.COLUMN_VALUE BP_VALUE,
        CASE
          WHEN R.COLUMN_VALUE IS NULL AND RR.COLUMN_VALUE IS NULL THEN 0
          WHEN R.COLUMN_VALUE = RR.COLUMN_VALUE THEN 0
          ELSE 1
        END AS IS_DIFF
      FROM
        (
          SELECT SWR_ID, COLUMN_NAME, COLUMN_VALUE
          FROM
          (
            SELECT SWR_ID,
            <foreach collection="columns" item="column" separator=",">
            TO_CHAR(${column}) ${column}
            </foreach>
            FROM REQUESTS
          )
          UNPIVOT (
            COLUMN_VALUE FOR COLUMN_NAME IN
            <foreach collection="columns" item="column" open="(" separator="," close=")">
            ${column}
            </foreach>
          )
        ) R
        JOIN
        (
          SELECT SWR_ID, UPLOADED_BY, UPLOAD_DTTM, COLUMN_NAME, COLUMN_VALUE
          FROM
          (
            SELECT SWR_ID, UPLOADED_BY, UPLOAD_DTTM,
            <foreach collection="columns" item="column" separator=",">
            TO_CHAR(${column}) ${column}
            </foreach>
            FROM REQUESTS_REDBULL
          )
          UNPIVOT (
            COLUMN_VALUE FOR COLUMN_NAME IN
            <foreach collection="columns" item="column" open="(" separator="," close=")">
            ${column}
            </foreach>
          )
        ) RR ON (R.SWR_ID = RR.SWR_ID AND R.COLUMN_NAME = RR.COLUMN_NAME)
    </sql>

    <select id="isBpDiff" resultType="boolean">
      SELECT COUNT(*)
      FROM (
        SELECT
          <foreach collection="columns" item="column" separator=",">
          ${column}
          </foreach>
        FROM REQUESTS
        WHERE swr_id = #{swrId}
        MINUS
        SELECT
          <foreach collection="columns" item="column" separator=",">
          ${column}
          </foreach>
        FROM REQUESTS_REDBULL
        WHERE swr_id = #{swrId}
          and uploaded_by = #{uploadedBy}
      )
    </select>

    <select id="getBpDiffs" resultMap="BpDiffMap">
      SELECT *
      FROM (<include refid="selectBpDiffs"></include>)
      <where>
        is_diff = 1
        <if test="swrId != null">
            AND swr_id = #{swrId}
        </if>
        <if test="uploadedBy != null">
         AND uploaded_by = #{uploadedBy}
        </if>
      </where>
      ORDER BY swr_id, uploaded_by
    </select>
    
    <resultMap id="ScswrExcelCellConfigMap" type="com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig">
      <id column="ID" property="id"/>
      <result column="EXCEL_INDEX" property="excelIndex"/>
      <result column="EXCEL_LABEL" property="excelLabel"/>
      <result column="EXCEL_SPECIAL_NAME" property="excelSpecialName"/>
      <result column="DB_COLUMN_NAME" property="dbColumnName"/>
      <result column="IS_MULTISELECT" property="isMultiselect"/>
      <result column="IS_CONSTRAINED_BY_SQL_VALUES" property="isConstrainedBySqlValues"/>
      <result column="SQL_VALUE_SOURCE" property="sqlValueSource"/>
      <collection property="possibleValues" resultMap="ScswrExcelCellValueMapMap" columnPrefix="MAP_"/>
    </resultMap>

    <resultMap id="ScswrExcelCellValueMapMap" type="com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellValueMap">
      <id column="ID" property="id"/>
      <result column="EXCEL_VALUE" property="excelValue"/>
      <result column="DATABASE_VALUE" property="databaseValue"/>
      <result column="EXCEL_CFG_ID" property="excelCfgId"/>
    </resultMap>

    <sql id="selectBpExcelCfg">
      SELECT *
      FROM (
        SELECT
          cfg.*,

          mapper_cfg.ID MAP_ID,
          mapper_cfg.EXCEL_VALUE MAP_EXCEL_VALUE,
          mapper_cfg.DATABASE_VALUE MAP_DATABASE_VALUE,
          mapper_cfg.EXCEL_CFG_ID MAP_EXCEL_CFG_ID
        FROM BP_EXCEL_CFG cfg LEFT JOIN
          BP_EXCEL_DB_VALUE_MAP_CFG mapper_cfg ON (cfg.id = mapper_cfg.excel_cfg_id)
      )
    </sql>

    <select id="getSbeExcelCfgs" resultMap="ScswrExcelCellConfigMap">
      <include refid="selectBpExcelCfg"></include>
      ORDER BY EXCEL_INDEX
    </select>

    <select id="getConstrainedSbeExcelConfigs" resultMap="ScswrExcelCellConfigMap">
      <include refid="selectBpExcelCfg"></include>
      WHERE MAP_ID IS NOT NULL
        OR IS_CONSTRAINED_BY_SQL_VALUES = 1
      ORDER BY EXCEL_INDEX
    </select>

    <select id="getSbeExcelCfgByExcelLabel" resultMap="ScswrExcelCellConfigMap">
      <include refid="selectBpExcelCfg"></include>
      WHERE EXCEL_LABEL = #{excelLabel}
    </select>
    
    <select id="getAtCoordCatEmail" resultType="String">
        select distinct email from notify where sitecode = #{atSite} and notifytype in ('COORD', 'ATCAT')
    </select>
    
    <select id="checkSwrsNoForecast" resultType="java.util.LinkedHashMap">
        select status, to_char(target, 'MM/DD/YYYY') as target, 
        to_char(updated, 'MM/DD/YYYY') as updated, to_char(actual, 'MM/DD/YYYY')
        as actual from forecast_dates where swrid = ${swrId}
        <if test="status != null">
            and status = #{status}
        </if>
    </select>
    
    <update id="calculateDates" statementType="CALLABLE">
        {CALL CALCULATE_DATES(${swrId}, #{status})}
    </update>
    
    <update id="updateForecastDates" parameterType="String">
        update FORECAST_DATES set updated = TO_DATE(#{updatedDate}, 'MM/DD/YYYY'), 
        update_dttm = sysdate where swrid = ${swrId} and status = #{status}
    </update>
    
    <update id="updateTarget" parameterType="String">
        update FORECAST_DATES set target = TO_DATE(#{targetDate}, 'MM/DD/YYYY'), 
        update_dttm = sysdate where swrid = ${swrId} and status = #{status}
    </update>
    
    <insert id="insertForecastDates" parameterType="String">
        insert into FORECAST_DATES values
        (${swrId},#{status},TO_DATE(#{targetDate}, 'MM/DD/YYYY'),TO_DATE(#{updatedDate}, 'MM/DD/YYYY'),TO_DATE(#{actualDate}, 'MM/DD/YYYY'), sysdate)
    </insert>
    
    <update id="updateEstDate" parameterType="String">
        update requests set at_eststart_date = to_date(#{atStartTargetDate}, 'MM/DD/YYYY'), 
        at_estfinish_date = to_date(#{atShippedTargetDate}, 'MM/DD/YYYY') where swr_id = ${swrId}
    </update>
    
    <select id="getLdapxref" resultType="java.util.LinkedHashMap">
        select sitetype, sitecode from ldapxref where org = #{org}
    </select>
    
    <select id="getPlantCode" resultType="String">
        SELECT plantcode
                         FROM list_box
                        WHERE at_site = #{atSite}
    </select>
    
    <select id="swrListsByStat" resultType="java.util.LinkedHashMap">
        SELECT * FROM (
            SELECT t.*, rownum rn FROM (
        select * from (select * from (
        <include refid="selectSwrStat"/>)
        <where>
            <foreach collection="params.filterTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    ${key} = #{term}
                </if>
                <if test="term == null">
                    ${key} IS NULL
                </if>
            </foreach>
        </where>
        )
        <where>
            <foreach collection="params.searchTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    <choose>
                        <when test="term.end != null &amp;&amp; term.end != 'undefined'">
                            UPPER(${key}) between to_date(#{term.start},'mm/dd/yyyy') AND to_date(#{term.end},'mm/dd/yyyy')
                        </when>
                        <otherwise>
                            UPPER(${key}) LIKE '%'|| UPPER(#{term.start}) ||'%'  
                        </otherwise>
                    </choose>
                </if>
            </foreach>
        </where>
        <if test="params.orderClause != null and !params.orderClause.equals('')">
             ORDER BY ${params.orderClause}
        </if>
            ) t
        ) WHERE rn &gt;= ${params.firstRow} AND rn &lt; ${params.firstRow} + ${params.rowsPerPage}
    </select>
    
    <select id="countSwrListsByStat" resultType="int">
        SELECT COUNT(1) FROM (
        select * from (
            SELECT * FROM
        (<include refid="selectSwrStat"/>)
        <where>                   
            <foreach collection="params.filterTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    ${key} = #{term}
                </if>
                <if test="term == null">
                    ${key} IS NULL
                </if>
            </foreach>
        </where>
        ) t )
        <where>
            <foreach collection="params.searchTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    <choose>
                        <when test="term.end != null &amp;&amp; term.end != 'undefined'">
                            UPPER(${key}) between to_date(#{term.start},'mm/dd/yyyy') AND to_date(#{term.end},'mm/dd/yyyy')
                        </when>
                        <otherwise>
                            UPPER(${key}) LIKE '%'|| UPPER(#{term.start}) ||'%'  
                        </otherwise>
                    </choose>
                </if>
            </foreach>
        </where>
    </select>
    
</mapper>
