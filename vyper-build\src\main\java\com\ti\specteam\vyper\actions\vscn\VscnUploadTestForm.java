package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@Jacksonized
public class VscnUploadTestForm extends VscnNumberForm {
    // its ok if the content is null. this means it's being deleted
    private String content;
}
