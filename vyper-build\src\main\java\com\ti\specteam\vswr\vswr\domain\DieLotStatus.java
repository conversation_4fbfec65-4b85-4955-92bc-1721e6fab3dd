package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DieLotStatus {
    private String material;
    private String batch;
    private String plant;
    private String unrestricted;
    private String stock;
    private String qualInspection;
    private String restricted;
    private String blocked;
    private String returns;
}
