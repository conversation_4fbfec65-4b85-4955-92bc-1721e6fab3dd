package com.ti.specteam.atssmassupload.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;

public interface ProjectDeviceRepository extends JpaRepository<ProjectDevice, String> {

	@Query(value = "SELECT *  FROM MU_PROJ_DEVICE pd WHERE pd.PROJ_ID = :projectId", nativeQuery = true)
	List<ProjectDevice> findProjectDeviceByProjectId(@Param("projectId") String projectId);

	@Query(value = "SELECT * FROM MU_PROJ_DEVICE pd WHERE pd.PROJ_ID=:projId AND pd.id=:deviceId", nativeQuery = true)
	ProjectDevice findProjectDeviceByProjIdAndDeviceId(@Param("projId") String projId,
			@Param("deviceId") String deviceId);

	@Query(value = "SELECT pd.id FROM MU_PROJ_DEVICE pd WHERE pd.PROJ_ID=:projId AND pd.SPEC_DEVICE=:specDevice", nativeQuery = true)
	String findProjectDeviceIdBySpecDevice(@Param("projId") String projId, @Param("specDevice") String specDevice);
	
	@Modifying
	@Transactional
	@Query(value = "DELETE FROM MU_PROJ_DEVICE pd WHERE pd.PROJ_ID =:projId", nativeQuery = true)
	void deleteProjDeviceByProjId(@Param("projId") String projId);

}