package com.ti.specteam.atssmassupload.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ti.specteam.atssmassupload.domain.*;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadException;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadProjectNotFound;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceRepository;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceTravelerRepository;
import com.ti.specteam.vyper.apitraveler.ApiTravelerService;
import com.ti.specteam.vyper.apitraveler.TravelerVariant;
import com.ti.specteam.vyper.apitraveler.model.*;
import com.ti.specteam.vyper.atss.traveler.AtssRepository;
import com.ti.specteam.vyper.atss.traveler.Component;
import com.ti.specteam.vyper.atss.traveler.Paragraph;
import com.ti.specteam.vyper.atss.traveler.Status;
import com.ti.specteam.vyper.atss.traveler.Traveler;
import com.ti.specteam.vyper.atss.traveler.TravelerForm;
import com.ti.specteam.vyper.atss.traveler.TravelerService;
import com.ti.specteam.vyper.vscn.kafka.KafkaAtssTopicProducer;
import com.ti.specteam.vyper.vscn.model.KafkaAtssTopicResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectDeviceTravelerService {

    private final TravelerService travelerService;
    private final ProjectDeviceTravelerRepository projectDeviceTravelerRepository;
    private final ProjectHeaderService projectHeaderService;
    private final ProjectDeviceRepository projectDeviceRepository;
    private final AtssRepository atssRepository;
    private final ApiTravelerService apiTravelerService;
    private final AtssComponentValidatorService atssComponentValidatorService;
    private final SpecCamsService specCamsService;
    private final ProjectDeviceService projectDeviceService;
    private final KafkaAtssTopicProducer kafkaAtssTopicProducer;
    private final ProjectDeviceLogService projectDeviceLogService;
    private final ProjectDeviceVscnService projectDeviceVscnService;

    /**
     *
     * @param projectId project ID
     * @return the list of Traveler JSON by project
     */
    public ProjectDeviceTraveler getProjectTraveler(String projectId, TravelerMode travelerMode){

        ProjectDeviceTraveler projectDeviceTraveler = new ProjectDeviceTraveler();
        projectDeviceTraveler.setTravelerMode(travelerMode);

        // Get Reference Atss Traveler Input Info
        ProjectHeader projectHeader = projectHeaderService.validateUserByProjectId(projectId);

        // If not found, check by project number
        if ( projectHeader == null ){
            projectHeader = projectHeaderService.validateUserByProjectNumber(projectId);

            //If project Id not found either in ID or number , return error message
            if ( projectHeader == null ){
                throw new AtssMassUploadProjectNotFound(HttpStatus.BAD_REQUEST);
            }
        }
        projectDeviceTraveler.setProjectHeader(projectHeader);

        //Get reference Atss Traveler
        projectDeviceTraveler.setReferenceTraveler(getProjectReferenceTraveler(projectHeader,travelerMode));
        // Get material Atss Traveler Input
        projectDeviceTraveler.setProjectDeviceTraveler(getProjectDeviceTravelers(projectHeader,travelerMode));

        return projectDeviceTraveler;
    }
    public ProjectDeviceVyperTraveler getProjectDeviceTravelerVyper(String projectId, TravelerMode travelerMode) {
        ProjectDeviceVyperTraveler projectDeviceTravelers = new ProjectDeviceVyperTraveler();
        projectDeviceTravelers.setTravelerMode(travelerMode);

        ProjectHeader projectHeader = projectHeaderService.validateUserByProjectId(projectId);
        if ( projectHeader == null ){
            projectHeader = projectHeaderService.validateUserByProjectNumber(projectId);

            //If project Id not found either in ID or number , return error message
            if ( projectHeader == null ){
                throw new AtssMassUploadProjectNotFound(HttpStatus.BAD_REQUEST);
            }
        }
        projectDeviceTravelers.setProjectHeader(projectHeader);

        projectDeviceTravelers.setReferenceTraveler(getProjectReferenceTravelerVyper(projectHeader));

        projectDeviceTravelers.setProjectDeviceTraveler(getProjectDeviceTravelersVyper(projectDeviceTravelers.getReferenceTraveler(), projectHeader));
        return projectDeviceTravelers;
    }

    private ApiTraveler getProjectReferenceTravelerVyper(ProjectHeader projectHeader){

        ApiTraveler traveler = new ApiTraveler();
        TravelerVariant variant =  TravelerVariant.VYPER;
        if (! projectHeader.getRefSpecDevice().isBlank()){
            TravelerForm refTravelerForm = new TravelerForm( projectHeader.getRefSpecDevice(),
                    projectHeader.getFacilityAt(),
                    (!projectHeader.getRefStatus().isEmpty() && projectHeader.getRefStatus().equals("W") ? Status.WORKING: Status.ACTIVE));

            try{
                traveler = apiTravelerService.traveler(projectHeader.getRefVyperBuildNumber(),variant);
            }catch(TravelerService.TravelerNotFoundException travelerNotFoundException){
                log.warn("No reference traveler found");
            }
        }
        return traveler;

    }
    private Traveler getProjectReferenceTraveler(ProjectHeader projectHeader, TravelerMode travelerMode){

        Traveler traveler = new Traveler();

        // ATSS mode
        if ( travelerMode == TravelerMode.ATSS ){
            if (! projectHeader.getRefSpecDevice().isBlank()){
                TravelerForm refTravelerForm = new TravelerForm( projectHeader.getRefSpecDevice(),
                        projectHeader.getFacilityAt(),
                        (!projectHeader.getRefStatus().isEmpty() && projectHeader.getRefStatus().equals("W") ? Status.WORKING: Status.ACTIVE));

                try{
                    traveler = getAtssTravelerOrEmpty(refTravelerForm);
                }catch(TravelerService.TravelerNotFoundException travelerNotFoundException){
                    log.warn("No reference traveler found");
                }
            }
        }

        return traveler;

    }

    private List<ApiTraveler> getProjectDeviceTravelersVyper(ApiTraveler refTraveler, ProjectHeader projectHeader) {

        List<ProjectDevice> projectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projectHeader.getProjId());

        return projectDevices.stream().map(device -> {
            ApiTraveler deviceTraveler = apiTravelerService.copyTraveler(refTraveler);
            deviceTraveler = projectDeviceService.fillHeaderInformation(deviceTraveler, device, projectHeader.getFacilityAt());
            updateTravelerBySpecChanges(deviceTraveler, device.getSpecChanges(), projectHeader.getFacilityAt(), TravelerVariant.VYPER);
            return deviceTraveler;
        }).collect(Collectors.toList());
    }

    private ApiTraveler getDeviceTravelerForAtss(ProjectHeader projectHeader, ApiTraveler refTraveler, ProjectDevice projectDevice){
        ApiTraveler traveler  = apiTravelerService.copyTraveler(refTraveler);
        //Remove Vyper specific components
        removeVyperComponents(traveler);
        projectDeviceService.fillHeaderInformation(traveler, projectDevice, projectHeader.getFacilityAt());
        updateTravelerBySpecChanges(traveler, projectDevice.getSpecChanges(), projectHeader.getFacilityAt(), TravelerVariant.ATSS);

        return traveler;
    }

    /**
     * remove pkg niche component
     * and intentionally left blank
     * @param traveler
     */
    private void removeVyperComponents(ApiTraveler traveler){
        traveler.getSubFlows().stream()
                .filter(sf -> StringUtils.equalsIgnoreCase("HEADER", sf.getType()))
                .map(sf -> {
                    if (sf.getOperations().size() > 0 )
                        return sf.getOperations().get(0);// Only one header operation will present
                    else return new ApiTravelerOperation("HEADER");
                })
                .forEach(opn -> opn.getComponents().removeIf(
                        component -> (StringUtils.equals("Package Niche", component.getName()) ||
                                ( (component.getName().startsWith("CUST") || component.getName().startsWith("ECAT")) && component.getValue() == null  ))
                        ));
    }

    private List<Traveler> getProjectDeviceTravelers(ProjectHeader projectHeader, TravelerMode travelerMode){
        List<Traveler> materialTravelers = new ArrayList<>();

        if (travelerMode == TravelerMode.ATSS ){
            // Find the device traveler modes
            List<TravelerForm> projectDeviceAtssTravelerForms =
                    projectDeviceTravelerRepository.getProjectDeviceAtssSpecHeaders(projectHeader.getProjId(), projectHeader.getFacilityAt(), Status.WORKING);
            if ( !projectDeviceAtssTravelerForms.isEmpty()){
                materialTravelers =
                        projectDeviceAtssTravelerForms.stream()
                            .map(this::getAtssTravelerOrEmpty)
                            .collect(Collectors.toList());
            }

        }

        return materialTravelers;
    }

    private Traveler getAtssTravelerOrEmpty(TravelerForm travelerForm){
        Traveler traveler = new Traveler();
        traveler.setFacilityAt(travelerForm.getFacilityAt());
        traveler.setSpecDevice(travelerForm.getSpecDevice());
        traveler.setStatus(travelerForm.getStatus());

        try{
            traveler = travelerService.build(travelerForm);
        }catch(TravelerService.TravelerNotFoundException travelerNotFoundException){
            log.warn("No traveler found {}", travelerForm);
        }
        return traveler;
    }

    /**
     * Function to override the reference traveler information with specific component changes
     * @param traveler
     * @param changes
     * @param facility
     */
    private void updateTravelerBySpecChanges(ApiTraveler traveler, List<SpecChange> changes, String facility, TravelerVariant variant) {

        List<ApiTravelerComponent> opnSymbolComponents = new ArrayList<>();
        Set<ApiTravelerComponent> symbolComponents = new HashSet<>();

        for(SpecChange change: changes) {
            for(ApiTravelerSubflow subflow: traveler.getSubFlows()) {
                if(!subflow.getType().equals(change.getFlowType())) continue;

                for(ApiTravelerOperation operation: subflow.getOperations()) {
                    opnSymbolComponents = new ArrayList<>();

                    // check if operation is not null meaning there is attribute change so match operation
                    if(change.getOperationName() != null && !operation.getName().equals(change.getOperationName())) continue;


                    for(ApiTravelerComponent component: operation.getComponents()) {

                        int compPriority = change.getComponentOccurrence() != null ? change.getComponentOccurrence() : 1;

                        // Match name
                        if(!component.getName().equals(change.getComponentName()) ) continue;

                        // Match occurrence only if provided one is more than 1
                        if ( compPriority > 1 && !( component.getPriority() == compPriority )) continue;

                        // Fix Attributes on component value change
                        if(!change.getComponentValue().equalsIgnoreCase(component.getValue()))
                            fixTravelerComponentAttributes(component, change, facility);

                        // If the same component name exists, then replace the value
                        component.setValue(change.getComponentValue());

                        // Check if paragraph component and no spec change involving attribute
                        if(component.getParagraph() != null && !component.getParagraph().isBlank()
                        && change.getAttributeName() == null){
                            Component comp = new Component();
                            comp.setName(component.getName());
                            comp.setValue(component.getValue());
                            comp.add(atssRepository.readParagraph(comp));
                            // Find embed names if any
                            Optional<Paragraph> pOptional = comp.getParagraphs().stream()
                                                            .filter(p -> p.getEmbedName() != null).findAny();
                            if(pOptional.isPresent()){
                                // Find embed component from current operation
                                Optional<ApiTravelerComponent> componentFound = operation.getComponents()
                                    .stream()
                                    .filter(cmp -> cmp.getName().equalsIgnoreCase(pOptional.get().getEmbedName())).findFirst();
                                if(componentFound.isPresent() && componentFound.get().getValue() != null){
                                    // Build propely formatted paragraph text with embed value
                                    List<String> lines = travelerService.getLinesFromParagraphs(comp.getParagraphs(), null, componentFound.get().getValue());
                                    component.setParagraph(lines.stream().collect(Collectors.joining("\n")));
                                }
                            }
                        }

                        // skip processing attributes if change is null
                        if(change.getAttributeName() == null) continue;

                        // If Symbolization component & attribute, send to header
                        if ( change.getComponentName().toUpperCase().contains("SYMBOL")){
                            ApiTravelerComponent newComponent = createEmbedComponentsInHeader(traveler, operation, component, change, facility, variant);
                            if ( newComponent != null)   opnSymbolComponents.add(newComponent);
                        }

                        for(ApiTravelerAttribute attribute: component.getAttributes()) {

                            if(attribute.getName().equals(change.getAttributeName())) {
                                attribute.setValue(change.getAttributeValue());
                            }
                        }

                    }
                    // Add new components to operation
                    if (! opnSymbolComponents.isEmpty()){
                        if ( variant == TravelerVariant.VYPER ){
                            operation.getComponents().addAll(opnSymbolComponents);
                        }else{
                            operation.getComponents().removeAll(opnSymbolComponents);
                        }
                        symbolComponents.addAll(opnSymbolComponents);
                    }

                }
            }

            if ( variant == TravelerVariant.ATSS){
                // Add Header operation in subflow if not exists
                ApiTravelerOperation headerOperation = traveler.findOrCreateOperation("HEADER","HEADER");
                symbolComponents.forEach( component -> traveler.addComponentToOperation(headerOperation,component));
            }

        }
    }


    /**
     *
     * @param atssTravelerCreateRequest
     */
    public void createAtssTraveler(AtssTravelerCreateRequest atssTravelerCreateRequest) {
        log.debug("createAtssTraveler {}",atssTravelerCreateRequest);

        ProjectHeader projectHeader = projectHeaderService.validateUserByProjectId(atssTravelerCreateRequest.getProjectId());
        if ( projectHeader == null ){
            throw new AtssMassUploadException("Project ID not found "+atssTravelerCreateRequest.getProjectId());
        }

        if ( atssTravelerCreateRequest.getProjectDevices().isEmpty() ){
            throw new AtssMassUploadException("No devices selected for traveler "+atssTravelerCreateRequest.getProjectId());
        }

        // Manage CAMS creation first
        Map<String, Object> camsResult = manageComponentsUpdatesInAtss(atssTravelerCreateRequest.getProjectId());
        log.debug("CAMS Result {}", camsResult);

        projectDeviceLogService.createLog(atssTravelerCreateRequest.getProjectId(), camsResult);

        // If CAMS already exists , then proceed for Traveler creation
        if ( camsResult.getOrDefault("success", Boolean.FALSE).equals(Boolean.TRUE) ){

            // Check if cams SCN is active
            if (!specCamsService.isCamsScnActive(camsResult)){
                String scnDetails = "SCN1 ";
                if ( camsResult.get("newCamsScnId") != null ){
                    scnDetails += camsResult.get("newCamsScnId").toString();
                }
                if ( camsResult.get("updateCamsScnId") != null ){
                    scnDetails += " SCN2 "+ camsResult.get("updateCamsScnId").toString();
                }

                throw new AtssMassUploadException("Please try again. CAMS SCN(s) are being activated.  "+scnDetails);
            }

            // Create the traveler in ATSS
            List<ProjectDeviceForm> projectDeviceForAtss =
                    atssTravelerCreateRequest.getProjectDevices();
            if(atssTravelerCreateRequest.getAction().equalsIgnoreCase("ACTIVE_ATSS")) {


                // perform changelink validation when action "ACTIVE_ATSS" only if its vscn check says
                projectDeviceForAtss
                    .forEach(device -> projectDeviceVscnService.validateVscnForProduction(projectHeader, device));
            }
            List<String> projectDeviceIdsForAtss = projectDeviceForAtss.stream().map(ProjectDeviceForm::getId).collect(Collectors.toList());

            List<ProjectDevice> projectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projectHeader.getProjId());

            //Get Reference Traveler
            ApiTraveler refTraveler  = apiTravelerService.traveler(projectHeader.getRefVyperBuildNumber(),TravelerVariant.ATSS,false);

            projectDevices.stream()
                    .filter( projectDevice ->  projectDeviceIdsForAtss.contains(projectDevice.getId()))
                    .forEach( projectDevice -> {
                        projectDeviceLogService.createLog(projectDevice.getId(),camsResult);
                        ApiTraveler apiTraveler = getDeviceTravelerForAtss(projectHeader, refTraveler, projectDevice);
                        apiTraveler.setVyperBuildNumber(projectHeader.getProjId());
                        apiTraveler.setVyperScnNumber(projectDevice.getId());
                        apiTraveler.setSubmitter(projectHeader.getOwnerId());
                        apiTraveler.setScnNumber(projectDevice.getScnId());
                        apiTraveler.setFlagSubmitForSignOff(atssTravelerCreateRequest.getAction().equals("ACTIVE_ATSS"));
                        apiTraveler.setFlagMultiBuildDevice(projectDevice.getIsMultiBuild());
                        pushToAtssKafkaQueue(apiTraveler);
                        projectDevice.setStatus("Submitted");
                        projectDeviceRepository.save(projectDevice);
                    });

        }else{
            throw new AtssMassUploadException("CAMS Creation failed for traveler:"+camsResult );
        }
    }

    /**
     *
     * @param apiTraveler
     */
    private void pushToAtssKafkaQueue(ApiTraveler apiTraveler){
        kafkaAtssTopicProducer.send(apiTraveler);
    }

    /**
     * Update the response from ATSS
     * @param msgIn
     */
    public void processAtssResponse(KafkaAtssTopicResponse msgIn) throws JsonProcessingException {
        log.debug("kafka resp: {} {}", msgIn.getMessage(), msgIn.getValidations());
        String projectId = msgIn.getVyperBuildNumber();
        String projectDeviceId = msgIn.getVyperScnNumber();
        ProjectDevice projectDevice =
                projectDeviceRepository.findProjectDeviceByProjIdAndDeviceId(projectId, projectDeviceId);
        if ( projectDevice != null){
            projectDeviceLogService.createLog(projectDeviceId,msgIn);
            //set the spec device from atss api
            if ( msgIn.getSpecDevice() != null ){
                projectDevice.setSpecDevice(msgIn.getSpecDevice());
            }
            if (msgIn.isSuccess()){
                projectDevice.setScnId(String.valueOf(msgIn.getScnId()));
                projectDeviceService.refreshDeviceStatus(projectDevice);
            }else{
                projectDevice.setStatus("SCN Creation failed");
            }
            projectDeviceRepository.save(projectDevice);

            // Send email
            if (msgIn.isSuccess()){
                projectDeviceService.sendScnUpdateEmail(projectDevice);
            }
        }
    }

    private List<String> getSpecDeviceList( String projId){
        List<ProjectDevice> projectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projId);
        return projectDevices.stream().map(ProjectDevice::getSpecDevice).collect(Collectors.toList());
    }

    // Create the CAMS in ATSS
    private Map<String, Object> manageComponentsUpdatesInAtss(String projId) {
        List<DeviceComponent> camsUpdates = getCamsDiff(projId);
        List<String> specDeviceList = getSpecDeviceList(projId);
        return createOrUpdateComponentsInAtss(specDeviceList, camsUpdates);
    }

    public Map<String, Object> createOrUpdateComponentsInAtss(List<String> specDeviceList, List<DeviceComponent> camsUpdates){
        return specCamsService.createOrUpdateCams(specDeviceList, camsUpdates);
    }

    public Map<String, Object> createNewComponentsInAtss(String projId){
        return manageComponentsUpdatesInAtss(projId);
    }
    /**
     *
     * @param projId
     * @return List of device components
     */
    public List<DeviceComponent> getCamsDiff(String projId){
        log.debug("getCamsDiff");
        ProjectHeader projectHeader = projectHeaderService.validateUserByProjectId(projId);
        List<ProjectDevice> projectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projId);

        // Get the PRA or Build from Ref if exists
        String refNumber = projectHeader.getRefVyperPraNumber() ;
        if ( refNumber == null ) {
            refNumber = projectHeader.getRefVyperBuildNumber();
        }

        ApiTraveler referenceTraveler = new ApiTraveler();
        if ( refNumber != null ){
            //Ignore for Full flow spin
            //referenceTraveler = apiTravelerService.traveler( refNumber, TravelerVariant.ATSS, false);
            log.debug("Ref Traveler found");
        }

        // For each component entered by user, prepare the list of Cams array with attributes
        Map <String , DeviceComponent> uniqueCams = new HashMap<>();
        for ( ProjectDevice eachMaterial : projectDevices ){
            List<SpecChange> materialChanges = eachMaterial.getSpecChanges();
            materialChanges.forEach( specComp -> {
                //Add new components
                String nameValueKey = specComp.getComponentName()+specComp.getComponentValue();
                DeviceComponent deviceComponent = uniqueCams.get(nameValueKey);
                if ( deviceComponent != null || !uniqueCams.containsKey(nameValueKey)) {
                    deviceComponent =
                            prepareNewDeviceComponent(projectHeader.getFacilityAt(),
                                    specComp.getComponentName(),specComp.getComponentValue());
                }


                //Add Symbolization
                if (isSymbolAttribute(specComp)){
                    uniqueCams.put(specComp.getAttributeName()+specComp.getAttributeValue(),
                            prepareNewDeviceComponent(projectHeader.getFacilityAt(),
                                    specComp.getAttributeName(),
                                    specComp.getAttributeValue()));

                }else{
                    //Add new attributes
                    uniqueCams.put(nameValueKey, addAttributes(deviceComponent, specComp));
                }
            });
        }

        //Find the Unique component values from ref traveler and possibly replace the attributes

        //Merge with Reference
        if ( referenceTraveler != null ){
            for ( ApiTravelerSubflow subFlow : referenceTraveler.getSubFlows()){
                for ( ApiTravelerOperation operation: subFlow.getOperations()) {
                    for ( ApiTravelerComponent component : operation.getComponents()){
                        String compKey = component.getName()+component.getValue();

                        // Retrieve the other attributes from the reference for the component
                        // And update in the newly added components by project
                        if ( uniqueCams.containsKey(compKey)){
                            DeviceComponent deviceComponent  = uniqueCams.get(compKey);
                            // Add missing attributes from reference component
                            deviceComponent.getAttributes().addAll(
                                    component.getAttributes()
                                            .stream()
                                            .filter( attr -> deviceComponent.getAttributes().stream().noneMatch( exAttr -> exAttr.getName().equals(attr.getName()) ))
                                            .map( attr -> {
                                                return DeviceComponentAttribute.builder()
                                                        .name(attr.getName())
                                                        .value(attr.getValue())
                                                        .build();
                                            }).collect(Collectors.toSet()));
                            uniqueCams.put(compKey,deviceComponent);
                        }
                    }
                }
            }
        }

        return uniqueCams.values().stream().map(this::fixAttributeValues).collect(Collectors.toList());
    }

    /**
     * Can be configured later by DB table
     * @param specChange
     * @return
     */
    private boolean isSymbolAttribute ( SpecChange specChange){
        return specChange.getAttributeName() != null &&
                (
                specChange.getAttributeName().startsWith("CUST") ||
                        specChange.getAttributeName().startsWith("ECAT") );
    }
    private void addOperationComponents(Map<String , ApiTravelerComponent> uniqueCams, List<ApiTravelerOperation> flowOperations){
        flowOperations
                .forEach( operation -> {
                  operation.getComponents()
                          .forEach( component -> {
                                if (! uniqueCams.containsKey(component.getName()+component.getValue()) ){
                                    uniqueCams.put(component.getName()+component.getValue() , component);
                                }
                          });
                });
    }

    private DeviceComponent prepareNewDeviceComponent(String facility,String compName, String compValue){
        return DeviceComponent.builder()
                .name(compName)
                .value(compValue)
                .facility(facility)
                .attributes(new HashSet<DeviceComponentAttribute>())
                .build();
    }

    private DeviceComponent addAttributes( DeviceComponent deviceComponent,SpecChange specChange){
        if ( deviceComponent != null && specChange.getAttributeName() != null && specChange.getAttributeValue() != null){
            deviceComponent.getAttributes().add(
                    DeviceComponentAttribute.builder()
                            .name(specChange.getAttributeName())
                            .value(specChange.getAttributeValue())
                            .isQualified("N")
                            .build()
            );
        }
        return deviceComponent;
    }

    /**
     * Apply component specific business logic for attributes
     * @param deviceComponent
     * @return updated DeviceComponent
     */
    private DeviceComponent fixAttributeValues( DeviceComponent deviceComponent ){
        if ( deviceComponent != null){
            Map<String, Object> componentData =
                    atssComponentValidatorService.getAtssComponentData(deviceComponent.getFacility(), deviceComponent.getName(), deviceComponent.getValue());
            String componentType = (String) componentData.getOrDefault("componentType","INVALID");
            // Unknown component name
            if ( !componentType.equals("INVALID") ){

                deviceComponent.setComponentType(componentType);

                // Return true if component do not exist
                Boolean isComponentExists = (Boolean) componentData.getOrDefault("componentExists", Boolean.FALSE);
                deviceComponent.setExisting(isComponentExists);

                // Return false if it's Paragraph
                if ( componentType.equalsIgnoreCase("Paragraph") ){
                    return deviceComponent;
                }

                // Add attributes only if it do not present in ATSS
                if (!isComponentExists ){
                    if ( deviceComponent.getName().contains("Test Program") ){
                        deviceComponent = atssComponentValidatorService.manageTestPrograms(deviceComponent);
                    }else if (deviceComponent.getName().contains("Die")){
                        deviceComponent = atssComponentValidatorService.getDieAttributes(deviceComponent);
                    }
                }else{
                    // Remove attributes if they are same in ATSS
                    Map<String , Boolean> componentAttributes = ( (Map<String, Boolean>) componentData.get("componentAttributes"));
                    if ( componentAttributes != null && !deviceComponent.getAttributes().isEmpty()){
                        deviceComponent.getAttributes().removeIf( deviceComponentAttribute ->  componentAttributes.containsKey(deviceComponentAttribute.getName() + AtssComponentValidatorService.ATTRIBUTE_NAME_VALUE_SEPAROTOR + deviceComponentAttribute.getValue()));
                    }
                }
            }


        }
        return deviceComponent;
    }

    private ApiTravelerComponent createEmbedComponentsInHeader(ApiTraveler apiTraveler, ApiTravelerOperation operation, ApiTravelerComponent component, SpecChange specChange, String facility, TravelerVariant variant){

        if ( specChange.getComponentName().toUpperCase().contains("SYMBOL") ){
            // Move the attributes to Header
            ApiTravelerComponent symbolEmbeddedComponent = newApiTravelerComponent(specChange.getAttributeName(), specChange.getAttributeValue());

            Optional<ApiTravelerComponent> componentFound = operation.getComponents()
                .stream()
                .filter(cmp -> cmp.getName().equalsIgnoreCase(symbolEmbeddedComponent.getName())).findFirst();
            // Not present
            if(componentFound.isEmpty()) {
                return symbolEmbeddedComponent;
            }else {
                if(component.getParagraph() != null && componentFound.get().getValue() != null){
                     // Get new paragraph text based on component name and value from ATSS
                    Component comp = new Component();
                    comp.setName(component.getName());
                    comp.setValue(component.getValue());
                    comp.add(atssRepository.readParagraph(comp));
                    // Build propely formatted paragraph text with embed value
                    List<String> lines = travelerService.getLinesFromParagraphs(comp.getParagraphs(), null, specChange.getAttributeValue());
                    component.setParagraph(String.join("\n", lines));
                }
                componentFound.get().setValue(specChange.getAttributeValue());

                // ATSS Needs to move to Header and need to be deleted
                if ( variant == TravelerVariant.ATSS ){
                    return componentFound.get();
                }
            }
        }

        return null;
    }

    private ApiTravelerComponent newApiTravelerComponent(String name, String value){
        ApiTravelerComponent custComponent = new ApiTravelerComponent();
        custComponent.setName(name);
        custComponent.setValue(value.toUpperCase());
        custComponent.getAttributes().clear();
        return custComponent;
    }

    public List<ApiTravelerAttribute> getTestProgramAttributes(String componentName, String componentValue, String facility) {
        final List<ApiTravelerAttribute> attributes = new ArrayList<ApiTravelerAttribute>();
        Map<String, Object> componentData = atssComponentValidatorService.getAtssComponentData(facility, componentName, componentValue);
        Boolean isComponentExists = (Boolean) componentData.getOrDefault("componentExists", Boolean.FALSE);
        if(!isComponentExists) {
            attributes.addAll(atssComponentValidatorService.manageTestProgramsByComponentValue(componentValue));
        }else {
            attributes.addAll(getApiTravelerAttributesFromAtssComponentData(componentData));
        }

        return attributes;
    }

    public List<ApiTravelerAttribute> getDieAttributes(String componentName, String componentValue, String facility) {
        final List<ApiTravelerAttribute> attributes = new ArrayList<ApiTravelerAttribute>();
        Map<String, Object> componentData = atssComponentValidatorService.getAtssComponentData(facility, componentName, componentValue);
        Boolean isComponentExists = (Boolean) componentData.getOrDefault("componentExists", Boolean.FALSE);
        if(!isComponentExists) {
            attributes.addAll(atssComponentValidatorService.getDieAttributes(componentValue));
        }else {
            attributes.addAll(getApiTravelerAttributesFromAtssComponentData(componentData));
        }

        return attributes;
    }

    public void fixTravelerComponentAttributes(ApiTravelerComponent component, SpecChange change, String facility) {
        // Add die attributes from SAP if it is different from the reference
        if ( change.getComponentName().startsWith("Die")){
            component.getAttributes().clear();
            component.getAttributes().addAll(getDieAttributes(change.getComponentName(), change.getComponentValue(), facility));
        }
        if( change.getComponentName().startsWith("Test Program")) {
            component.getAttributes().clear();
            component.getAttributes().addAll(getTestProgramAttributes(change.getComponentName(), change.getComponentValue(), facility));
        }
        return ;
    }

    public List<ApiTravelerAttribute> getApiTravelerAttributesFromAtssComponentData(Map<String, Object> componentData) {
        final List<ApiTravelerAttribute> attributes = new ArrayList<ApiTravelerAttribute>();
        Map<String , Boolean> componentAttributes = ( (Map<String, Boolean>) componentData.get("componentAttributes"));
            componentAttributes.forEach((attr, isExists) -> {
                if(isExists) {
                    String[] keyValue = attr.split(AtssComponentValidatorService.ATTRIBUTE_NAME_VALUE_SEPAROTOR);
                    if(keyValue[1] != null)
                        attributes.add(new ApiTravelerAttribute(keyValue[0], keyValue[1]));
                }
            });
        return attributes;
    }
}