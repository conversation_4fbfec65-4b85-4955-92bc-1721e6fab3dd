package com.ti.specteam.vswr.dashboard.BatchProcessing.SCSWRService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.velocity.VelocityContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.specteam.vswr.dashboard.BatchProcessing.Repository.SCSWRDao;
import com.ti.specteam.vswr.dashboard.BatchProcessing.Repository.SpecDBDAO;
import com.ti.specteam.vswr.dashboard.domain.BpDiff;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellValueMap;
import com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailContext;
import com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailService;
import com.ti.util.directoryservices.EmployeeDirectory;
import com.ti.util.directoryservices.EmployeeInfo;

/**
 *
 * <AUTHOR>
 */
@Service
public class SCSWRServiceImpl implements SCSWRService {

  @Autowired
  private SCSWRDao scswrDao;
  @Autowired
  private SpecDBDAO specdbDao;
  @Autowired
  private BatchProcessingEmailService batchProcessingEmailService;
  @Autowired
  EmployeeDirectory employeeDirectory;

  public List<Map<String, String>> getColumns() {
    return scswrDao.getColumns();
  }

  public List<String> getATSites() {
    return scswrDao.getATSites();
  }

  public List<String> getSBE1() {
    return scswrDao.getSBE1();
  }

  public List<String> getSwrType() {
    return scswrDao.getSwrType();
  }

  public void delRedbullMax(String swrId, String userId) {
    scswrDao.delRedbullMax(swrId, userId);
  }

  public List<Map<String, String>> requestsColumns() {
    return scswrDao.requestsColumns();
  }

  public List<String> getMarketCat() {
    return scswrDao.getMarketCat();
  }

  public List<Map<String, Object>> getRequests(String userId, List<Map<String, String>> siteExt, String swrIds) {
    System.out.println("siteExtDwnld = " + siteExt);
    return scswrDao.getRequests(userId, siteExt, swrIds);
  }

  public void replaceRedbullRequest(Map scswrList) {
    String swrId = (String) scswrList.get("SWR_ID");
    String userId = (String) scswrList.get("UPLOADED_BY");
    // Clear out any associated redbull records so they can be replaced by the new
    // insert
    scswrDao.deleteRedbullRecords(swrId, userId);
    System.out.println("scswrList = " + scswrList);
    scswrDao.insertRequests(scswrList);
  }

  public int validateSwrId(String swrId) {
    return scswrDao.validateSwrId(swrId);
  }

  public List<Map<String, Object>> getUserSecurity(String userId) {
    return scswrDao.getUserSecurity(userId);
  }

  public int confirmAtSite(String atSite) {
    return scswrDao.confirmAtSite(atSite);
  }

  public int confirmSbeSite(String sbe1) {
    return scswrDao.confirmSbeSite(sbe1);
  }

  public void handleSwrStatusUpdate(String swrId, String changeReason, String uid, String swrUpdate, boolean isBatch) {
    String currentStatus = scswrDao.getCurrentStatusBySwrId(swrId);
    String newStatus;

    if (swrUpdate.equals(SWR_UPDATE_ACTION.VOID_SWR)) {
      newStatus = SWR_STATUS.VOID;
    } else if (swrUpdate.equals(SWR_UPDATE_ACTION.VOID_FCST_SWR)) {
      newStatus = SWR_STATUS.VOID_FORECAST;
    } else {
      if (currentStatus.startsWith("SBE_Forecast") ||
          currentStatus.startsWith("AT_Forecast") ||
          currentStatus.matches("^AT.*Forecast$")) {
        newStatus = SWR_STATUS.SBE_FORECAST_REVISED;
      } else if (currentStatus.contentEquals(SWR_STATUS.IN_SIGNOFF)) {
        newStatus = SWR_STATUS.EDIT;
      } else if (currentStatus.contentEquals(SWR_STATUS.DISAPPROVE)) {
        String signoffFlag = scswrDao.getSignoffFlagFromLdapRefBySwrId(swrId);
        if (signoffFlag.contentEquals(APPROVAL_TYPE.GROUP) || signoffFlag.contentEquals(APPROVAL_TYPE.SBE_1)
            || signoffFlag.contentEquals(APPROVAL_TYPE.ITSS_ID)) {
          newStatus = SWR_STATUS.IN_SIGNOFF;
        } else {
          newStatus = SWR_STATUS.SBE_SHIPPED_PAPERWORK;
        }
      } else {
        String signoffFlag = scswrDao.getSignoffFlagFromLdapRefBySwrId(swrId);
        if (swrUpdate.contentEquals(SWR_UPDATE_ACTION.SUBMIT_SWR) && currentStatus.contentEquals(SWR_STATUS.SAVED)
            && signoffFlag.contentEquals(APPROVAL_TYPE.SBE_1)) {
          newStatus = SWR_STATUS.IN_SIGNOFF;
        } else if (swrUpdate.contentEquals(SWR_UPDATE_ACTION.SUBMIT_SWR)
            && currentStatus.contentEquals(SWR_STATUS.SAVED))
          newStatus = SWR_STATUS.SBE_SHIPPED_PAPERWORK;
        else if (swrUpdate.contentEquals(SWR_UPDATE_ACTION.SUBMIT_SWR)
            && currentStatus.contentEquals(SWR_STATUS.DISAPPROVE))
          newStatus = SWR_STATUS.IN_SIGNOFF;
        else
          newStatus = currentStatus;
      }
    }

    Integer result = null;
    scswrDao.pkgSwrHistoryUpdateStatus(swrId, newStatus, null, changeReason, uid, result);

    // Send notification of change in status
    // Get the current swr info after all the updates
    Map<String, Object> swrInfo = this.getRequest(swrId);
    try {
      this.sendSuccessfulSwrUpdateMail(swrId, changeReason, swrInfo, uid, swrUpdate, isBatch);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public List<Map<String, Object>> getSbeView(String uid, String swrId) {
    return scswrDao.getSbeView(uid, swrId);
  }

  public List<String> getSwrIdUpdateList(String uid) {
    return scswrDao.getSwrIdUpdateList(uid);
  }

  public List<String> getAtCoordCatEmail(String atSite) {
    return scswrDao.getAtCoordCatEmail(atSite);
  }

  public Map<String, String> getLdapxref(String org) {
    return scswrDao.getLdapxref(org);
  }

  public List<Map<String, String>> checkSwrsNoForecast(String swrId, String status) {
    return scswrDao.checkSwrsNoForecast(swrId, status);
  }

  public void calculateDates(String swrId, String status) {
    scswrDao.calculateDates(swrId, status);
  }

  public void updateTarget(String targetDate, String swrId, String status) {
    scswrDao.updateTarget(targetDate, swrId, status);
  }

  public void updateForecastDates(String updatedDate, String swrId, String status) {
    scswrDao.updateForecastDates(updatedDate, swrId, status);
  }

  public void insertForecastDates(String updatedDate, String swrId,
      String status, String targetDate, String actualDate) {
    scswrDao.insertForecastDates(updatedDate, swrId, status, targetDate, actualDate);
  }

  public void updateEstDate(String atStartTargetDate, String swrId, String atShippedTargetDate) {
    scswrDao.updateEstDate(atStartTargetDate, swrId, atShippedTargetDate);
  }

  public Map<String, Object> getRequestRedbull(String swrId, String userId) {
    return scswrDao.getRequestRedbull(swrId, userId);
  }

  public Map<String, Object> getRequest(String swrId) {
    return scswrDao.getRequest(swrId);
  }

  public int validateDeviceName(String deviceName, String atSite) {
    return specdbDao.validateDeviceName(deviceName, atSite);
  }

  public String getPlantCode(String atSite) {
    return scswrDao.getPlantCode(atSite);
  }

  public int validateMatlSpecDevice(String deviceName, String specDevice) {
    return specdbDao.validateMatlSpecDevice(deviceName, specDevice);
  }

  public List<Map<String, Object>> swrListsByStat(String tableName, String swrStat,
      String submitSwrStat, DataTableParams params) {
    return scswrDao.swrListsByStat(tableName, swrStat, submitSwrStat, params);
  }

  public int countSwrListsByStat(String tableName, String swrStat,
      String submitSwrStat, DataTableParams params) {
    return scswrDao.countSwrListsByStat(tableName, swrStat, submitSwrStat, params);
  }
  
  public void createRequestsHistoryRecordByRequestRecord(String swrId){
    scswrDao.createRequestsHistoryRecordByRequestRecord(swrId);
  }

  @Override
  @Transactional
  public void proceedUpdateRequest(List<String> swrIds, String userId, String changeReason, String swrUpdate) {
    // Update each swr
    for (String swrId : swrIds) {
      // Handle forecast Updates
      Map<String, Object> swrInfo = this.getRequestRedbull(swrId, userId);
      boolean hasARedbullRecord = swrInfo != null;
      if (!hasARedbullRecord) {
        swrInfo = this.getRequest(swrId);
      }
      try {
        if (!StringUtils.equals(swrUpdate, SWR_UPDATE_ACTION.VOID_SWR)
            && !StringUtils.equals(swrUpdate, SWR_UPDATE_ACTION.VOID_FCST_SWR)) {
          this.updateForecastDates(swrInfo, swrId);
        }
      } catch (Exception e) {
        e.printStackTrace();
        throw new RuntimeException("Failed to update forecasts for swrId=" + swrId, e);
      }

      // Record history for the change
      scswrDao.createRequestsHistoryRecordByRequestRecord(swrId);

      // If this included a redbull record
      if (hasARedbullRecord) {
        // Update the request with the staged redbull changes
        scswrDao.mergeRequestsRedbullIntoRequestsBySwrIdAndUserId(swrId, userId);
        // Clear the staged records accordingly
        scswrDao.deleteRedbullRecords(swrId, userId);
      }

      // Handle logic for changing the Swr's status
      try {
        this.handleSwrStatusUpdate(
            swrId,
            changeReason + " [Revised though the SCSWR Batch Processing Tool]",
            userId,
            swrUpdate,
            true
            );

      } catch (Exception e) {
        e.printStackTrace();
        throw new RuntimeException("Failed To Update Table: id=" + swrId, e);
      }
    }
  }

  public void sendSuccessfulSwrUpdateMail(String swrId, String changeReason, Map<String, Object> swrInfo, String userId,
      String swrUpdate, boolean isBatch)
      throws Exception {
    BatchProcessingEmailContext batchProcessingEmailContext = new BatchProcessingEmailContext();
    batchProcessingEmailContext.setTemplate(
        "com/ti/specteam/vswr/dashboard/BatchProcessing/templates/successfulSwrUpdate.vm");

    // Build velocity context
    VelocityContext velocityContext = new VelocityContext();
    velocityContext.put("swrId", swrId);
    velocityContext.put("changeReason", changeReason);
    velocityContext.put("swrUpdate", swrUpdate);
    velocityContext.put("swrInfo", swrInfo);
    velocityContext.put("isBatch", isBatch);
    velocityContext.put("requestorName",
        employeeDirectory.getEmployeeById(swrInfo.get("REQUESTOR_EMPID").toString()).get(EmployeeInfo.COMMON_NAME));

    EmployeeInfo currentUser = employeeDirectory.getEmployeeById(userId);
    velocityContext.put("userName", currentUser.get(EmployeeInfo.COMMON_NAME));

    batchProcessingEmailContext.setVelocityContext(velocityContext);

    // Set tos
    String userEmail = currentUser.get(EmployeeInfo.EMAIL_ALIAS);
    List<String> coordEmail = this.getAtCoordCatEmail(swrInfo.get("ATSITE").toString());
    batchProcessingEmailContext.getTos().add(userEmail);
    batchProcessingEmailContext.getTos().addAll(coordEmail);

    // Set subject
    batchProcessingEmailContext
        .setSubject("SWR ID (" + swrInfo.get("SBE1SITE") + ") " + swrId + " " + swrInfo.get("DEVICE_NAME")
            + " " + swrInfo.get("CURRENT_STATUS") + " Pin:" + swrInfo.get("PIN")
            + " Pkg:" + swrInfo.get("PKG"));

    // Send email
    batchProcessingEmailService.send(batchProcessingEmailContext);
  }

  public void updateForecastDates(Map<String, Object> swrInfo, String swrId) {
    List<Map<String, String>> swrForecastDates = this.checkSwrsNoForecast(swrId, null);
    String updated, target, actual;
    updated = swrInfo.get("NEED_DATE") != null ? swrInfo.get("NEED_DATE").toString() : "";
    target = actual = "";
    for (Map<String, String> map : swrForecastDates) {
      if ("SBE_Shipped_Paperwork".equals(map.get("STATUS"))) {
        updated = "".equals(updated) ? map.get("UPDATED") : updated;
        target = map.get("TARGET");
        actual = map.get("ACTUAL");
      }
    }
    List<String> status = Arrays.asList("SBE_Forecast_Submitted", "SBE_Shipped_Paperwork", "AT_Started", "AT_Shipped");
    if (!swrForecastDates.isEmpty()) {

      if ((updated != null && !"".equals(updated))) {
        this.updateForecastDates(updated, swrId, "SBE_Shipped_Paperwork");
      }
      if (target != null && !"".equals(target)) {
        if (!("Risk Production".equals(swrInfo.get("SWR_TYPE")) && "A/T Only".equals(swrInfo.get("SWR_TYPE")))) {
          this.calculateDates(swrId, "SBE_Shipped_Paperwork");
          this.calculateDates(swrId, "AT_Started");
        }
      }
    } else {
      if ("Risk Production".equals(swrInfo.get("SWR_TYPE")) || "A/T Only".equals(swrInfo.get("SWR_TYPE"))) {
        for (String stat : status) {
          if ("SBE_Shipped_Paperwork".equals(stat)) {
            this.insertForecastDates(updated, swrId, stat, target, actual);
          } else {
            this.insertForecastDates("", swrId, stat, "", "");
          }
        }
        if ((updated == null || "".equals(updated)) && (target != null && !"".equals(target))) {
          this.updateForecastDates(updated, swrId, "SBE_Shipped_Paperwork");
        } else {
          this.updateTarget(target, swrId, "SBE_Shipped_Paperwork");
        }
      } else {
        if (updated != null && !"".equals(updated)) {
          if (!swrForecastDates.isEmpty()) {
            for (String stat : status) {
              if ("SBE_Shipped_Paperwork".equals(stat)) {
                this.insertForecastDates(updated, swrId, stat, target, actual);
              } else {
                this.insertForecastDates("", swrId, stat, "", "");
              }
            }
          } else if (target != null && !"".equals(target)) {
            this.updateForecastDates(updated, swrId, "SBE_Shipped_Paperwork");
            this.calculateDates(swrId, "SBE_Shipped_Paperwork");
            this.calculateDates(swrId, "AT_Started");
          }
        }
      }
    }

    if ("Risk Production".equals(swrInfo.get("SWR_TYPE")) || "A/T Only".equals(swrInfo.get("SWR_TYPE"))) {
      String atStartedTarget = "";
      String atShippedTarget = "";
      for (Map<String, String> map : swrForecastDates) {
        if ("AT_Started".equals(map.get("STATUS"))) {
          atStartedTarget = map.get("TARGET");
        }
      }
      if (atStartedTarget != null && !"".equals(atStartedTarget)) {
        this.updateTarget(atStartedTarget, swrId, "AT_Started");
      }
      for (Map<String, String> map : swrForecastDates) {
        if ("AT_Shipped".equals(map.get("STATUS"))) {
          atShippedTarget = map.get("TARGET");
        }
      }
      if (atShippedTarget != null && !"".equals(atShippedTarget)) {
        this.updateTarget(atShippedTarget, swrId, "AT_Shipped");
      }
      this.updateEstDate(atStartedTarget, swrId, atShippedTarget);
    }
  }

  public List<ScswrExcelCellConfig> getSbeExcelCfgs() {
    List<ScswrExcelCellConfig> sbeExcelCfgs = scswrDao.getSbeExcelCfgs();
    sbeExcelCfgs.forEach(cfg->{
      this.populatePossibleValuesFromSql(cfg);
    });
    return sbeExcelCfgs;
  }
  public List<ScswrExcelCellConfig> getConstrainedSbeExcelConfigs() {
    List<ScswrExcelCellConfig> sbeExcelCfgs = scswrDao.getConstrainedSbeExcelConfigs();
    sbeExcelCfgs.forEach(cfg->{
      this.populatePossibleValuesFromSql(cfg);
    });
    return sbeExcelCfgs;
  }
  public ScswrExcelCellConfig getSbeExcelCfgByExcelLabel(String excelLabel) {
    ScswrExcelCellConfig excelConfig = scswrDao.getSbeExcelCfgByExcelLabel(excelLabel);
    if(excelConfig == null) {
      throw new NoSuchElementException("Config was not found with label: " + excelLabel);
    }
    this.populatePossibleValuesFromSql(excelConfig);
    return excelConfig;
  }

  private void populatePossibleValuesFromSql(ScswrExcelCellConfig scswrExcelCellConfig) {
    List<ScswrExcelCellValueMap> possibleValues = scswrExcelCellConfig.getPossibleValues();
    if (scswrExcelCellConfig.isConstrainedBySqlValues()) {
      try {
        possibleValues =
            this.getDropdownValuesFromSql(scswrExcelCellConfig.getSqlValueSource());
      } catch (Exception e) {
        e.printStackTrace();
        throw new RuntimeException(
            "Invalid sql found for dropdown values for: " + scswrExcelCellConfig.getExcelLabel());
      }
    }
    scswrExcelCellConfig.setPossibleValues(possibleValues);
  }


  public List<ScswrExcelCellValueMap> getDropdownValuesFromSql(String sqlValueSource) {
    return scswrDao.getDropdownValuesFromSql(sqlValueSource).stream()
        .map(
            value ->
                ScswrExcelCellValueMap.builder().databaseValue(value).excelValue(value).build())
        .collect(Collectors.toList());
  }

  @Override
  public List<BpDiff> getBpDiffs(String uploadedBy, String swrId) {
      List<String> bpDiffColumns = scswrDao.getBpDiffColumns();
      return scswrDao.getBpDiffs(uploadedBy, swrId, bpDiffColumns);
  }

  @Override
  public boolean isBpDiff(String uploadedBy, String swrId) {
      List<String> bpDiffColumns = scswrDao.getBpDiffColumns();
      return scswrDao.isBpDiff(uploadedBy, swrId, bpDiffColumns);
  }

}
