package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;


@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangePraComponentForm extends PraNumberForm {

    @NotNull
    @Size(min=1)
    private String name;

    @NotNull
    @Size(min=1)
    private String oldValue;

    @NotNull
    @Size(min=1)
    private String newValue;

//    public String display() {
//
//        List<String> names = new ArrayList<>();
//
//        for (ComponentInstance i : component.getInstances()) {
//            for (ComponentPriority p : i.getPriorities()) {
//                names.add(p.getValue());
//            }
//        }
//
//        return String.join(", ", names);
//    }

}
