package com.ti.specteam.atssmassupload.exception;

import com.ti.specteam.vyper.vscn.model.AtssFieldError;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MassUploadApiError {

    private int status;
    private String message;
    private List<AtssFieldError> fieldErrors = new ArrayList<>();

    public MassUploadApiError(int status, String message, List<AtssFieldError> fieldErrors) {
        this.status = status;
        this.message = message;
        this.fieldErrors.addAll(fieldErrors);
    }

    public void addFieldError(AtssFieldError fieldError) {
        fieldErrors.add(fieldError);
    }
}
