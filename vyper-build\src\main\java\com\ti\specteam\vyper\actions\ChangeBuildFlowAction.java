package com.ti.specteam.vyper.actions;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.ti.specteam.vyper.build.model.SystemName;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.config.deviceflow.DeviceFlowsConfig;
import com.ti.specteam.vyper.config.deviceflow.DeviceFlowsConfigService;
import com.ti.specteam.vyper.config.flowopn.DeviceFlowOperation;
import com.ti.specteam.vyper.config.flowopnconfig.FlowOpnsConfig;
import com.ti.specteam.vyper.config.flowopnconfig.FlowOpnsConfigService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.validate.ValidateService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChangeBuildFlowAction {
    
    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final DeviceFlowsConfigService deviceFlowsConfigService;
    private final FlowOpnsConfigService flowOpnsConfigService;

    public Build execute(Vyper vyper, Build build, String flowName, String symbolChoice) {
        if (StringUtils.isBlank(flowName)) throw new VyperException("The flowName cannot be empty.");

        DeviceFlowsConfig flow = deviceFlowsConfigService.findByFlowName(flowName);
        build.getBuildFlow().setFlowId(flow.getId());
        build.getBuildFlow().setFlowName(flow.getFlowName());
        build.getBuildFlow().setSymbolChoice(symbolChoice);
        
        List<FlowOpnsConfig> flowConfig = flowOpnsConfigService.getAllOperationsByFlowId( flow.getId());
        List<DeviceFlowOperation> flowRows = flowConfig.stream()
                .map( FlowOpnsConfig::getOperation )
                .collect( Collectors.toList());

        build.getBuildFlow().setFlowRows(flowRows);

        return build;
    }
}
