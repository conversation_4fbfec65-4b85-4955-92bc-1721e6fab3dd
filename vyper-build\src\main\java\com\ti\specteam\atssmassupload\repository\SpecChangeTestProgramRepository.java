package com.ti.specteam.atssmassupload.repository;

import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import com.ti.specteam.atssmassupload.domain.SpecChangeTestProgram;

import lombok.RequiredArgsConstructor;

/**
 *
 * <AUTHOR>
 */

@Repository
@RequiredArgsConstructor
public class SpecChangeTestProgramRepository {

	private final NamedParameterJdbcTemplate jdbcTemplate;

	public List<SpecChangeTestProgram> specChangeTestProgram(List<String> programNames) {
		String sql = "SELECT PROGRAM_NAME,PGM_STATUS,PROGRAM_REV from spec.test_program_info_vw@specdb  "
				+ "where PROGRAM_NAME IN (:programNames) AND (PGM_STATUS='Active' OR PGM_STATUS='In Work')";
		return jdbcTemplate.query(sql, new MapSqlParameterSource("programNames", programNames),
				BeanPropertyRowMapper.newInstance(SpecChangeTestProgram.class));

	};

	public SpecChangeTestProgram getTestProgramLatestRevision(String programName) {
		String sql = "SELECT * from spec.test_program_info_vw@specdb  "
				+ "where PROGRAM_NAME = :programName AND PGM_STATUS='Active' " + " AND ROWNUM < 2";
		return jdbcTemplate.queryForObject(sql, new MapSqlParameterSource("programName", programName),
				BeanPropertyRowMapper.newInstance(SpecChangeTestProgram.class));

	};

	public boolean activeTestProgramAndRevision(String programName, String programRevision) {
		String sql = "select 1 from  spec.test_program_info_vw@specdb where PROGRAM_NAME = :programName \n" +
				"AND PROGRAM_REV = :programRevision and PGM_STATUS = 'Active'";

		List<String> cValues = jdbcTemplate.query(
				sql,
				new MapSqlParameterSource()
						.addValue("programName", programName)
						.addValue("programRevision",programRevision),
				new SingleColumnRowMapper<>());

		return !cValues.isEmpty();

	};

}