package com.ti.specteam.vswr.vswr.web;

import org.springframework.web.bind.annotation.RestController;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.VSCSWRForm;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.service.VSCSWRService;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.security.access.prepost.PreAuthorize;

import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;

@RestController
@RequiredArgsConstructor
@Validated
@Slf4j
@RequestMapping("/v1/VSCSWR")
@PreAuthorize("@externalAuthCheck.validateUser()")
public class VSCSWRController {

    private final VSCSWRService vscswrService;

    @PostMapping("/pushExistingToScswr")
    public ResponseEntity<String> pushExistingToScswr(@RequestBody ATSWRForm atswrForm){
        log.info("pushExistingToScswr updateForm from VSCSWRController");
        try {
            return ResponseEntity.ok(vscswrService.pushExistingToScswr(atswrForm));
        } catch (Exception e) {
            log.error(e.toString());
            return new ResponseEntity<String>("Error. Please contact IT.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/pushNewToScswr")
    public ResponseEntity<GeneralInfo> pushNewToScswr(@RequestBody ATSWRForm atswrForm){
        log.info("pushNewToScswr updateForm from VSCSWRController");
        try {
            return ResponseEntity.ok(vscswrService.pushNewToScswr(atswrForm));
        } catch (Exception e) {
            log.error(e.toString());
            return new ResponseEntity<GeneralInfo>(new GeneralInfo(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/mapVswrToScswr")
    public ResponseEntity<VSCSWRForm> mapVswrToScswr(@RequestBody ATSWRForm atswrForm){
        log.info("mapVswrToScswr updateForm from VSCSWRController");
        try {
            if (atswrForm == null) {
                return new ResponseEntity<>(new VSCSWRForm(), HttpStatus.BAD_REQUEST);
            }

            if (atswrForm.getDieInfo() == null) {
                atswrForm.setDieInfo(new ArrayList<>()); // Prevents backend crash
            }
            return ResponseEntity.ok(vscswrService.mapVswrToScswr(atswrForm));
        } catch (Exception e) {
            log.error(e.toString());
            return new ResponseEntity<VSCSWRForm>(new VSCSWRForm(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    @GetMapping("/fetchExistingScswr/{swrID}")
    public ResponseEntity<VSCSWRForm> fetchExistingScswr(@PathVariable ("swrID") String swrID){
        log.info("mapVswrToScswr fetchExistingScswr from VSCSWRController");
        try {
            return ResponseEntity.ok(vscswrService.fetchExistingScswr(swrID));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new VSCSWRForm());
        }
    }
    
    @GetMapping("/isScswrStatusBlocked/{swrID}")
    public ResponseEntity<String> isScswrStatusBlockedReason(@PathVariable ("swrID") String swrID){
        log.info("mapVswrToScswr isScswrStatusBlockedReason from VSCSWRController");
        try {
            return ResponseEntity.ok(vscswrService.isScswrStatusBlockedReason(swrID));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok("Error retrieving status");
        }
    }
}