<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.vswr.repository.BDWDao">

    <resultMap id="RequestorInfoMap" type="com.ti.specteam.vswr.vswr.domain.RequestorInfo">
        <result property="itssID" column="ITSS_ID"/>
        <result property="name" column="COMMON_NAME"/>
        <result property="phone" column="WORK_PHONE"/>
        <result property="email" column="EMAIL_ADDR_DESC"/>
        <result property="costCenter" column="CC"/>
        <result property="group" column="TI_GRP_DESCR"/>
    </resultMap>

    <select id="fetchRequestorInfo" resultMap="RequestorInfoMap">
        SELECT
            ITSS_ID,
            COMMON_NAME,
            EMAIL_ADDR_DESC,
            REPLACE(WORK_PHONE, '-', '') WORK_PHONE,
            CC,
            TI_GRP_DESCR
        FROM <EMAIL>
        WHERE UPPER(ITSS_ID) = #{aid}
    </select>

    <select id="fetchWwid" resultType="String">
        SELECT WWID from adhoc.UV_MATL_DETAIL@BDW WHERE OLD_MATERIAL = #{oldMaterial}
    </select>

    <select id="fetchMcm" resultType="String">
        SELECT ENTRPT.GET_MCM_BOM_FLAG@BDW(#{oldMaterial}) FROM DUAL
    </select>

    <select id="fetchIso" resultType="String">
        SELECT NVL(<EMAIL>(#{oldMaterial}), 'N') FROM DUAL
    </select>

    <select id="fetchApl" resultType="String" timeout="5">
        SELECT ENTRPT.GET_APL_FLAG@BDW(#{oldMaterial}) FROM DUAL
    </select>

    
</mapper>
