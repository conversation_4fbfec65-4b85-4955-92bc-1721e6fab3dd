package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.SubFlow;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_TURNKEY;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeTurnkeyAction {
    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;

    public Build execute(ChangeTurnkeyForm changeTurnkeyForm) {
        log.debug("execute(changeTurnkeyForm:{})", changeTurnkeyForm);

        Vyper vyper = vyperService.fetchVyper(changeTurnkeyForm);
        Build build = buildService.fetchBuild(changeTurnkeyForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getTurnkey().setValue(changeTurnkeyForm.getValue());
        build.getTurnkey().getSource().appointUser(securityService.user());

        if(StringUtils.equalsIgnoreCase(build.getBuildFlowName(),"TKY") && StringUtils.equalsIgnoreCase(changeTurnkeyForm.getValue(),"NON-TKY")){
            build.getTest().setContent(null);
            build.getTest().getTravelerOperations().clear();
            build.getFlow().getObject().getOperations().removeIf(flowOperation -> flowOperation.getSubFlow() != null && flowOperation.getSubFlow().equals(SubFlow.TEST));
        }

        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_TURNKEY,
                "changed turnkey to: " + changeTurnkeyForm.getValue()
        );

        return buildService.saveBuild(build);
    }
}
