package com.ti.specteam.vswr.vswr.domain;


import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VSCSWRForm {
    private String swrID;                   //SWR_ID
    private String currentStatus;           //CURRENT_STATUS
    private String title;                   //TITLE
    private String atSite;                  //ATSITE
    private String plantCode;
    private String sbe1;                    //SBE1SITE
    private String profitCenter;            //PROFCNTR
    private String costCenter;              //COST_CENTER
    private String io;                      //CHARGE
    private String itssID;                  //REQUESTOR_EMPID
    private String email;                   //REQUESTOR_EMAIL
    private String name;                    //REQUESTOR_NAME
    private String phone;                   //REQUESTOR_PHONE
    private String requestDate;             //REQUEST_DATE
    private String sapMaterial;             //DEVICE_NAME
    private String sapBaseMaterial;         //PARENT_DEVICE                 
    private String pin;                     //PIN
    private String pkg;                     //PKG
    private String specDevice;              //PTSS_TRAVEL
    private String purpose;                 //COMMENT_PURPOSE
    private String generalComment;          //COMMENT_REQ
    private String pkgGroup;                //PACKCLASS
    private String industrySector;          //MARKET_CATEGORY
    private String lineItem;                //LINE_ITEM
    private String apl;                     //IS_APL
    @Builder.Default
    private String mcm = "";                //IS_MCM
    private String iso;                     //ISO_VALUE

    private String dieLot1;                 //DIE_LOT
    private String dieName1;                //DIE_NAME
    private String dieRev1;                 //DIE_REV
    private String dieDesignator1;          //DIE_DESIGNATOR
    private String dieSize1;                //DIE_SIZE
    private String scribeWidth1;            //SCRIBEWIDTH
    private String fabCode1;                //FAB_CODE
    private String fabTech1;                //FABTECH
    private String waferDiameter1;          //WAFER_DIAMETER
    private String waferThickness1;         //WAFER_THICKNESS
    private String backgrind1;              //BACKGRIND
    
    @Builder.Default
    private String dualDie = "n";           //DUALDIE
    @Builder.Default
    private String dieLot2 = "";            //DDIE_LOT
    private String dieName2;                //DDIENAME
    private String dieRev2;                 //DDIEREV
    private String dieDesignator2;          //DDIEDSG
    private String dieSize2;                //DDIESIZE
    private String scribeWidth2;            //DSCRIBEWIDTH
    private String fabCode2;                //DDFABCODE
    private String fabTech2;                //DFABTECH
    private String waferDiameter2;          //DWAFER_DIAMETER
    private String waferThickness2;         //DWAFER_THICKNESS
    private String backgrind2;              //DBACKGRIND

    @Builder.Default
    private String tripleDie = "n";         //TRIPLEDIE
    @Builder.Default
    private String dieLot3 = "";            //TDIE_LOT
    private String dieName3;                //TDIENAME
    private String dieRev3;                 //TDIEREV
    private String dieDesignator3;          //TDIEDSG
    private String dieSize3;                //TDIESIZE
    private String scribeWidth3;            //TSCRIBEWIDTH
    private String fabCode3;                //TDFABCODE
    private String fabTech3;                //TFABTECH
    private String waferDiameter3;          //TWAFER_DIAMETER
    private String waferThickness3;         //TWAFER_THICKNESS
    private String backgrind3;              //TBACKGRIND

    @Builder.Default
    private String quadDie = "n";           //QUADDIE
    @Builder.Default
    private String dieLot4 = "";            //FODIE_LOT
    private String dieName4;                //FODIENAME
    private String dieRev4;                 //FODIEREV
    private String dieDesignator4;          //FODIEDSG
    private String dieSize4;                //FODIESIZE
    private String scribeWidth4;            //FOSCRIBEWIDTH
    private String fabCode4;                //FODFABCODE
    private String fabTech4;                //FOFABTECH
    private String waferDiameter4;          //FOWAFER_DIAMETER
    private String waferThickness4;         //FOWAFER_THICKNESS
    private String backgrind4;              //FOBACKGRIND

    @Builder.Default
    private String quintDie = "n";          //QUINTDIE
    @Builder.Default
    private String dieLot5 = "";            //FIDIE_LOT
    private String dieName5;                //FIDIENAME
    private String dieRev5;                 //FIDIEREV
    private String dieDesignator5;          //FIDIEDSG
    private String dieSize5;                //FIDIESIZE
    private String scribeWidth5;            //FISCRIBEWIDTH
    private String fabCode5;                //FIDFABCODE
    private String fabTech5;                //FIFABTECH
    private String waferDiameter5;          //FIWAFER_DIAMETER
    private String waferThickness5;         //FIWAFER_THICKNESS
    private String backgrind5;              //FIBACKGRIND

    @Builder.Default
    private String travelerStatus="vyper";  //TRAVEL_STAT
    @Builder.Default
    private String assemblyReq="yes";       //ASYREQ
    private String shipType;                //SHIP_TYPE
    private String deliveryNote;            //SBEDELNOTE
    private String dateShipped;             //DATESHIP
    private String invoice;                 //SBEINVOICE
    private String qtyToShip;               //GEC_QTY
    private String sapWaybill;              //SBEWAYBILL
    
    // bom -> assembly req
    @Builder.Default
    private String header = "N/A";          //HEADER1
    @Builder.Default
    private String leadFrame = "N/A";       //LEADFRAMES1
    @Builder.Default
    private String moldCompound = "N/A";    //MOLD_COMPOUND1
    @Builder.Default
    private String mountCompound = "N/A";   //MOUNT_COMPOUND1
    @Builder.Default
    private String wire = "N/A";            //WIRE
    @Builder.Default
    private String solder = "N/A";          //SOLDER
    @Builder.Default
    private String lid = "N/A";             //LID1
    @Builder.Default
    private String chipCap = "N/A";         //CHIPCAP

    @Builder.Default
    private String bumpReq = "n";           //BUMPREQ
    
    @Builder.Default
    private String wireDiameter = "";            //WIRE_DIAMETER    

    public void merge(VSCSWRForm vscswrForm){
        this.swrID = vscswrForm.swrID == null ? this.swrID : vscswrForm.swrID;
        this.currentStatus = vscswrForm.currentStatus == null ? this.currentStatus : vscswrForm.currentStatus;
        this.title = vscswrForm.title == null ? this.title : vscswrForm.title;
        this.atSite = vscswrForm.atSite == null ? this.atSite : vscswrForm.atSite;
        this.sbe1 = vscswrForm.sbe1 == null ? this.sbe1 : vscswrForm.sbe1;
        this.profitCenter = vscswrForm.profitCenter == null ? this.profitCenter : vscswrForm.profitCenter;
        this.costCenter = vscswrForm.costCenter == null ? this.costCenter : vscswrForm.costCenter;
        this.io = vscswrForm.io == null ? this.io : vscswrForm.io;
        this.itssID = vscswrForm.itssID == null ? this.itssID : vscswrForm.itssID;
        this.email = vscswrForm.email == null ? this.email : vscswrForm.email;
        this.name = vscswrForm.name == null ? this.name : vscswrForm.name;
        this.phone = vscswrForm.phone == null ? this.phone : vscswrForm.phone;
        this.requestDate = vscswrForm.requestDate == null ? this.requestDate : vscswrForm.requestDate;
        this.sapMaterial = vscswrForm.sapMaterial == null ? this.sapMaterial : vscswrForm.sapMaterial;
        this.sapBaseMaterial = vscswrForm.sapBaseMaterial == null ? this.sapBaseMaterial : vscswrForm.sapBaseMaterial;
        this.pin = vscswrForm.pin == null ? this.pin : vscswrForm.pin;
        this.pkg = vscswrForm.pkg == null ? this.pkg : vscswrForm.pkg;
        this.specDevice = vscswrForm.specDevice == null ? this.specDevice : vscswrForm.specDevice;
        this.purpose = vscswrForm.purpose == null ? this.purpose : vscswrForm.purpose;
        this.pkgGroup = vscswrForm.pkgGroup == null ? this.pkgGroup : vscswrForm.pkgGroup;
        this.industrySector = vscswrForm.industrySector == null ? this.industrySector : vscswrForm.industrySector;
        this.lineItem = vscswrForm.lineItem == null ? this.lineItem : vscswrForm.lineItem;
        this.apl = vscswrForm.apl == null ? this.apl : vscswrForm.apl;
        this.mcm = vscswrForm.mcm == null ? this.mcm : vscswrForm.mcm;
        this.iso = vscswrForm.iso == null ? this.iso : vscswrForm.iso;
    }

    public void addDieInfo(int dieNum, DieInfo dieInfo, String dieLots){
        switch(dieNum){
            case 1:
                this.dieLot1 = dieLots;
                this.dieName1 = dieInfo.getMatlMasterDieName();
                this.dieRev1 = dieInfo.getDieRev();
                this.dieDesignator1 = dieInfo.getDieDesignator();
                this.dieSize1 = dieInfo.getDieSize();
                this.scribeWidth1 = dieInfo.getScribeWidth();
                this.fabCode1 = dieInfo.getFabCode();
                this.fabTech1 = dieInfo.getFabTechnology();
                this.waferDiameter1 = dieInfo.getWaferDiameter();
                this.waferThickness1 = dieInfo.getWaferThickness();
                this.backgrind1 = dieInfo.getBackgrindThickness();
                break;
            case 2:
                this.dualDie = "y";
                this.dieLot2 = dieLots;
                this.dieName2 = dieInfo.getMatlMasterDieName();
                this.dieRev2 = dieInfo.getDieRev();
                this.dieDesignator2 = dieInfo.getDieDesignator();
                this.dieSize2 = dieInfo.getDieSize();
                this.scribeWidth2 = dieInfo.getScribeWidth();
                this.fabCode2 = dieInfo.getFabCode();
                this.fabTech2 = dieInfo.getFabTechnology();
                this.waferDiameter2 = dieInfo.getWaferDiameter();
                this.waferThickness2 = dieInfo.getWaferThickness();
                this.backgrind2 = dieInfo.getBackgrindThickness();
                break;
            case 3:
                this.tripleDie = "y";
                this.dieLot3 = dieLots;
                this.dieName3 = dieInfo.getMatlMasterDieName();
                this.dieRev3 = dieInfo.getDieRev();
                this.dieDesignator3 = dieInfo.getDieDesignator();
                this.dieSize3 = dieInfo.getDieSize();
                this.scribeWidth3 = dieInfo.getScribeWidth();
                this.fabCode3 = dieInfo.getFabCode();
                this.fabTech3 = dieInfo.getFabTechnology();
                this.waferDiameter3 = dieInfo.getWaferDiameter();
                this.waferThickness3 = dieInfo.getWaferThickness();
                this.backgrind3 = dieInfo.getBackgrindThickness();
                break;
            case 4:
                this.quadDie = "y";
                this.dieLot4 = dieLots;
                this.dieName4 = dieInfo.getMatlMasterDieName();
                this.dieRev4 = dieInfo.getDieRev();
                this.dieDesignator4 = dieInfo.getDieDesignator();
                this.dieSize4 = dieInfo.getDieSize();
                this.scribeWidth4 = dieInfo.getScribeWidth();
                this.fabCode4 = dieInfo.getFabCode();
                this.fabTech4 = dieInfo.getFabTechnology();
                this.waferDiameter4 = dieInfo.getWaferDiameter();
                this.waferThickness4 = dieInfo.getWaferThickness();
                this.backgrind4 = dieInfo.getBackgrindThickness();
                break;
            case 5:
                this.quintDie = "y";
                this.dieLot5 = dieLots;
                this.dieName5 = dieInfo.getMatlMasterDieName();
                this.dieRev5 = dieInfo.getDieRev();
                this.dieDesignator5 = dieInfo.getDieDesignator();
                this.dieSize5 = dieInfo.getDieSize();
                this.scribeWidth5 = dieInfo.getScribeWidth();
                this.fabCode5 = dieInfo.getFabCode();
                this.fabTech5 = dieInfo.getFabTechnology();
                this.waferDiameter5 = dieInfo.getWaferDiameter();
                this.waferThickness5 = dieInfo.getWaferThickness();
                this.backgrind5 = dieInfo.getBackgrindThickness();
                break;
        }
    }
    
    public void addDieLotInfo(DieLotInfo dieLotInfo){
        this.shipType = dieLotInfo.getMatShipStatus();
        
        if(!"direct".equals(this.shipType)){
            return;
        }

        this.deliveryNote = dieLotInfo.getDeliveryNote();
        this.dateShipped = dieLotInfo.getDateShipped();
        this.invoice = dieLotInfo.getInvoice();
        this.qtyToShip = dieLotInfo.getQtyToShip();
        this.sapWaybill = dieLotInfo.getSapWaybill();
    }

    public void addAssyInfo(List<MaterialInfo> bomInfoList){
        for(MaterialInfo bomInfo : bomInfoList){
            if(bomInfo.getSequence() == 1){
                String component = bomInfo.getComponent().toLowerCase();
                String componentValue = bomInfo.getTravelerComponent();
                switch(component){
                    case "header":
                        this.header = componentValue;
                        break;
                    case "leadframe":
                        this.leadFrame = componentValue;
                        break;
                    case "mold compound":
                        this.moldCompound = componentValue;
                        break;
                    case "mount compound":
                        this.mountCompound = componentValue;
                        break;
                    case "wire":
                        this.wire = componentValue;
                        break;
                    case "solder ball":
                        this.solder = componentValue;
                        break;
                    case "lid":
                        this.lid = componentValue;
                        break;
                    case "chip capacitor":
                        this.chipCap = componentValue;
                        break;
                }
            }
        }
    }
}