package com.ti.specteam.vswr.dashboard.repository;

import org.springframework.stereotype.Repository;

import com.ti.specteam.vswr.dashboard.domain.security.LdapXRefRecord;
import com.ti.specteam.vswr.dashboard.domain.security.VswrSecurityRecord;

@Repository
public interface VswrSecurityDao {

    public VswrSecurityRecord getVswrSecurityRecordByEmpId(String empId);

    public LdapXRefRecord getLdapXRefRecordByOrg(String org);

    public boolean doesAtSiteExist(String atSite);

    public boolean doesSbeExist(String sbe);

}
