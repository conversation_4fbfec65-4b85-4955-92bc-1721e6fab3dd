package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PackingRequirements {
    String vswrID;
    String stickerType;

    @JsonProperty("eWaiver")
    String eWaiver;
    
    String isRetestRMR;
    String finishedGoodsDispo;
    String waferSkeleton;
    String plantCode;
    String pdcUnrestrictedSale;
}
