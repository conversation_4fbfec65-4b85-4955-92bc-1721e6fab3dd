package com.ti.specteam.vyper.actions;
import com.ti.specteam.vyper.build.model.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFHyperlink;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.buildlink.BuildLinkService;
import lombok.extern.slf4j.Slf4j; 
import lombok.RequiredArgsConstructor;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class DownloadVyperExcelAction {
    private final VyperService vyperService;
    private final BuildService buildService;
    private final int dataStartRowNumber = 0;
    private final int dataStartColNumber = 1;
    private final String cellDelim = ", ";
    private final int COLUMN_WIDTH =  25 * 256;
    private final BuildLinkService buildLinkService;
    @Value("${spring.datasource.vyper.username}")
    private String envType;

    public void execute(String vyperNumber, OutputStream os) throws IOException{
        log.debug("execute(vyperNumber:{})", vyperNumber);
        Vyper vyper = vyperService.fetchVyper(vyperNumber);
              

        Workbook wb = new XSSFWorkbook();     
        addBuildSheet(wb, vyper); 

        // Write the output to a file
        wb.write(os);
        os.close();
    }

    private void addBuildSheet(Workbook wb, Vyper vyper){
        // Get all builds
        List<Build> builds = buildService.findAllBuildsByVyperNumber(vyper.getVyperNumber());

        // CreationHelper createHelper = wb.getCreationHelper();
        Sheet sheet = wb.createSheet("Builds");
        int rowTracker = 0+dataStartRowNumber;          // this is the current row number to be created

        // Creating all the rows
        Row buildNumberRow = sheet.createRow(rowTracker++);
        Row buildStateRow = sheet.createRow(rowTracker++);
        Row buildtypeRow = sheet.createRow(rowTracker++);
        Row descriptionRow = sheet.createRow( rowTracker++);
        Row deviceRow = sheet.createRow( rowTracker++);
        Row facilityRow = sheet.createRow( rowTracker++);
        Row backgrindRow = sheet.createRow( rowTracker++);
        Row copyFromRow = sheet.createRow( rowTracker++);
        Row pkgNicheRow = sheet.createRow( rowTracker++);
        Row bomTemplateRow = sheet.createRow( rowTracker++);
        Row flowRow = sheet.createRow(rowTracker++);
        Row dieRow = sheet.createRow(rowTracker++);
        Row waferSawMethodRow = sheet.createRow(rowTracker++);
        Row leadFrameRow = sheet.createRow(rowTracker++);
        Row mountCompoundRow = sheet.createRow(rowTracker++);
        Row mbDiagramRow = sheet.createRow(rowTracker++);
        Row wireRow = sheet.createRow(rowTracker++);
        Row moldCompoundRow = sheet.createRow(rowTracker++);
        Row symbolizationRow = sheet.createRow(rowTracker++);
        Row packConfigRow = sheet.createRow(rowTracker++);
        Row MSLRow = sheet.createRow(rowTracker++);
        Row drybakeRow = sheet.createRow(rowTracker++);
        Row ESLRow = sheet.createRow(rowTracker++);
        Row turnkeyRow = sheet.createRow(rowTracker++);
        CreationHelper createHelper = wb.getCreationHelper();

        CellStyle hlinkstyle = wb.createCellStyle();
        Font hlinkfont = wb.createFont();
        hlinkfont.setUnderline(XSSFFont.U_SINGLE);
        hlinkfont.setColor(IndexedColors.BLUE.index);
        hlinkstyle.setFont(hlinkfont);

        String buildHyperlink = buildLinkService.getBuildSelectionLink(vyper.getVyperNumber());

        // Adding the side headers
        addHeaders(sheet);
        sheet.autoSizeColumn(0);    
        // Adding all the rows
        for(int i = 0; i < builds.size() ; i++){
            int col =  i+dataStartColNumber;                            // Current working column
            Build build = builds.get(i);                                // Current working build

            addCell(buildNumberRow, build.getBuildNumber(), col);
            
            Cell buildNumberCell = buildNumberRow.getCell(col);
            XSSFHyperlink link = (XSSFHyperlink)createHelper.createHyperlink(HyperlinkType.URL);
            link.setAddress(buildHyperlink.replaceAll("<buildnumber>", build.getBuildNumber()));
            buildNumberCell.setHyperlink((XSSFHyperlink) link);
            buildNumberCell.setCellStyle(hlinkstyle);

            addCell(buildStateRow,build.getState().name(),col);
            addCell(buildtypeRow,build.getBuildtype(),col);
            addCell(descriptionRow, build.getDescription(),col);
            addCell(deviceRow, build.getMaterial().getMaterial(),col);
            addCell(facilityRow, build.getFacility().getPdbFacility(),col);

            // add the backgrind
            StringBuilder s = new StringBuilder();
            if(null != build.getBackgrind().getBackgrindVal()) {
                s.append(build.getBackgrind().getBackgrindVal()).append(" ");
            }
            if(build.getBackgrind().getBackgrindSelected() != null && !build.getBackgrind().getBackgrindSelected().isEmpty()) {
                s.append(build.getBackgrind().getBackgrindSelected().stream().map(SelectionItem::getValue).toString());
            }
            addCell(backgrindRow, s.toString(), col);

            addCell(copyFromRow,build.getVyperCopiedFrom().getVyperBuildNumber(),col);
            addCell(pkgNicheRow,build.getPackageNiche().getName(),col);
            addCell(bomTemplateRow,build.getBomTemplate().getBomTemplateName(),col);
            addCell(flowRow,build.getFlow().getObject().getName(),col);
            addCell(dieRow,build.getDies().getDieString(cellDelim),col);
            addCell(waferSawMethodRow,build.getWaferSawMethod().getValue(),col);
            addCell(leadFrameRow, getComponentString(cellDelim, "Leadframe", build),col);
            addCell(mountCompoundRow, getComponentString(cellDelim, "Mount Compound", build),col);
            addCell(mbDiagramRow, getComponentString(cellDelim, "MB Diagram", build),col);
            addCell(wireRow, getComponentString(cellDelim, "Wire", build),col);
            addCell(moldCompoundRow, getComponentString(cellDelim, "Mold Compound", build),col);
            addCell(symbolizationRow,build.getSymbolization().getSymbolString(cellDelim),col);
            addCell(packConfigRow,build.getPackConfig().getValue(),col);
            addCell(MSLRow, getComponentString(cellDelim, "MSL", build),col);
            addCell(drybakeRow,build.getDryBake().getValue(),col);
            addCell(ESLRow,build.getEsl().getShelfLife(),col);
            addCell(turnkeyRow, build.getTurnkey().getValue(), col);

            // Set the column width
            sheet.setColumnWidth(col, COLUMN_WIDTH);

        }

        // Add Text Wrapping
        CellStyle wrappedCellStyle = wb.createCellStyle();
        wrappedCellStyle.setWrapText(true);

        for(Row row : sheet){
            if(row != buildNumberRow){

                for(Cell cell : row){
                    cell.setCellStyle(wrappedCellStyle);
                    
                }
            }
        }
    }


    private void addHeaders(Sheet sheet){
        // Headers added to the first column
        List<String> rowHeaders = Arrays.asList(
            "Build Number",
            "Build State",
            "Build type", 
            "Description", 
            "Device", 
            "Facility",
            "Backgrind",
            "Copy From",
            "Package Niche",
            "Bill of Process Template(s)",
            "Flow",
            "Die(s)",
            "Wafer Saw Method",
            "Leadframe",
            "Mount Compound",
            "MB Diagram",
            "Wire",
            "Mold Compound",
            "Symbolization",
            "Pack Config",
            "MSL",
            "Drybake",
            "ESL",
            "Turnkey");
        Iterator<Row> rowIter = sheet.iterator();
        Iterator<String> headerIter  = rowHeaders.iterator();
        while(rowIter.hasNext() && headerIter.hasNext()){
            rowIter.next().createCell(0).setCellValue(headerIter.next());

        }
    }
    
    private void addCell(Row row, String str, int col){
        Cell cell = row.createCell(col);
        cell.setCellValue(str);
    }
    private Row addMultipleCells(Sheet sheet, Row startRow, int col, List<String> str){
        int rowCounter = startRow.getRowNum();
        Row nextRow = sheet.getRow(0);

        for(String val : str){

            nextRow = sheet.getRow(rowCounter);
            if(nextRow== null){
                nextRow = sheet.createRow(rowCounter);
            }

            Cell cell = nextRow.createCell(col);
            cell.setCellValue(val);
            rowCounter++;
        }
        nextRow = sheet.createRow(rowCounter);
        return nextRow;
        
    }

    private String getComponentString(String delim, String component, Build build){
        String compString = "";
        for(ComponentInstance instance:build.findComponentByName(component).getInstances()){
            for(ComponentPriority priority:instance.getPriorities()){
                compString += priority.getValue()+ delim;
            }
        }
        compString = StringUtils.stripEnd(compString, delim);
        return compString;

    }

}
