package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.testprogram.TestProgramService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.UPLOAD_PROGRAM;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UploadTestAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final TestProgramService testProgramService;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;

    public Build execute(UploadTestForm uploadTestForm) {
        log.debug("execute(uploadTestForm:{})", uploadTestForm);

        Vyper vyper = vyperService.fetchVyper(uploadTestForm);
        Build build = buildService.fetchBuild(uploadTestForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getTest().setContent(uploadTestForm.getContent());

        testProgramService.parse(build, securityService.user());

        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                UPLOAD_PROGRAM,
                "uploaded test program file"
        );

        return buildService.saveBuild(build);
    }

}
