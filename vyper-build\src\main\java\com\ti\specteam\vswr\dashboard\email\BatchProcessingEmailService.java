package com.ti.specteam.vswr.dashboard.email;

import lombok.RequiredArgsConstructor;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.io.StringWriter;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

@Service
@RequiredArgsConstructor
public class BatchProcessingEmailService {

  private final JavaMailSender javaMailSender;
  private final BatchProcessingEmailCfg emailProperties;
  private final VelocityEngine velocityEngine;

  private String getEmailPrefix(String serverName) {
    if (serverName.contains("scswr-dev")) {
      return "SCSWR-DEV";
    } else if (serverName.contains("localhost")) {
      return "SCSWR LOCALHOST";
    } else {
      return "SCSWR";
    }
  }

  public void send(BatchProcessingEmailContext context) throws MessagingException {
    StringWriter sw = new StringWriter();

    VelocityContext velocityContext = context.getVelocityContext();
    velocityContext.put("serverName", emailProperties.getServerName());
    velocityContext.put("emailPrefix", this.getEmailPrefix(emailProperties.getServerName()));
    velocityContext.put("swrDashboardToolLink", emailProperties.getSwrDashboardToolLink());
    velocityEngine.mergeTemplate(context.getTemplate(), "UTF-8", velocityContext, sw);

    String emailBody = sw.toString();

    MimeMessage message = javaMailSender.createMimeMessage();
    MimeMessageHelper helper = new MimeMessageHelper(message);
    helper.setFrom(emailProperties.getFrom());

    if (!context.getTos().isEmpty()) {
      helper.setTo(context.getTos().toArray(String[]::new));
    }

    if (!context.getCcs().isEmpty()) {
      helper.setCc(context.getCcs().toArray(String[]::new));
    }
    helper.setSubject(context.getSubject());
    helper.setText(emailBody, true);

    javaMailSender.send(message);
  }

}
