package com.ti.specteam.vyper.build;

import com.ti.specteam.vyper.bdw.BdwRepository;
import com.ti.specteam.vyper.build.choice.ChoiceQuery;
import com.ti.specteam.vyper.build.choice.ChoiceQueryService;
import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.componentmap.util.FilterMethod;
import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.ComponentPriority;
import com.ti.specteam.vyper.build.util.ComponentValueRowMapper;
import com.ti.specteam.vyper.build.vyper.ComponentChoiceForm;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.entity.build.BuildEntity;
import com.ti.specteam.vyper.entity.build.BuildEntityService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Woods
 */
@SuppressWarnings({"SqlResolve", "SqlNoDataSourceInspection"})
@Service
@Slf4j
@RequiredArgsConstructor
public class ComponentService {

    private final NamedParameterJdbcTemplate jdbcTemplate;
    private final ComponentMapService componentMapService;
    private final ChoiceQueryService choiceQueryService;
    private final BuildEntityService buildEntityService;
    private final UserUtilsService userUtilService;
    private final BdwRepository bdwRepository;
    private final PraService praService;

    //    @Cacheable("component.choices")
    public List<String> componentChoices(ComponentChoiceForm componentChoiceForm) {

        // get the component map
        ComponentMap componentMap = componentMapService.findByName(componentChoiceForm.getComponentName());
        if (null == componentMap) {
            return Collections.emptyList();
        }

        if (componentChoiceForm.getMethod().equals(FilterMethod.PARENT_CHILD_DIE)) {
            if (componentChoiceForm.getPraNumber() == null) {
                throw new VyperException("PRA Number can not be null for the parent child die method");
            }

            Pra pra = praService.fetchPra(componentChoiceForm.getPraNumber());
            Component dieComponent = pra.findComponentByName("Die");

            if (dieComponent != null) {
                List<ComponentPriority> dieComponentPriorities = dieComponent.getPriorities(0);
                if (dieComponentPriorities != null) {
                    List<String> parentDies = dieComponentPriorities.stream().map(ComponentPriority::getValue).collect(Collectors.toList());
                    List<String> dieNames = dieComponentPriorities.stream().map(ComponentPriority::getValue)
                            .collect(Collectors.toList());
                    List<String> childDies = bdwRepository.fetchChildDies(dieNames);
                    parentDies.addAll(childDies);
                    return parentDies.stream().distinct().collect(Collectors.toList());
                }
            }
            return null;
        }

        // get the components
        List<String> componentNames = new ArrayList<>();
        componentNames.add(componentMap.getAtssComponentName());
        if (null != componentMap.getAlternativeFillSource()) {
            Arrays.asList(componentMap.getAlternativeFillSource().split(",")).forEach(s -> {
                String name = s.trim();
                if (!name.isEmpty()) {
                    componentNames.add(name);
                }
            });
        }

        // loop through the names until we get results

        for (String componentName : componentNames) {
            List<String> results = componentChoices(componentName, componentChoiceForm);
            if (!results.isEmpty()) {
                return results;
            }
        }

        return Collections.emptyList();
    }

    public List<String> componentChoices(String componentName, ComponentChoiceForm componentChoiceForm) {

        log.debug("componentChoices(componentName:{}, componentChoiceForm:{})", componentName, componentChoiceForm);

        // get the query
        ChoiceQuery choiceQuery = choiceQueryService.determine(componentChoiceForm.getMethod());
        if (null == choiceQuery) {
            return Collections.emptyList();
        }

        // replace the parameters
        MapSqlParameterSource source = new MapSqlParameterSource();
        if (choiceQuery.isComponentName()) {
            source.addValue("component_name", componentName);
        }


        if (choiceQuery.isFacility()) {
            if (userUtilService.isInternalUser()) {
                source.addValue("facility_at", componentChoiceForm.getFacility());
            } else {
                // Add based on the user facility rights
                List<String> listFacility = userUtilService.checkUserApiAccessSites();
                if (listFacility.contains(componentChoiceForm.getFacility())) {
                    source.addValue("facility_at", componentChoiceForm.getFacility());
                }
            }

        }

        if (choiceQuery.isPkgGroup()) {
            source.addValue("pkg_group", componentChoiceForm.getPkgGroup());
        }

        if (choiceQuery.isPkg()) {
            source.addValue("pkg", componentChoiceForm.getPkg());
        }

        if (choiceQuery.isPin()) {
            source.addValue("pin", componentChoiceForm.getPin());
        }

        if (choiceQuery.isSupplierNumber()) {

            // VYPER-1886 - query updated - supplier part number is lowercased and removed of spaces

            String s = componentChoiceForm.getSupplierOrWire();
            if (s != null) {
                s = s.replace(" ", "").toUpperCase();
            }
            source.addValue("supplier_number", s);
        }

        if (choiceQuery.isWire()) {
            source.addValue("wire", componentChoiceForm.getSupplierOrWire());
        }

        if (choiceQuery.isSuffix()) {
            source.addValue("suffix", componentChoiceForm.getSuffix() + "%");
        }

        if (choiceQuery.isBuildCreateDate()) {
            Date date = null;

            // if we find the build entity, use it's create date
            BuildEntity buildEntity = buildEntityService.findByBuildNumber(componentChoiceForm.getBuildNumber());
            if (buildEntity != null) {
                date = buildEntity.getCreatedDate();
            }

            // if we don't have a data, use a month ago
            if (date == null) {
                date = Date.from(ZonedDateTime.now().minusMonths(1).toInstant());
            }

            source.addValue("build_create_date", date);
        }

        List<String> values;
        if (source.getValues().isEmpty()) {
            values = jdbcTemplate.query(choiceQuery.getQuery(), new ComponentValueRowMapper());
        } else {
            values = jdbcTemplate.query(choiceQuery.getQuery(), source, new ComponentValueRowMapper());
        }

        return values;
    }

}
