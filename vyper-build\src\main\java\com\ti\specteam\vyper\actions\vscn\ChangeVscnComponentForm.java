package com.ti.specteam.vyper.actions.vscn;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ti.specteam.vyper.actions.VyperNumberForm;
import com.ti.specteam.vyper.build.model.SelectionItem;
import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@Jacksonized
public class ChangeVscnComponentForm extends VscnNumberForm {

    @NotNull
    @Size(min = 1)
    private String name;

    @NotNull
    private List<SelectionItem> items;

    @JsonIgnore
    public String display() {
        return items.stream().map(SelectionItem::getValue).collect(Collectors.joining(", "));
    }
}
