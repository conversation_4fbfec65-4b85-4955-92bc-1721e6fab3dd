package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.ChangelinkChange;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_CHANGELINK_CHANGE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeChangelinkChangeAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final AuditService auditService;

    public Build execute(ChangeChangeLinkChangeForm changeChangeLinkChangeForm) {
        log.debug("execute(changeChangeLinkChangeForm:{})", changeChangeLinkChangeForm);

        Vyper vyper = vyperService.fetchVyper(changeChangeLinkChangeForm);
        Build build = buildService.fetchBuild(changeChangeLinkChangeForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getChangelink().getChanges().clear();
        for (Map<String, Object> change : changeChangeLinkChangeForm.getChanges()) {
            ChangelinkChange clc = new ChangelinkChange();
            clc.getObject().putAll(change);
            clc.getSource().appointUser(securityService.user());
            build.getChangelink().getChanges().add(clc);
        }

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_CHANGELINK_CHANGE,
                "changed changelink change to: " + changeChangeLinkChangeForm.display()
        );

        return buildService.saveBuild(build);
    }

}
