package com.ti.specteam.vswr.vswr.web;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.service.PostATSWRService;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.extern.slf4j.Slf4j;

@RestController
@Validated
@Slf4j
@RequestMapping("/v1/post")
@PreAuthorize("@externalAuthCheck.validateUser()")
public class PostATSWRController {
    @Autowired
    PostATSWRService postATSWRService;

    @PostMapping("/save")
    public ResponseEntity<GeneralInfo> saveForm(@RequestBody ATSWRForm atswrForm){
        log.info("saveData called from PostATSWRController");
        try {
            return ResponseEntity.ok(postATSWRService.saveForm(atswrForm));
        } catch (Exception e) {
            log.error(e.toString());
            
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    @PostMapping("/update")
    public ResponseEntity<GeneralInfo> updateForm(@RequestBody ATSWRForm atswrForm){
        log.info("saveData updateForm from PostATSWRController");
        try {
            return ResponseEntity.ok(postATSWRService.updateForm(atswrForm));
        } catch (Exception e) {
            log.error(e.toString());
            
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}