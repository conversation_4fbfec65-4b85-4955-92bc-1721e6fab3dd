package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.Required;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.Selection;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_BACKGRIND;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeBackgrindAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final AddFlowComponentAction addFlowComponentAction;
    private final UserUtilsService userUtilsService;


    public Build execute(ChangeBackgrindForm changeBackgrindForm) {
        log.debug("execute(changeBackgrindForm:{})", changeBackgrindForm);
        Boolean refreshComplete = false;

        Vyper vyper = vyperService.fetchVyper(changeBackgrindForm);
        Build build = buildService.fetchBuild(changeBackgrindForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getBackgrind().setBackgrindVal(changeBackgrindForm.getBackgrindOption());
        build.getBackgrind().setBackgrindSelected(changeBackgrindForm.getBackgrindSelected());
        build.getBackgrind().getSource().appointUser(securityService.user());

        if(changeBackgrindForm.getBackgrindOption().equals("YES") && changeBackgrindForm.getBackgrindSelected() == null){
            Selection backgrindSelection = build.findSelection("Backgrind","Backgrind");
            if(backgrindSelection != null && backgrindSelection.getItems() != null){
                build.getBackgrind().setBackgrindSelected(new ArrayList<>(backgrindSelection.getItems()));
            }
        }
        
        if(changeBackgrindForm.getBackgrindSelected() != null){
            String cName = "Backgrind";

            FlowOperation flowOperation = build.getFlow().getObject().getOperations().stream()
                    .filter(fo -> StringUtils.equalsIgnoreCase(fo.getName(), cName))
                    .findFirst()
                    .orElse(null);

            if(build.getBackgrind().getBackgrindVal().equals("YES")
                    && flowOperation != null
                && flowOperation.getComponents() != null
                && !flowOperation.getComponents().stream().anyMatch(flowComponent -> StringUtils.equalsIgnoreCase(flowComponent.getName(), cName))){

                AddFlowComponentForm addFlowComponentForm = new AddFlowComponentForm();
                addFlowComponentForm.setOperationName(cName);
                addFlowComponentForm.setName(cName);
                addFlowComponentForm.setRequired(Required.valueOf("OPTIONAL"));
                addFlowComponentForm.setBuildNumber(build.getBuildNumber());
                addFlowComponentForm.setVyperNumber(vyper.getVyperNumber());
                userUtilsService.validateUserByBuild(build.getBuildNumber());
                buildService.saveBuild(build);
                build = addFlowComponentAction.execute(addFlowComponentForm); // overriding build object
                refreshComplete = true;
            }
        }

        if(refreshComplete == false){
            selectionLoader.load(vyper, build);
            travelerRefreshService.load(vyper, build);
            validateOperationLoader.load(vyper, build);
        }

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_BACKGRIND,
                "changed backgrind to: " + changeBackgrindForm.display()
        );

        return buildService.saveBuild(build);
    }

}