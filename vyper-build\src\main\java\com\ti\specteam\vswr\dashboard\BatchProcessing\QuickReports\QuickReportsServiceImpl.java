package com.ti.specteam.vswr.dashboard.BatchProcessing.QuickReports;

 import java.text.SimpleDateFormat;
 import java.util.List;
 import java.util.Map;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;

import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.specteam.vswr.dashboard.BatchProcessing.Repository.QuickReportsDao;
 
 /**
  * Implementation of generic DataTables service.
  */
 @Service
 public class QuickReportsServiceImpl implements QuickReportsService {
     //---- Members
 
     @Autowired
     private QuickReportsDao genericDao;
 
     /**
      * {@inheritDoc}
      */
     public List<Map<String, Object>> getRows(final String tableName, final DataTableParams params) {
         return genericDao.getRows(tableName, params);
     }
 
     /**
      * {@inheritDoc}
      */
     public List<Map<String, Object>> getPage(final String tableName,
             final DataTableParams params, final List siteExt,
             final String listColumns, final String swrStat) { 
         SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy kk:mm:ss");
         List<Map<String, String>> columns = genericDao.getColumns(tableName, null);
        // System.out.println("From Impl Rows Per Page :-->"+params.getRowsPerPage());
         System.out.println("siteExt = " + siteExt);
         List<Map<String, Object>> page = genericDao.getPage(tableName, params, siteExt, listColumns, swrStat);
         // loop through rows, replacing nulls with empty strings.
        //  for (Map<String, Object> row : page) {
        //      for (Map<String, String> column : columns) {
        //          if (!row.containsKey(column.get("column_name"))) {
        //              row.put(column.get("column_name"), "");
        //          } else {
        //              if ("java.sql.Timestamp".equals(row.get(column.get("column_name")).getClass().getName())) {
        //                  row.put(column.get("column_name"), 
        //                          sdf.format(row.get(column.get("column_name"))));
        //              }
        //          }
        //      }
        //  }
         return page;
     }
 
     /**
      * {@inheritDoc}
      */
     public int getCount(final String tableName) {
         return genericDao.getCount(tableName);
     }
 
     /**
      * {@inheritDoc}
      */
     public int getCountBySearch(final String tableName, final DataTableParams params,
             final List<Map<String,String>> siteExt, String swrStat) {
         return genericDao.getCountBySearch(tableName,  params, siteExt, swrStat);
     }
 
     /**
      * {@inheritDoc}
      */
     public List<Map<String, String>> getColumns(final String tableName, final List<String> filter) {
         return genericDao.getColumns(tableName, filter);
     }

     public List<Map<String, Object>> getPage(final String tableName,
     final DataTableParams params) { 
        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy kk:mm:ss");
        List<Map<String, String>> columns = genericDao.getColumns(tableName);
        // System.out.println("From Impl Rows Per Page :-->"+params.getRowsPerPage());
        List<Map<String, Object>> page = genericDao.getPage(tableName, params);
        // loop through rows, replacing nulls with empty strings.
        for (Map<String, Object> row : page) {
            for (Map<String, String> column : columns) {
                if (!row.containsKey(column.get("column_name"))) {
                    row.put(column.get("column_name"), "");
                } else {
                    if ("java.sql.Timestamp".equals(row.get(column.get("column_name")).getClass().getName())) {
                        row.put(column.get("column_name"), 
                                sdf.format(row.get(column.get("column_name"))));
                    }
                }
            }
        }
        return page;
    }
 }
 