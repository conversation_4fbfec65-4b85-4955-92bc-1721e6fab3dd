package com.ti.specteam.atssmassupload.web;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.ProjectDeviceForm;
import com.ti.specteam.atssmassupload.service.ProjectDeviceLogService;
import com.ti.specteam.atssmassupload.service.ProjectDeviceService;
import com.ti.specteam.vyper.vscn.model.AtssScn;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/v1/atssmassupload/project/device")
@CrossOrigin(origins = { "*" })
@Slf4j
@RequiredArgsConstructor
public class ProjectDeviceController {

    private final ProjectDeviceLogService projectDeviceLogService;
    private final ProjectDeviceService projectDeviceService;

    @GetMapping("/{deviceId}/log")
    public List<AtssScn> getDeviceLog(@PathVariable("deviceId") String deviceId){
        return projectDeviceLogService.findProjectDeviceLogByDeviceId(deviceId);
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/save")
    public ProjectDevice saveProjectDevice(@NotNull @RequestBody ProjectDeviceForm projectDeviceForm){
        return projectDeviceService.saveProjectDevice(projectDeviceForm);
    }

}
