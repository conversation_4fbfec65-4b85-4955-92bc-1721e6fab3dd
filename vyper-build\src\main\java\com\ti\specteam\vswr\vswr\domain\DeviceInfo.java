package com.ti.specteam.vswr.vswr.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceInfo {

    private String vswrID;

    @JsonAlias("Material")
    private String sapMaterial;
    
    private String sapBaseMaterial;

    private String specDevice;

    @JsonAlias("SBE")
    private String sbe;

    @JsonAlias("SBE1")
    private String sbe1;

    @JsonAlias("SBE2")
    private String sbe2;

    private String industrySector;

    @JsonAlias("PackagePin")
    private String pin;
    
    @JsonAlias("PackageDesignator")
    private String pkg;
    
    @JsonAlias("PackageGroup")
    private String pkgGroup;

    private String buildQuantity;
    private String profitCenter;
    private String offloadInfo;
    private String apl;
    private String iso;
    private String mcm;

    @JsonAlias("OldMaterial")
    private String oldMaterial;
    
    private String niche;
    private String pdbFacility;
}
