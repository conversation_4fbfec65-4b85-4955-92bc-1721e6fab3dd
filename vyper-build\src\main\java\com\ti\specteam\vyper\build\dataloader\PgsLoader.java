package com.ti.specteam.vyper.build.dataloader;

import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.componentlistener.context.PgsListenerContext;
import com.ti.specteam.vyper.componentlistener.listener.PgsListener;
import com.ti.specteam.vyper.config.flowopn.DeviceFlowOperation;
import com.ti.specteam.vyper.pgs.PgsParserService;
import com.ti.specteam.vyper.pgs.PgsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PgsLoader {

    private final PgsService pgsService;
    private final PgsParserService pgsParserService;
    private final List<PgsListener> listeners;

    private final List<String> componentNames = Arrays.asList(
            "MB Diagram", "Leadframe", "Mount Compound", "Mold Compound", "Wire", "MB Diagram", "Wire");

    /**
     * Query pgs for the component details, and store in the build
     *
     * @param vyper The build in which to store the PGS data.
     * @param build The build in which to store the PGS data.
     */
    public void load(Vyper vyper, Build build) {
        log.debug("load(vyper:{}, build:{})", vyper.getVyperNumber(), build.getBuildNumber());

        clearPgs(build);
        clearComponents(build);
        fetchPgs(build);

        loadAlternativeOptionNames(build);

        if (build.isAtssFullCopy()) {
            return;
        }

        // load components only if not ATSS FULL COPY
        loadComponents(build);

        removeInvalidDieAttributes(build);

        // call listeners only if not ATSS FULL COPY

        // loop through the alternative BOMs
        build.getPgs().getBomNames().forEach(bomName -> {
            PgsListenerContext context = PgsListenerContext.builder()
                    .vyper(vyper)
                    .build(build)
                    .bomName(bomName)
                    .build();

            listeners.forEach(listener ->
                    listener.onPgsLoader(context)
            );

        });

    }

    public void loadComponents(Build build) {
        loadDie(build);
    }

    public void clearPgs(Build build) {
        Pgs pgs = build.getPgs();
        pgs.setAlternativeOptionName(null);
        pgs.getBomNames().clear();
        pgs.setUrl(null);
        pgs.setLastUpdated(null);
        pgs.getObject().clear();
    }

    // here wer clear the components, but we manually remove the selection items one at a time.
    // this should allow us to keep any user-entered selections, but allow us to remove the
    // selections from pgs.
    public void clearComponents(Build build) {

        List<DeviceFlowOperation> flowRows = build.getBuildFlow().getFlowRows();
        Set<String> flowOperationNames = (flowRows != null ? flowRows : Collections.<DeviceFlowOperation>emptyList()).stream()
                .map(DeviceFlowOperation::getOpnName)
                .collect(Collectors.toSet());


        componentNames.stream()
                .filter(flowOperationNames::contains)
                .forEach(cName -> {
                    // find the component
                    Component component = build.findOrCreateComponent(cName, Source.PGS);

                    // find all the selections for this component
                    List<Selection> selections = build.getSelections().stream()
                            .filter(selection -> StringUtils.equalsIgnoreCase(selection.getName(), cName))
                            .collect(Collectors.toList());

                    // remove the selections that have the same component value
                    component.getInstances().stream()
                            .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                            .forEach(componentPriority -> {
                                String value = componentPriority.getValue();

                                selections.forEach(selection -> selection.removeItem(value));
                            });

                    component.clear();
                    component.getSource().reset();
                });
    }

    public void fetchPgs(Build build) {
        String url = pgsService.buildDeviceLookupUrl(
                build.getMaterial().getMaterial(),
                build.getFacility().getPlantCode());

        Map<String, Object> pgsData = pgsService.fetchDeviceLookupUrl(
                build.getMaterial().getMaterial(),
                build.getFacility().getPlantCode());

        Pgs pgs = build.getPgs();
        pgs.setUrl(url);
        pgs.setLastUpdated(new Date());
        pgs.getObject().putAll(pgsData);
    }

    public void loadAlternativeOptionNames(Build build) {

        // get all the assembly BOMs, then get the AlternateOptionName  values

        Pgs pgs = build.getPgs();
        PgsObject pgsObject = pgs.getObject();

        List<Map<String, Object>> refObjects = pgsParserService.metadataRefObjectsByType(pgsObject, "AssemblyBOM");

        List<BomName> aons = refObjects.stream()
                .map(refObject -> {
                    Map<String, Object> attrs = (Map<String, Object>) refObject.get("attrs");
                    String name = (String) attrs.get("AlternateOptionName");

                    BomName aon = new BomName();
                    aon.setId((String) refObject.get("id"));
                    aon.setName(name);
                    return aon;
                })
                .sorted()
                .collect(Collectors.toList());

        pgs.getBomNames().addAll(aons);
    }

    public void loadDie(Build build) {

        Pgs pgs = build.getPgs();
        PgsObject pgsObject = pgs.getObject();

        String name = pgs.getAlternativeOptionName();

        BomName bomName = pgs.getBomNames().stream().filter(bn -> StringUtils.equalsIgnoreCase(name, bn.getName())).findFirst().orElse(null);
        if (null == bomName) return;

        Map<String, Object> refObject = pgsParserService.metadataRefObjectById(pgsObject, bomName.getId());
        if (null == refObject || refObject.isEmpty()) return;

        Map<String, Object> attrs = (Map<String, Object>) refObject.get("attrs");
        if (null == attrs || attrs.isEmpty()) return;

        List<String> dieNames = (List<String>) attrs.get("DieName");
        if (null == dieNames) return;

        List<Map<String, Object>> refObjects = pgsParserService.metadataRefObjectsByType(pgsObject, "Die");

        // prepare the die object

        Dies dies = new Dies();
        DieInstance dieInstance = new DieInstance();
        dieInstance.setType("Die");
        dies.getDieInstances().add(dieInstance);
        dies.getSource().appointSystem(SystemName.PGS);
        build.setDies(dies);

        // loop through the dies
        dieNames.forEach(dieName -> {
            // get the ref object
            Map<String, Object> r = refObjects.stream()
                    .filter(ro -> {
                        Map<String, Object> a = (Map<String, Object>) ro.get("attrs");
                        return StringUtils.equalsIgnoreCase(dieName, (String) a.get("Die"));
                    })
                    .findFirst()
                    .orElse(null);

            if (r == null) return;
            // add the attributes
            Map<String, Object> a = (Map<String, Object>) r.get("attrs");
            if (a == null) return;
            loadDie(dieInstance, dieName, a);
        });

    }

    public void loadDie(DieInstance dieInstance, String dieName, Map<String, Object> attributes) {
        Die die = new Die();
        die.setName(dieName);
        die.setEngineering(Engineering.N);
        attributes.keySet().forEach(key -> die.getObject().put(fixAttributeName(key), attributes.get(key)));
        die.getSource().appointSystem(SystemName.PGS);
        dieInstance.getDies().add(die);

        //  set incoming water thickness to the pgs - outgoing wafer thickness
        // if the value is 999, the incoming wafer thick value is not set.
        Integer value = null;
        try {
            value = (Integer) attributes.get("OutgoingWaferThicknessUm");
            if (999 != value) {
                die.setIncomingWaferThick(value);
            }
        } catch (RuntimeException ex) {
            log.warn("unable to set incoming wafer thickness. value was:" + value);
        }

    }

    public String fixAttributeName(String name) {
        if (name == null || name.isEmpty()) {
            return null;
        }

        // put spaces after upper case letters
        // BumpType -> Bump Type
        StringBuilder output = new StringBuilder();
        for (int n = 0; n < name.length(); ++n) {
            String ch = name.substring(n, n + 1);
            if (StringUtils.isAlpha(ch) && !StringUtils.isAllLowerCase(ch)) {
                if (output.length() > 0) {
                    output.append(" ");
                }
            }
            output.append(ch);
        }

        // fix some specific issues in the attribute name
        String value = output.toString()
                .replaceAll(" Xum$", " X um") // space between direction and unit
                .replaceAll(" Yum$", " Y um")
                .replaceAll(" Mm$", " mm");// fix capitalization

        // cleanup some specific die names
        switch (value) {
            case "C Tech":
                return "CTech";
            case "L R P Tech":
                return "LRPTech";
            case "P R Tech":
                return "PRTech";
        }

        return value;
    }

    /**
     * Remove any attributes that have the value "INVALID"
     *
     * @param build {@link Build} The build object
     */
    public void removeInvalidDieAttributes(Build build) {
        for (DieInstance dieInstance : build.getDies().getDieInstances()) {
            dieInstance.removeInvalid();
        }
    }

    // loop through our dies, retrieve pgs attributes, and update.
    public void refreshAllDieAttributes(Build build) {
        build.getDies()
                .stream()
                .flatMap(DieInstance::stream)
                .forEach(die -> {
                    Map<String, Object> attributes = pgsService.fetchDieAttribute(die.getName());
                    attributes.keySet().forEach(key -> die.getObject().put(fixAttributeName(key), attributes.get(key)));
                });
    }

}
