package com.ti.specteam.vswr.dashboard.service;

import com.ti.specteam.vswr.dashboard.domain.security.VswrRolesContainer;
import com.ti.specteam.vswr.dashboard.domain.security.VswrSecurityRecord;

public interface VswrSecurityService {
    class RoleTypes {
      public static final String SBE = "SBE";
      public static final String AT = "A/T";
      public static final String BTH = "BTH";
    }
    class AdministrativeLevels {
      public static final String ADMIN = "Admin";
    }
    class ErrorTypes {
      public static final String GENERATE_LDAP_ROLES_ERROR = "Could not generate default roles by ldap information";
    }
    public static final String SITE_EXTENSION_DELIM = ";";

    public VswrRolesContainer getVswrRolesByAid(String aid);

    public VswrRolesContainer convertVswrSecurityRecordToVswrRolesContainer(VswrSecurityRecord vswrSecurityRecord);

    public VswrRolesContainer generateVswrRolesByLdapInformation(String aid);
}
