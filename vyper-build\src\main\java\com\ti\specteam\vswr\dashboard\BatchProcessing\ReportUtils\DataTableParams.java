package com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;

/**
 * Helper class to parse DataTable parameters from a request variable.
 */
public final class DataTableParams {

    //---- Members
    private Map<String, String> filters;
    private Map<String, DataTableSearchParam> searchTerms;
    private int sEcho;
    private int firstRow;
    private int rowsPerPage;
    private String orderClause;
    /**
     * {@value #DEFAULT_PAGE_ROWS}.
     */
    public static final int DEFAULT_PAGE_ROWS = 10000;

    //---- Methods
    /**
     * Alternate zero-argument constructor; used for manually building parameters.
     */
    public DataTableParams() {
        filters = new HashMap<String, String>();
        searchTerms = new HashMap<String, DataTableSearchParam>();
        rowsPerPage = DEFAULT_PAGE_ROWS;
    }

    /**
     * Main constructor; populates search terms and echo parameter.
     *
     * @param parameters The request parameter map with values generated by the
     *                   DataTables widget.
     */
    public DataTableParams(final Map<String, String[]> parameters) {
        this();
        if (parameters == null) {
            return;
        }
        for (String param : parameters.keySet()) {
            if (param.startsWith("sSearch_")) {
                String suffix = param.substring(param.indexOf('_'));
                String[] searchable = parameters.get("bSearchable" + suffix);
                String[] search = parameters.get("sSearch" + suffix);
                if (searchable != null && "true".equals(searchable[0])
                        && StringUtils.isNotEmpty(search[0])
                        && !search[0].endsWith("~")) {
                    String[] colName = parameters.get("mDataProp" + suffix);
                    String[] result = search[0].split("~");
                    // If the column is non-existing, do not filter
                    if (colName != null && StringUtils.isNotBlank(colName[0])) {
                        searchTerms.put(colName[0], new DataTableSearchParam(
                                result[0], result.length > 1 ? result[1] : null));
                    }
                }
            }
        }
        // Set the echo, start and length parameters
        if (parameters.containsKey("sEcho")) {
            sEcho = Integer.parseInt(parameters.get("sEcho")[0]);
        }
        if (parameters.containsKey("iDisplayStart")) {
            // Add one to convert from 0-based index to 1-based index
            firstRow = Integer.parseInt(parameters.get("iDisplayStart")[0]) + 1;
        }
        if (parameters.containsKey("iDisplayLength")) {
            rowsPerPage = Integer.parseInt(parameters.get("iDisplayLength")[0]);
        }

        // Set the sort parameters
        String[] sortCol = parameters.get("iSortCol_0");
        String[] sortDir = parameters.get("sSortDir_0");
        if (sortCol != null) {
            String[] colName = parameters.get("mDataProp_" + sortCol[0]);
            if (colName != null && StringUtils.isNotBlank(colName[0])) {
                orderClause = " UPPER(" + colName[0] + ") " + sortDir[0];
            }
        }
    }

    /**
     * Adds a filter (exact match) to the parameters.
     *
     * @param column The column to filter on
     * @param value  The filter value
     * @return This DataTableParams object
     */
    public DataTableParams addFilter(final String column, final String value) {
        filters.put(column, value);
        return this;
    }

    /**
     * Returns a Set of filter terms.
     *
     * @return A Set of Map.Entry objects representing the filter terms
     */
    public Set<Map.Entry<String, String>> getFilterTerms() {
        return filters.entrySet();
    }

    /**
     * Adds a search term (wildcard match) to the parameters.
     *
     * @param column The column to search on
     * @param values The search term values
     * @return This DataTableParams object
     */
    public DataTableParams addSearchTerm(final String column,
            final DataTableSearchParam values) {
        searchTerms.put(column, values);
        return this;
    }

    /**
     * Returns a Set of search terms.
     *
     * @return A Set of Map.Entry objects representing the search terms
     */
    public Set<Map.Entry<String, DataTableSearchParam>> getSearchTerms() {
        return searchTerms.entrySet();
    }

    /**
     * Returns the value of sEcho.
     *
     * @return The sEcho value.
     */
    public int getsEcho() {
        return sEcho;
    }

    /**
     * Sets the firstRow value.
     *
     * @param fr The row number of the first row to be returned
     * @return This DataTableParams object
     */
    public DataTableParams setFirstRow(final int fr) {
        // Add one to convert from 0-based index to 1-based index
        this.firstRow = fr + 1;
        return this;
    }

    public int getFirstRow() {
        return firstRow;
    }

    /**
     * Sets the rowsPerPage value.
     *
     * @param rpp The number of rows per page to be returned
     * @return This DataTableParams object
     */
    public DataTableParams setRowsPerPage(final int rpp) {
        this.rowsPerPage = rpp;
        return this;
    }

    public int getRowsPerPage() {
        return rowsPerPage;
    }

    /**
     * Sets the values used in the SQL &quot;ORDER BY&quot; clause.
     *
     * @param oc The columns to order results by
     * @return This DataTableParams object
     */
    public DataTableParams setOrderClause(final String oc) {
        this.orderClause = oc;
        return this;
    }

    public String getOrderClause() {
        return orderClause;
    }

    @Override
    public String toString() {
        return "DataTableParams{" + "filters=" + filters
                + ", searchTerms=" + searchTerms
                + ", sEcho=" + sEcho
                + ", firstRow=" + firstRow
                + ", rowsPerPage=" + rowsPerPage
                + ", orderClause=" + orderClause + '}';
    }
}
