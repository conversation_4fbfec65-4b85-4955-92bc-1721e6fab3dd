package com.ti.specteam.vyper.componentlistener.processor

import com.ti.specteam.vyper.atss.attribute.AttributeRepository
import com.ti.specteam.vyper.atss.component.AtssComponentService
import com.ti.specteam.vyper.atss.traveler.Traveler
import com.ti.specteam.vyper.build.model.*
import com.ti.specteam.vyper.componentlistener.context.AtssLoaderListenerContext
import com.ti.specteam.vyper.componentlistener.context.PgsListenerContext
import com.ti.specteam.vyper.componentlistener.processor.utils.MatchContext
import com.ti.specteam.vyper.pgs.PgsParserService
import com.ti.specteam.vyper.wiremetal.WireMetalRepository
import spock.lang.Specification
import spock.lang.Unroll

class WireProcessorSpec extends Specification {

    PgsParserService pgsParserService = Mock(PgsParserService)
    WireMetalRepository wireMetalRepository = Mock(WireMetalRepository)
    AttributeRepository attributeRepository = Mock(AttributeRepository)
    AtssComponentService atssComponentService = Mock(AtssComponentService)

    WireProcessor processor = new WireProcessor(
            pgsParserService,
            wireMetalRepository,
            attributeRepository,
            atssComponentService
    )

    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _
    }

    def "onPgsLoader - load the Wire"() {
        def bomName1 = new BomName(id: "ID1", name: "BOM1")

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.metadataRefObjectById(_, "ID1") >> [
                attrs: [WirePartNumber: ["NUMBER1", "NUMBER2"]]
        ]
        1 * pgsParserService.metadataRefObjectsByType(_, "Wire") >> [
                [attrs: [PartNumber: "NUMBER1", WireType: "TYPE1", WireDiameterMils: "1.1"]],
                [attrs: [PartNumber: "NUMBER2", WireType: "TYPE2", WireDiameterMils: "2.2"]]
        ]

        and:
        build1.getComponentKeyValue("Wire", 0, 0, "name") == "TYPE1 1.1"
        build1.getComponentKeyValue("Wire", 0, 0, "PartNumber") == "NUMBER1"
        build1.getComponentKeyValue("Wire", 0, 0, "WireType") == "TYPE1"
        build1.getComponentKeyValue("Wire", 0, 0, "WireDiameterMils") == "1.1"
        build1.getComponentKeyValue("Wire", 0, 1, "name") == "TYPE2 2.2"
        build1.getComponentKeyValue("Wire", 0, 1, "PartNumber") == "NUMBER2"
        build1.getComponentKeyValue("Wire", 0, 1, "WireType") == "TYPE2"
        build1.getComponentKeyValue("Wire", 0, 1, "WireDiameterMils") == "2.2"

        build1.findComponentByName("Wire").getInstances().size() == 1
        build1.findComponentByName("Wire").getInstances().get(0).getPriorities().size() == 2

    }

    def "onPgsLoader - existing data is cleared"() {

        def c1 = build1.findOrCreateComponent("Wire", Source.PGS)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "Cu 0.96", Engineering.N)
        p1.put "supplier_number", "Cu 0.96"

        def bomName1 = new BomName(id: "ID1", name: "BOM1")

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.metadataRefObjectById(_, "ID1") >> [
                attrs: [WirePartNumber: ["NUMBER1", "NUMBER2"]]
        ]
        1 * pgsParserService.metadataRefObjectsByType(_, "Wire") >> [
                [attrs: [PartNumber: "NUMBER1", WireType: "TYPE1", WireDiameterMils: "1.1"]],
                [attrs: [PartNumber: "NUMBER2", WireType: "TYPE2", WireDiameterMils: "2.2"]]
        ]

        and:
        build1.getComponentKeyValue("Wire", 0, 0, "name") == "TYPE1 1.1"
        build1.getComponentKeyValue("Wire", 0, 0, "PartNumber") == "NUMBER1"
        build1.getComponentKeyValue("Wire", 0, 0, "WireType") == "TYPE1"
        build1.getComponentKeyValue("Wire", 0, 0, "WireDiameterMils") == "1.1"
        build1.getComponentKeyValue("Wire", 0, 1, "name") == "TYPE2 2.2"
        build1.getComponentKeyValue("Wire", 0, 1, "PartNumber") == "NUMBER2"
        build1.getComponentKeyValue("Wire", 0, 1, "WireType") == "TYPE2"
        build1.getComponentKeyValue("Wire", 0, 1, "WireDiameterMils") == "2.2"

        build1.findComponentByName("Wire").getInstances().size() == 1
        build1.findComponentByName("Wire").getInstances().get(0).getPriorities().size() == 2
    }

    def "onAtssLoader - traveler component not found - marked as processed"() {

        def traveler = new Traveler()
        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Wire")
    }

    def "onAtssLoader - component not found - marked as processed"() {

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Wire", "CVALUE1")
        tc.create("Base Metal", "Cu")
        tc.create("Wire Diameter", "24.38 UM (0.96 MIL)")
        tc.create("EDGE Number", "EDGE12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Wire")
    }

    def "onAtssLoader - sets the build component priority value"() {
        build1.findOrCreateComponent("Wire", Source.VYPER)

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Wire", "CVALUE1")
        tc.create("Base Metal", "Cu")
        tc.create("Wire Diameter", "24.38 UM (0.96 MIL)")
        tc.create("EDGE Number", "EDGE12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Wire")

        and:
        build1.getComponentKeyValue("Wire", 0, 0, "name") == "Cu 0.96"
        build1.getComponentKeyValue("Wire", 0, 0, "base_metal") == "Cu"
        build1.getComponentKeyValue("Wire", 0, 0, "wire_diameter") == "24.38 UM (0.96 MIL)"
        build1.getComponentKeyValue("Wire", 0, 0, "edge_number") == "EDGE12345"
    }

    def "onAtssLoader - change pgs source atss"() {

        def c1 = build1.findOrCreateComponent("Wire", Source.PGS)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "Cu 0.96", Engineering.N)
        p1.put "supplier_number", "Cu 0.96"

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Wire", "CVALUE1")
        tc.create("Base Metal", "Cu")
        tc.create("Wire Diameter", "24.38 UM (0.96 MIL)")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        build1.findComponentByName("Wire").getPriority(0, 0).source.system.name == SystemName.ATSS
    }

    @Unroll
    def "matchWire - #description"() {

        ComponentPriority priority = new ComponentPriority()
        priority.setValue(vyperValue, Engineering.N, Source.VYPER)
        MatchContext context = new MatchContext()

        when:
        def ret = processor.matchValue(priority, armarcDiameter, armarcType, context)

        then:
        _ * wireMetalRepository.existsByArmarcMetalAndCamsMetal(_, _) >> wireMetalExists

        _ * attributeRepository.findByName("GLOBAL", "Wire", _) >> []

        and:
        ret == result

        and:
        context.match == result
        context.armarcType == armarcType || context.armarcType == ""
        context.armarcDiameter == armarcDiameter || context.armarcDiameter == ""
        context.vyperValue == vyperValue

        where:
        result | armarcType | armarcDiameter | vyperValue | wireMetalExists | description
        false  | "AL"       | "1.4"          | "CU 1.4"   | false           | "fail if types are different"
        false  | "CU"       | "0.8"          | "CU 1.4"   | false           | "fail if diameters are different"
        true   | "CU"       | "1.4"          | "CU 1.4"   | true            | "passes if types and diameters are same"
        true   | null       | null           | ""         | false           | "pass with null armarc value and and no pra wire"
    }

    @Unroll
    def "matchWire gets values from cams if no value in ComponentPriority"() {

        ComponentPriority priority = new ComponentPriority()
        priority.setValue("1234567-1234", Engineering.N, Source.VYPER)
//        priority.setKeyValue("Base Metal", "", Engineering.N, Source.VYPER)
//        priority.setKeyValue("Wire Diameter", "", Engineering.N, Source.VYPER)
        MatchContext context = new MatchContext()

        when:
        def ret = processor.matchValue(priority, "111", "AT1", context)

        then:
        1 * wireMetalRepository.existsByArmarcMetalAndCamsMetal("AT1", "VT1") >> false

        1 * attributeRepository.findByName("GLOBAL", "Wire", "1234567-1234") >> [
                [key: "Base Metal", value: "VT1"],
                [key: "Wire Diameter", value: "111"]
        ]

        and:
        !ret

        and:
        !context.match
        context.armarcType == "AT1"
        context.armarcDiameter == "111"
        context.vyperValue == "1234567-1234"
        context.vyperDiameter == "111"
        context.vyperType == "VT1"


    }

    def "matchWire passes if diameters are within 98%"() {

        when:
        def ret = processor.matchDiameter(armarcDiameter, camsDiameter)

        then:
        ret == result

        where:
        result | armarcDiameter   | camsDiameter
        true   | "0 UM (100 MIL)" | "100"
        true   | "0 UM (100 MIL)" | "99"
        true   | "0 UM (100 MIL)" | "98"
        false  | "0 UM (100 MIL)" | "97"
        false  | "0 UM (100 MIL)" | "96"
    }

}
