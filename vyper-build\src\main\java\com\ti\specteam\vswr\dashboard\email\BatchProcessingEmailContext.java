package com.ti.specteam.vswr.dashboard.email;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import org.apache.velocity.VelocityContext;

@Data
public class BatchProcessingEmailContext {
  private String template;
  private VelocityContext velocityContext = new VelocityContext();
  private List<String> tos = new ArrayList<>();
  private List<String> ccs = new ArrayList<>();
  private String subject;
}
