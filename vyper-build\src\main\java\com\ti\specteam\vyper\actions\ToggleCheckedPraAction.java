package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ti.specteam.vyper.verifier.model.ValidatedComponent;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.user.User;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.security.user.UserUtilsService;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_TOGGLE_CHECKED;

@Service
@Slf4j
@RequiredArgsConstructor
public class ToggleCheckedPraAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final SecurityService securityService;
    private final BuildService buildService;
    private final UserUtilsService userUtilsService;

    public Pra execute(PraToggleCheckedForm praToggleCheckedForm) {
        log.debug("execute(praToggleCheckedForm:{})", praToggleCheckedForm);

        // grab the objects
        Vyper vyper = vyperService.fetchVyper(praToggleCheckedForm);
        Pra pra = praService.fetchPra(praToggleCheckedForm);
        
        userUtilsService.validateUserByBuild(pra.getBuildNumber());

        Build build = buildService.fetchBuild(pra.getBuildNumber());

        
        // validate current user can do this
        validateService.checkOpen(vyper);
        validateService.checkAt(build);
        validateService.checkEditable(vyper, pra);

        User user = securityService.user();

       ValidatedComponent validatedComponent = pra.getValidatedComponents().get(praToggleCheckedForm.getComponentName());
       String detail = "";

       if(validatedComponent != null){
           if(praToggleCheckedForm.getChecked()){
              validatedComponent.setUsername(user.getUsername());
              validatedComponent.setUserid(user.getUserid()); 
              SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ssZ");            
              validatedComponent.setWhen(simpleDateFormat.format(new Date()));
              validatedComponent.setChecked(true);
              detail = validatedComponent.getParentOperation()+" checked by "+user.display();
           }else{
                validatedComponent.setUsername(null);
                validatedComponent.setUserid(null);
                validatedComponent.setWhen(null);
                validatedComponent.setChecked(false);
                detail = validatedComponent.getParentOperation()+" un-checked by "+user.display();
           }
       }else{
           throw new VyperException(praToggleCheckedForm.getComponentName()+" was not found");
       }

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_TOGGLE_CHECKED,
                detail);

        return praService.savePra(pra);
    }

}
