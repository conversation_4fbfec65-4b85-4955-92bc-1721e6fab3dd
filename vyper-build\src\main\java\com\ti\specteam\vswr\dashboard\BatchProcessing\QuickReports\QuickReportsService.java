package com.ti.specteam.vswr.dashboard.BatchProcessing.QuickReports;

import java.util.List;
import java.util.Map;

import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;

/**
 * Generic service definition.
 */
public interface QuickReportsService {

    /**
     * Returns the rows of a given table or view.
     *
     * @param tableName The table name
     * @param params A DataTableParams holding filter and order parameters
     * @return The contents of the table
     */
    List<Map<String, Object>> getRows(String tableName, DataTableParams params);

    /**
     * Returns a page of data from a table.
     *
     * @param tableName The table name
     * @param params A DataTableParams holding search, filter, order, and page
     * index parameters
     * @return The table data matching the row and search criteria
     */
    List<Map<String, Object>> getPage(String tableName, DataTableParams params, 
            List siteExt, String listColumns, String swrStat);

    /**
     * Returns the number of rows in a table.
     *
     * @param tableName The table name
     * @return The total number of rows
     */
    int getCount(String tableName);

    /**
     * Returns the number of rows in a table for the given search criteria.
     *
     * @param tableName The table name
     * @param params A DataTableParams holding filter and search parameters
     * @return The number of matching rows
     */
    int getCountBySearch(String tableName, DataTableParams params,
            List<Map<String,String>> siteExt, String swrStat);

    /**
     * Returns the column metadata for the given table.
     *
     * @param tableName The table name
     * @return The DB metadata from all_tab_cols
     */
    List<Map<String, String>> getColumns(String tableName, List<String> filter);
}