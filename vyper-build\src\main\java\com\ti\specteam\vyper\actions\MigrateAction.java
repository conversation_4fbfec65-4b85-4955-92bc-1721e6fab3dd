package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.Required;
import com.ti.specteam.vyper.atss.paragraph.ReplacementService;
import com.ti.specteam.vyper.audit.Audit;
import com.ti.specteam.vyper.audit.AuditRepository;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.EcatLoader;
import com.ti.specteam.vyper.build.dataloader.PgsLoader;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.entity.build.BuildEntityService;
import com.ti.specteam.vyper.entity.vyper.VyperEntityService;
import com.ti.specteam.vyper.history.build.BuildHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Woods
 */
@SuppressWarnings("unused")
@Service
@Slf4j
@RequiredArgsConstructor
public class MigrateAction {

    private final VyperEntityService vyperEntityService;
    private final ChangeMaterialAction changeMaterialAction;
    private final PgsLoader pgsLoader;
    private final AuditRepository auditRepository;
    private final BuildEntityService buildEntityService;
    private final BuildService buildService;
    private final BuildHistoryService buildHistoryService;
    private final JdbcTemplate jdbcTemplate;
    private final EcatLoader ecatLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ReplacementService replacementService;

    @PostConstruct
    public void run() {
        log.info("run()");

        // these migrations have already been run in production
//        migrate_01();
//        migrate_02();
//        migrate_03();
//        migrate_04();
//        migrate_05();
//        migrate_06();
//        migrate_07();
//        migrate_08();
//        migrate_09();
//        migrate_10();
//        migrate_11();
//        migrate_12();
//        migrate_13();
//        migrate_14();
//        migrate_15();
//        migrate_16();
//        migrate_17();
//        migrate_18();
//        migrate_19();
//        migrate_20();
//        migrate_21();
//        migrate_22();
//        migrate_23();
//        migrate_24();
//        migrate_25();

        // vyper 1.2.2 migrations
        migrate_26();
        migrate_27();
        migrate_28();

        // vyper1.2.2b migrations
        migrate_29();
        migrate_30();

        // vyper 1.2.3 migrations
        migrate_31();
        migrate_32();

        // vyper 1.2.4 migration
        migrate_33();

        log.debug("Migration Complete");
    }

    /**
     * For the VyperEntity that have null version, we call update
     * so the version get set.
     */
    public void migrate_01() {
        log.debug("migrate_01()");

        vyperEntityService.findAllByVersionIsNull().forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                vyperEntityService.update(ve);
            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }
        });

    }

    /**
     * reload the material object
     * reload the facility object
     * create the pgs object
     * create the test object
     */
    public void migrate_02() {
        log.debug("migrate_02()");

        vyperEntityService.findAllByVersion(1).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {

                    // reload the material.object and material.facilities
                    String material = build.getMaterial().getMaterial();
                    if (material != null) {
                        changeMaterialAction.fetchMaterialAndFacilities(material, build);
                    }

                    // create pgs object

                    if (null == build.getPgs()) {
                        build.setPgs(new Pgs());
                        pgsLoader.load(vyper, build);
                    }

                    // create test object

                    if (null == build.getTest()) {
                        build.setTest(new Test());
                    }

                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_2.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });

    }

    /**
     * add comments to the build
     */
    public void migrate_03() {
        log.debug("migrate_03()");

        vyperEntityService.findAllByVersion(2).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {

                    if (null == build.getComments()) {
                        build.setComments(new ArrayList<>());
                    }

                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_3.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });

    }

    /**
     * add the esl component
     */
    public void migrate_04() {
        log.debug("migrate_04()");

        vyperEntityService.findAllByVersion(3).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {

                    if (null == build.getEsl()) {
                        build.setEsl(new Esl());
                    }

                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_4.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    /**
     * add the packConfig component
     */
    public void migrate_05() {
        log.debug("migrate_05()");

        vyperEntityService.findAllByVersion(4).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {

                    if (null == build.getPackConfig()) {
                        build.setPackConfig(new PackConfig());
                    }

                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_5.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    /**
     * Add the submitter, buApprover and atApprover
     */
    public void migrate_06() {
        log.debug("migrate_06()");

        vyperEntityService.findAllByVersion(5).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {

                    if (null == build.getSubmitter()) {
                        build.setSubmitter(new UserTime());
                    }

                    if (null == build.getAtApprover()) {
                        build.setAtApprover(new UserTime());
                    }

                    if (null == build.getBuApprover()) {
                        build.setBuApprover(new UserTime());
                    }

                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_6.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    /**
     * Add the engineering flag to the component
     * Add item to selections
     */
    public void migrate_07() {
        log.debug("migrate_07()");

        vyperEntityService.findAllByVersion(6).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {

                    // add engineering to the components

                    build.getComponents().forEach(component ->
                            component.getInstances().forEach(componentInstance ->
                                    componentInstance.getPriorities().forEach(componentPriority ->
                                            componentPriority.setEngineering(Engineering.N))));

                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_7.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    /**
     * selections - convert from values to items
     */
    public void migrate_08() {
        log.debug("migrate_08()");

        vyperEntityService.findAllByVersion(7).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build ->
                        build.getSelections().forEach(selection -> {
//                        selection.getValues().forEach(value -> {
//                            SelectionItem item = new SelectionItem();
//                            item.setValue(value);
//                            item.setEngineering("N");
//                            selection.getItems().add(item);
//                        });
//
//                        selection.getValues().clear();
                        }));

                // save the update

                vyper.setVersion(VyperVersion.VERSION_8.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    /**
     * set project type to "other"
     */
    public void migrate_09() {
        log.debug("migrate_09()");

        vyperEntityService.findAllByVersion(8).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                ve.setProjectType("Other");
                vyper.setVersion(VyperVersion.VERSION_9.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    /**
     * update ESL
     * N/A -> SSL (2 years)
     * 5+ Year ESL -> ESL (5 years)
     */
    public void migrate_10() {
        log.debug("migrate_10()");

        vyperEntityService.findAllByVersion(9).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().forEach(build -> {
                    if (StringUtils.equalsIgnoreCase("5+ Year ESL", build.getEsl().getShelfLife())) {
                        build.getEsl().getObject().put("value", "ESL (5 years)");
                    } else {
                        build.getEsl().getObject().put("value", "SSL (2 years)");
                    }
                });

                // save the update

                vyper.setVersion(VyperVersion.VERSION_10.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    @SuppressWarnings("SimplifyStreamApiCallChains")
    public void migrate_11() {
        log.debug("migrate_11()");

//        auditRepository.deleteAll();

        vyperEntityService.findAllByVersion(10).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            if (StringUtils.equalsIgnoreCase("VYPER0071700", ve.getVyperNumber())) {
                return;
            }

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                List<Audit> audits = vyper.getAudits().stream()
                        .map(va -> {
                            Audit audit = new Audit();
                            audit.setVyperNumber(vyper.getVyperNumber());
                            audit.setBuildNumber(va.getBuildNumber());
                            audit.setUserid(va.getWho().getUserid());
                            audit.setUsername(va.getWho().getUsername());
                            audit.setActivity(va.getActivity());
                            audit.setDetail(va.getDetail());
                            audit.setWhen(va.getWhen());
                            return audit;
                        }).filter(audit -> audit.getActivity() != null)
                        .map(audit -> {
                            if (audit.getDetail().length() > 4000) {
                                audit.setDetail(audit.getDetail().substring(0, 3999));
                            }
                            return audit;
                        })
                        .collect(Collectors.toList());

                auditRepository.saveAll(audits);

                // save the update

                vyper.setVersion(VyperVersion.VERSION_11.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // clear the build.audits lists
    public void migrate_12() {
        log.debug("migrate_12()");

        vyperEntityService.findAllByVersion(11).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            if (StringUtils.equalsIgnoreCase("VYPER0071700", ve.getVyperNumber())) {
                return;
            }

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                vyper.getAudits().clear();

                vyper.setVersion(VyperVersion.VERSION_12.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // fix the TravelerOperation.required = "Y" to REQUIRED
    @SuppressWarnings("CodeBlock2Expr")
    public void migrate_13() {
        log.debug("migrate_13()");

        vyperEntityService.findAllByVersion(12).forEach(ve -> {
            log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

            try {
                log.debug(ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                // if any required are Y, then change to REQUIRED
                vyper.getBuilds().forEach(build -> {
                    build.getTraveler().getOperations().forEach(travelerOperation -> {
                        if (travelerOperation.getRequired() == Required.Y) {
                            travelerOperation.setRequired(Required.REQUIRED);
                        }
                    });
                });

                vyper.setVersion(VyperVersion.VERSION_13.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // move vyper.builds to the build entity
    public void migrate_14() {
        log.debug("migrate_14()");

        vyperEntityService.findAllByVersion(13).forEach(ve -> {

            if (StringUtils.equalsIgnoreCase("VYPER0071700", ve.getVyperNumber())) {
                return;
            }

            try {
                log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                vyper.getBuilds().stream()
                        .sorted((build1, build2) -> StringUtils.compare(build1.getBuildNumber(), build2.getBuildNumber()))
                        .forEach(buildService::saveBuild);

                vyper.setVersion(VyperVersion.VERSION_14.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // remove vyper.builds
    public void migrate_15() {
        log.debug("migrate_15()");

        vyperEntityService.findAllByVersion(14).forEach(ve -> {

            if (StringUtils.equalsIgnoreCase("VYPER0071700", ve.getVyperNumber())) {
                return;
            }

            try {
                log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.getBuilds().clear();

                vyper.setVersion(VyperVersion.VERSION_15.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // add state to build_history
    public void migrate_16() {
        log.debug("migrate_16()");

        buildHistoryService.findAllByStateIsNull().forEach(history -> {

            try {
                log.debug("migrating buildNumber:{}", history.getBuildNumber());

                Build build = buildEntityService.fromJson(history.getJson());
                history.setState(build.getState());

                buildHistoryService.update(history);

            } catch (VyperException e) {
                log.warn(history.getBuildNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // add submit date to the build entity
    public void migrate_17() {
        log.debug("migrate_17()");

        vyperEntityService.findAllByVersion(15).forEach(ve -> {
            try {
                buildEntityService.findAllByVyperNumber(ve.getVyperNumber()).forEach(be -> {
                    log.debug("migrating buildNumber:{}", be.getBuildNumber());

                    Build build = buildEntityService.fromJson(be.getJson());

                    OffsetDateTime odt = build.getSubmitter().getWhen();
                    if (null != odt) {
                        be.setDateSubmitted(new Date(odt.toInstant().toEpochMilli()));
                        buildEntityService.update(be);
                    }

                });

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());
                vyper.setVersion(VyperVersion.VERSION_17.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });

    }

    // set build history facility, submitter and date submitted
    public void migrate_18() {
        log.debug("migrate_18()");

        // facility
        jdbcTemplate.execute("" +
                "update build_history\n" +
                "set facility_at = json_value(the_contents, '$.facility.object.PDBFacility')\n" +
                "where facility_at is null");

        // submitter
        jdbcTemplate.execute("" +
                "update build_history\n" +
                "set submitter = \n" +
                "    json_value(the_contents, '$.submitter.user.username') || ' / ' ||\n" +
                "    json_value(the_contents, '$.submitter.user.userid')\n" +
                "where submitter is null\n" +
                "and json_value(the_contents, '$.submitter.user.userid') is not null");

        // date submitted
        jdbcTemplate.execute("" +
                "update build_history\n" +
                "set date_submitted = cast(TO_UTC_TIMESTAMP_TZ(json_value (the_contents, '$.submitter.when')) as DATE)\n" +
                "where date_submitted is null\n" +
                "and json_value (the_contents, '$.submitter.when') is not null");
    }

    // set material
    public void migrate_19() {
        log.debug("migrate_19()");

        jdbcTemplate.execute("" +
                "update build_json\n" +
                "set material = json_value(build_obj, '$.material.object.Material')\n" +
                "where material is null");
    }

    // set build entity - final approval date
    public void migrate_20() {
        log.debug("migrate_20()");

        jdbcTemplate.execute("" +
                "update BUILD_JSON \n" +
                "set date_final_approval = cast(TO_UTC_TIMESTAMP_TZ(json_value (build_obj, '$.buApprover.when')) as DATE)\n" +
                "where json_value (build_obj, '$.state') = 'FINAL_APPROVED'\n");
    }

    // set build history - final approval date
    public void migrate_21() {
        log.debug("migrate_21()");

        jdbcTemplate.execute("" +
                "update build_history\n" +
                "set date_final_approval = cast(TO_UTC_TIMESTAMP_TZ(json_value (the_contents, '$.buApprover.when')) as DATE)\n" +
                "where json_value (the_contents, '$.state') = 'FINAL_APPROVED'\n");
    }

    // build.state names:
    //  reject -> rework
    //  new -> draft
    public void migrate_22() {
        log.debug("migrate_22()");

        jdbcTemplate.execute("" +
                "update build_json\n" +
                "set build_obj = json_mergepatch (build_obj, '{\"state\" : \"REWORK\"}' returning clob),\n" +
                "    state = 'REWORK'\n" +
                "where json_value (build_obj, '$.state') = 'REJECTED'\n" +
                "or state = 'REJECTED'\n" +
                "");
    }

    public void migrate_23() {
        log.debug("migrate_23()");

        jdbcTemplate.execute("" +
                "update build_json\n" +
                "set build_obj = json_mergepatch (build_obj, '{\"state\" : \"DRAFT\"}' returning clob),\n" +
                "    state = 'DRAFT'\n" +
                "where json_value (build_obj, '$.state') = 'NEW'\n" +
                "or state = 'NEW'\n" +
                "");
    }

    // rejectedTraveler -> reworkedTraveler
    public void migrate_24() {
        log.debug("migrate_24()");

        vyperEntityService.findAllByVersion(17).forEach(ve -> {

            try {
                log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                buildEntityService.findAllByVyperNumber(ve.getVyperNumber()).forEach(be -> {
                    Build build = buildEntityService.fromJson(be.getJson());
                    build.getRejectedTraveler().copy(build.getReworkedTraveler());
                    build.getRejectedTraveler().clear();
                    be.setJson(buildEntityService.toJson(build));
                    buildEntityService.update(be);
                });

                vyper.setVersion(VyperVersion.VERSION_24.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });

    }

    // add ecat values
    public void migrate_25() {
        log.debug("migrate_25()");

        vyperEntityService.findAllByVersion(24).forEach(ve -> {

            try {
                log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                buildEntityService.findAllByVyperNumber(ve.getVyperNumber()).forEach(be -> {
                    Build build = buildEntityService.fromJson(be.getJson());

                    String value = ecatLoader.load(vyper, build);

                    // add traveler component

                    TravelerOperation travelerOperation = build.getTraveler().getOperations().stream()
                            .filter(to -> StringUtils.equalsIgnoreCase("Symbol", to.getName()))
                            .findFirst()
                            .orElse(null);

                    if (travelerOperation != null) {
                        TravelerComponent tc = new TravelerComponent();
                        tc.setName("ECAT");
                        tc.setValue(value);
                        tc.setRequired(Required.Y);
                        tc.getSourceName().set(Source.VYPER);
                        tc.getSourceValue().set(Source.VYPER);
                        travelerOperation.getComponents().add(tc);
                    }

                    be.setJson(buildEntityService.toJson(build));
                    buildEntityService.update(be);
                });

                vyper.setVersion(VyperVersion.VERSION_25.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });

    }

    // refresh the paragraph texts, so ecat and custx are properly filled in
    public void migrate_26() {
        log.debug("migrate_26()");

        vyperEntityService.findAllByVersion(25).forEach(ve -> {

            try {
                log.debug("migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                buildEntityService.findAllByVyperNumber(ve.getVyperNumber()).forEach(be -> {
                    log.debug("build {}", be.getBuildNumber());

                    Build build = buildEntityService.fromJson(be.getJson());

                    travelerRefreshService.loadParagraphs(build);

                    be.setJson(buildEntityService.toJson(build));
                    buildEntityService.update(be);
                });

                vyper.setVersion(VyperVersion.VERSION_26.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // set the build symbol's replacementText to the replaced paragraph text
    public void migrate_27() {
        log.debug("migrate_27()");

        vyperEntityService.findAllByVersion(26).forEach(ve -> {

            try {
                log.debug("migrate_27 migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                buildEntityService.findAllByVyperNumber(ve.getVyperNumber()).forEach(be -> {
                    log.debug("migrate_27 build {}", be.getBuildNumber());

                    Build build = buildEntityService.fromJson(be.getJson());


                    build.getSymbolization().getSymbols().forEach(symbol -> {
                        SymbolObject symbolObject = symbol.getObject();
                        String text = replacementService.replaceTemplateText(build, symbolObject.getPicture());
                        symbolObject.setDisplay(text);
                    });

                    be.setJson(buildEntityService.toJson(build));
                    buildEntityService.update(be);
                });

                vyper.setVersion(VyperVersion.VERSION_27.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    // changes build numbers from VYPER0000000-0000 to VBUILD0000000-0000

    public void migrate_28() {
        log.debug("migrate_28()");
        String buildNumber = "VYPER0046154-0003";
        String buildPrefix = "VBUILD";
        String regexVerifier = "VBUILD[0-9]{7}-[0-9]{4}";
        vyperEntityService.findAllByVersion(27).forEach(ve -> {

            try {
                log.debug("migrate_28 migrating version:{}, number:{}", ve.getVersion(), ve.getVyperNumber());

                Vyper vyper = vyperEntityService.fromJson(ve.getJson());

                buildEntityService.findAllByVyperNumberAndBuildNumberLike(ve.getVyperNumber(), "VYPER%").forEach(be -> {
                    log.debug("migrate_28 build {}", be.getBuildNumber());

                    Build build = buildEntityService.fromJson(be.getJson());

                    // Changing the build number
                    String newBuildNumber = build.getBuildNumber();
                    newBuildNumber = newBuildNumber.substring(5);
                    newBuildNumber = buildPrefix + newBuildNumber;
                    log.debug("Original buildNumber: {}, new BuildNumber: {}", build.getBuildNumber(), newBuildNumber);

                    if (newBuildNumber.matches(regexVerifier)) {
                        be.setBuildNumber(newBuildNumber);
                        build.setBuildNumber(newBuildNumber);
                    } else {
                        log.warn("new BuildNumber {} did not pass validation ", newBuildNumber);

                    }

                    be.setJson(buildEntityService.toJson(build));
                    buildEntityService.update(be);
                });

                vyper.setVersion(VyperVersion.VERSION_28.getVersion());
                ve.setJson(vyperEntityService.toJson(vyper));
                vyperEntityService.update(ve);

            } catch (VyperException e) {
                log.warn(ve.getVyperNumber() + " " + e.getMessage(), e);
            }

        });
    }

    public void migrate_29() {
        log.debug("migrate_29()");

        jdbcTemplate.execute("" +
                "update vyper_audit\n" +
                "set username = 'Task Service',\n" +
                "    userid = 'a0999998'\n" +
                "where detail like '%via task service%'" +
                "");

        jdbcTemplate.execute("" +
                "update vyper_audit\n" +
                "set username = 'Task Service',\n" +
                "    userid = 'a0999998'\n" +
                "where detail = 'Automatically Approving - Build Selections not changed'" +
                "");

        jdbcTemplate.execute("" +
                "update vyper_json\n" +
                "set version = 29\n" +
                "where version = 28" +
                "");
    }

    /**
     * initialize the new component map pra flags
     */
    public void migrate_30() {
        log.debug("migrate_30()");

        jdbcTemplate.execute("update component_map set pra_armarc_validator = 'NO' where pra_armarc_validator is null");
        jdbcTemplate.execute("update component_map set pra_atss_at_validator = 'YES' where pra_atss_at_validator is null");
        jdbcTemplate.execute("update component_map set pra_atss_bom_validator = 'NO' where pra_atss_bom_validator is null");
        jdbcTemplate.execute("update component_map set pra_atss_global_validator = 'YES' where pra_atss_global_validator is null");
        jdbcTemplate.execute("update component_map set pra_die_validator = 'NO' where pra_die_validator is null");
        jdbcTemplate.execute("update component_map set pra_pgs_validator = 'NO' where pra_pgs_validator is null");
        jdbcTemplate.execute("update component_map set pra_bom_qualified_validator = 'NO' where pra_bom_qualified_validator is null");

        jdbcTemplate.execute("update component_map\n" +
                "set pra_atss_bom_validator = 'YES'\n" +
                "where name in (\n" +
                "'Adhesive', 'Antenna', 'Antenna Wire', 'Base', 'Bottom Overlay', 'Bubble Pack', 'Bubble Pack IN1', 'Bubble Pack IN2', 'Bubble Pack IN3', 'Bump-Flux', 'Bumper Clip', 'Canister', 'Cap Attach', 'Capacitor', 'Capacitor 1', 'Capacitor 10', 'Capacitor 11', 'Capacitor 12', 'Capacitor 13', 'Capacitor 14', 'Capacitor 15', 'Capacitor 16', 'Capacitor 17', 'Capacitor 2', 'Capacitor 3', 'Capacitor 4', 'Capacitor 5', 'Capacitor 6', 'Capacitor 7', 'Capacitor 8', 'Capacitor 9', 'Capillary', 'Carrier', 'Carrier Insert Part', 'Carrier Part - Ship', 'Carrier Part - Test', 'Carrier Tape', 'Chip Capacitor', 'Chip Capacitor 1', 'Chip Capacitor 10', 'Chip Capacitor 11', 'Chip Capacitor 13', 'Chip Capacitor 2', 'Chip Capacitor 3', 'Chip Capacitor 4', 'Chip Capacitor 5', 'Chip Capacitor 6', 'Chip Capacitor 7', 'Chip Capacitor 8', 'Chip Capacitor 9', 'Chip Resistor', 'Clip', 'Coating', 'Coil', 'Core Bobbin', 'Cover', 'Crystal', 'Crystal 2', 'DAM', 'Desiccant', 'Desiccant IN1', 'Desiccant IN2', 'Die Boat', 'Die Boat Cover', 'Diode', 'Diplexer', 'Diplexer 2', 'Dummy', 'ESL Device', 'Encap Compound', 'End Cap', 'End Pin', 'End Pin 2', 'End Plug', 'End Plug 2', 'End Tape', 'Eng - Shunt', 'Eng-Magnets', 'Eng-Singulate Spacer', 'Epoxy', 'Epoxy 2', 'Epoxy-Lid Perimeter', 'Fill', 'Film', 'Film 2', 'Filter', 'Filter 2', 'Filter 3', 'Flange - Reel', 'Flux', 'Foam Disc', 'Foam Liner', 'Frame', 'Getter', 'Glass', 'Ground Plane', 'Header', 'Heat Spreader', 'Heatsink', 'Hinge Plate', 'Hub - Reel', 'Humid Indicatr Strip', 'ISO Laminate', 'Inductor', 'Inductor 1', 'Inductor 2', 'Inductor 3', 'Inductor 4', 'Inductor 5', 'Inductor 6', 'Ink', 'Inlay Sheet', 'Insert Clip', 'Label-Chip Carrier', 'Label-Die Pack', 'Label-Dry Pack', 'Label-ESD', 'Label-MS Caution', 'Label-MS Caution IN1', 'Label-MS Caution IN2', 'Label-Moist IN1', 'Label-Moist IN2', 'Label-MoistSensitive', 'Label-Pack', 'Label-Pack IN1', 'Label-Pack IN2', 'Label-Pack IN3', 'Label-Shipping', 'Label-Shipping Reel', 'Lead Finish', 'Leadframe', 'Leadframe 2', 'Lid', 'Liner-Tape and Reel', 'Lock', 'Magnets', 'Memory', 'Mirror', 'Mnt CMPD-Stacked Die', 'Mold Compound', 'Mount Compound', 'Mount Compound 2', 'Overcoat', 'PCB', 'PCB 2', 'PFDA - DLP', 'Pack Filler', 'Pack Filler IN1', 'Pack Filler IN2', 'Pack Filler-13MM', 'Pack Filler-3.2MM', 'Pack Filler-Box', 'Pack Filler-Carrier', 'Pack Filler-JewelBox', 'Pack Filler-Tray', 'Packing Material', 'Paper Insert', 'Preform', 'RF Switch', 'RF Switch 2', 'Reel - Belt', 'Reel - Module', 'Reel - Tape and Reel', 'Resistor', 'Resistor 1', 'Resistor 10', 'Resistor 11', 'Resistor 12', 'Resistor 2', 'Resistor 3', 'Resistor 4', 'Resistor 5', 'Resistor 6', 'Resistor 7', 'Resistor 8', 'Resistor 9', 'Rubber Band', 'Saw Blade', 'Seasoning Wafer', 'Ship Bag', 'Ship Bag IN1', 'Ship Bag IN2', 'Ship Box', 'Ship Box IN1', 'Ship Box IN2', 'Ship Box IN3', 'Ship Tube', 'Shunt', 'Silicon Oil', 'Singulated Spacer', 'Solder', 'Solder Ball', 'Solder Bump', 'Solder Paste', 'Solder Paste 2', 'SolderPaste-CapMount', 'Solvent', 'Spacer', 'Stencil Mount', 'Stencil Print', 'Stiffener', 'Strap Web', 'Strapping Band', 'Substrate', 'Surf Tape', 'TEST', 'Tape', 'Tape - Pkg Saw', 'Tape - Tape and Reel', 'Tape Cover', 'Tape IN1', 'Tape Lock', 'Tape-ESD Adhesive', 'Tie', 'Top Overlay', 'Top Sheet', 'Tray', 'Underfill', 'VIA Ball', 'Wafer', 'Wafer Bag', 'Wafer Box', 'Wafer Dome', 'Wafer Separator', 'Wafer Tape', 'Wafer Vial', 'Window', 'Wire', 'Wire 2'\n" +
                ")");

        jdbcTemplate.execute("update component_map\n" +
                "set pra_die_validator = 'YES'\n" +
                "where name in (\n" +
                "'Die', 'Die 1', 'Die 2', 'Die 3', 'Die 4', 'Die 5', 'Die 6', 'Die Stack 1', 'Die Stack 2', 'Die Stack 3', 'Die Stack 4'\n" +
                ")");

        jdbcTemplate.execute("update component_map\n" +
                "set pra_pgs_validator = 'YES'\n" +
                "where name in (\n" +
                "'Die', 'Die 1', 'Die 2', 'Die 3', 'Die 4', 'Die 5', 'Die 6', 'Die Stack 1', 'Die Stack 2', 'Die Stack 3', 'Die Stack 4',\n" +
                "'MB Diagram','MB Diagram 2','MB Diagram 3','MB Diagram 4','MB Diagram 5',\n" +
                "'Leadframe','Leadframe 2',\n" +
                "'Mount Compound','Mount Compound 2','Mount Compound 3','Mount Compound 4','Mount Compound 5',\n" +
                "'Mold Compound',\n" +
                "'Wire',\n" +
                "'Wire 2',\n" +
                "'Wire 3'\n" +
                ")");

        jdbcTemplate.execute("update vyper_json\n" +
                "set version = 30\n" +
                "where version = 29" +
                "");
    }

    /**
     * initialize the new component map suffix query
     */
    public void migrate_31() {
        log.debug("migrate_31()");

        jdbcTemplate.execute(
                "update component_map\n" +
                "set query_suffix = 'SUFFIX'\n" +
                "where query_suffix is null" +
                "");
    }

    /**
     * initialize the new component map value_should_be74
     */
    public void migrate_32() {
        log.debug("migrate_32()");

        jdbcTemplate.execute("" +
                "update component_map\n" +
                "set value_should_be74 = 'NO'\n" +
                "where value_should_be74 is null\n");
    }

    /**
     * initialize the new component map value_should_be74
     */
    public void migrate_33() {
        log.debug("migrate_33()");

        jdbcTemplate.execute("" +
                "update component_map\n" +
                "set pra_diagram_validator = 'NO'\n" +
                "where pra_diagram_validator is null\n");
    }

}
