package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Engineering;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.verifier.model.VerifierSource;
import com.ti.specteam.vyper.verifier.vscn.VscnChangeNumberVerifier;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeVscnChangeLinkAction {
    private final VyperService vyperService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final SecurityService securityService;
    private final VscnChangeNumberVerifier vscnChangeNumberVerifier;

    public Vscn execute(ChangeVscnChangeLinkForm changeVscnChangeLinkForm) {
        log.debug("execute(changeVscnChangeLinkForm:{})", changeVscnChangeLinkForm);

        Vscn vscn = vscnService.findByVscnNumber(changeVscnChangeLinkForm);
        Vyper vyper = vyperService.findByVyperNumber(vscn.getVyperNumber());

        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        changeChangelinkNumber(vscn, changeVscnChangeLinkForm);
        vscnChangeNumberVerifier.verify(vyper,vscn);

        auditService.createVscn(
                vscn.getVyperNumber(),
                vscn.getVscnNumber(),
                AuditActivity.VSCN_CHANGE_CHANGELINK_CHANGE,
                "changed changelink change to: " + changeVscnChangeLinkForm.getChangeNumber());

        return vscnService.saveVscn(vscn);
    }

    public void changeChangelinkNumber(Vscn vscn, ChangeVscnChangeLinkForm changeVscnChangeLinkForm){
        String oldChangeNumber = vscn.getChangeNumber().getChangeNumber();
        vscn.getChangeNumber().setChangeNumber(changeVscnChangeLinkForm.getChangeNumber());
        vscn.getChangeNumber().getSource().appointUser(securityService.user());
        if(oldChangeNumber != null){
            // replace the verifier value
            for (Verifier verifier : vscn.getVerifiers()) {
                if (verifier.match("ChangeNumber", oldChangeNumber)) {
                    verifier.setValue(changeVscnChangeLinkForm.getChangeNumber());
                }
            }
        }else{
            // create the verifier
            vscn.findOrCreateVerifier("ChangeNumber",changeVscnChangeLinkForm.getChangeNumber(), Engineering.N, VerifierSource.SOURCE_CHANGELINK);
        }
    }
}
