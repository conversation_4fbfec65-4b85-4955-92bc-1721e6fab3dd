package com.ti.specteam.vswr.vswr.service;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.domain.VSCSWRForm;

public interface VSCSWRService {

    public String pushExistingToScswr(ATSWRForm atswrForm);

    public GeneralInfo pushNewToScswr(ATSWRForm atswrForm);
    
    public VSCSWRForm mapVswrToScswr(ATSWRForm atswrForm);
    
    public VSCSWRForm fetchExistingScswr(String swrID);
    
    public String isScswrStatusBlockedReason(String swrID);
}
