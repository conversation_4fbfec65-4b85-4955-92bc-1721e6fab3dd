package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_CHANGE_DESCRIPTION;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangePraDescriptionAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Pra execute(ChangePraDescriptionForm changePraDescriptionForm) {
        log.debug("execute(ChangePraDescriptionForm:{})", changePraDescriptionForm);

        Vyper vyper = vyperService.fetchVyper(changePraDescriptionForm);
        Pra pra = praService.fetchPra(changePraDescriptionForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, pra);

        execute(vyper, pra, changePraDescriptionForm);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_CHANGE_DESCRIPTION,
                "changed description to: " + changePraDescriptionForm.getDescription());

        return praService.savePra(pra);
    }

    public Vyper execute(Vyper vyper, Pra pra, ChangePraDescriptionForm changePraDescriptionForm) {
        return execute(vyper, pra, changePraDescriptionForm.getDescription().trim());
    }

    public Vyper execute(Vyper vyper, Pra pra, String description) {
        if (StringUtils.isBlank(description)) throw new VyperException("The description is invalid.");
        pra.setDescription(description);
        return vyper;
    }

}
