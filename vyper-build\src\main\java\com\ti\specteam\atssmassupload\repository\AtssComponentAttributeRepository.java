package com.ti.specteam.atssmassupload.repository;

import com.ti.specteam.atssmassupload.domain.SpecCompAttribute;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@RequiredArgsConstructor
@Slf4j
public class AtssComponentAttributeRepository {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public List<SpecCompAttribute> getAtssComponentData(String facilityAt, String componentName, String componentValue){
        String sql = " SELECT scmap.facility_at, scmap.component_name, scmap.component_type, scmap.attribute_name component_attribute_name, \n" +
                "       scav.component_value, scav.attribute_name, scav.attribute_value, scav.is_qualified, " +
                "       case when scav.component_value is not null then 1 else 0 end component_exists,\n" +
                "       case when scav.attribute_value is not null then 1 else 0 end attribute_exists \n" +
                "FROM  spec_comp_attr_map@specdb scmap\n" +
                "    left outer join spec_comp_attr_value@specdb scav on  \n" +
                "    scmap.facility_at = scav.facility_at \n" +
                "    and scmap.component_name = scav.component_name \n" +
                "    and (scmap.attribute_name = scav.attribute_name or scav.attribute_name is null) \n"+
                "    and scav.status = 'A' \n" +
                "    and scav.component_value = :componentValue\n" +
                "where scmap.facility_at = :facilityAt\n" +
                "and scmap.component_name = :componentName \n" +
                "order by nvl2(scmap.attribute_name,1,0), scav.attribute_name nulls last";
        return jdbcTemplate.query(
                sql,
                new MapSqlParameterSource()
                        .addValue("facilityAt", facilityAt)
                        .addValue("componentName", componentName)
                        .addValue("componentValue", componentValue),
                BeanPropertyRowMapper.newInstance(SpecCompAttribute.class));
    };

    public Map<String, Object> getSapDieAttributes (String dieName){
        log.debug("getSapDieAttributes {}", dieName);

        return jdbcTemplate.queryForMap(
                "SELECT * FROM BDW_DIE_ATTRIBUTES_VW WHERE dieName = :dieName ",
                new MapSqlParameterSource().addValue("dieName", dieName));

    }

}
