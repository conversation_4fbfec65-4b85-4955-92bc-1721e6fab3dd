package com.ti.specteam.vyper.apitraveler;

import com.ti.specteam.vyper.apitraveler.model.ApiTraveler;
import com.ti.specteam.vyper.vscn.kafka.KafkaAtssTopicProducer;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class VscnTravelerService {

    private final VscnService vscnService;
    private final PraTravelerService praTravelerService;
    private final AtssTravelerService atssTravelerService;
    private final KafkaAtssTopicProducer kafkaAtssTopicProducer;


    public ApiTraveler traveler(String vscnNumber) {

        Vscn vscn = vscnService.fetchVscn(vscnNumber);
        ApiTraveler apiTraveler = praTravelerService.traveler(vscn.getPraNumber());

        // todo: update for the vscn changes
        apiTraveler.setVyperScnNumber(vscnNumber);

        return apiTraveler;
    }

    /**
     * Submit the VSCN to ATSS
     * @param vscn
     * @return
     */
    public void submitTravelerToAtss(Vscn vscn){

        ApiTraveler apiTraveler =  traveler(vscn.getVscnNumber());
        apiTraveler = atssTravelerService.process(apiTraveler);
        kafkaAtssTopicProducer.send(apiTraveler);
    }
}
