package com.ti.specteam.atssmassupload.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ti.specteam.atssmassupload.config.AtssApiConfig;
import com.ti.specteam.vyper.security.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AtssApiService {
    private final AtssApiConfig atssApiConfig;
    private final RestTemplate restTemplate;
    private final SecurityService securityService;

    public Map<String, Object> callAtssApi(String uri, Object requestJson, HttpMethod method) throws JsonProcessingException {
        Map<String, Object> responseMap = new HashMap<>();
        HttpHeaders httpHeaders = new org.springframework.http.HttpHeaders();
        httpHeaders.set("X-ZUUL-TI-UID",securityService.userid());
        httpHeaders.set("X-ZUUL-TI-SECRET", atssApiConfig.getApiSecret());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(requestJson,httpHeaders);
        restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory());// Required for PATCH
        ResponseEntity<HashMap> responseApi = restTemplate.exchange(atssApiConfig.getBaseUrl()+uri, method, httpEntity, HashMap.class);

        responseMap.put("status", responseApi.getStatusCodeValue());

        if (responseApi.getStatusCode() == HttpStatus.OK || responseApi.getStatusCode() == HttpStatus.CREATED) {
            responseMap.put("success", Boolean.TRUE);
            responseMap.put("body",responseApi.getBody());
        } else {
            responseMap.put("success", Boolean.FALSE);
            responseMap.put("body",responseApi.getBody());
        }

        return responseMap;

    }

    public Map<String, Object> createNewCams( Object requestJson) throws JsonProcessingException {
        log.debug("createNewCams {}",requestJson);
        try{
            return callAtssApi(atssApiConfig.getCamsComponentUrl(), requestJson, HttpMethod.POST);
        }catch (HttpClientErrorException requestException){
            log.error(requestException.getMessage());
            throw requestException;
        }
    }

    public Map<String, Object> updateExistingCams( Object requestJson) throws JsonProcessingException {
        log.debug("updateExistingCams {}",requestJson);
        try{
            return callAtssApi(atssApiConfig.getCamsComponentUrl(), requestJson, HttpMethod.PATCH);
        }catch (HttpClientErrorException requestException){
            log.error(requestException.getMessage());
            throw requestException;
        }
    }

}
