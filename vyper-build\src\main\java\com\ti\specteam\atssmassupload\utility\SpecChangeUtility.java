package com.ti.specteam.atssmassupload.utility;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.SpecChange;
import com.ti.specteam.atssmassupload.domain.SpecChangeDataSheetGrid;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadException;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceRepository;
import com.ti.specteam.atssmassupload.repository.SpecChangeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 *
 * <AUTHOR>
 */

@Slf4j
@RequiredArgsConstructor
@Service
public class SpecChangeUtility {

	private final ProjectDeviceRepository projectDeviceRepository;
	private final SpecChangeRepository specChangeRepository;

	public Map<ProjectDevice, List<SpecChange>> gridRowToMap(String projId, String userId,
			List<SpecChangeDataSheetGrid> SpecChangeDataSheetGridRows) {
		log.debug("GridRowToMap(){}", SpecChangeDataSheetGridRows);
		StringBuffer currentMaterial = new StringBuffer();
		Map<ProjectDevice, List<SpecChange>> deviceChangesMap = new LinkedHashMap<ProjectDevice, List<SpecChange>>();
		List<ProjectDevice> existingProjectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projId);
		ProjectDevice tempProjectDevice = null;
		List<SpecChange> changeList = new ArrayList<SpecChange>();
		int rowIndex = 0;
		for (SpecChangeDataSheetGrid specChangeDataSheetRowInfo : SpecChangeDataSheetGridRows) {
			rowIndex++;
			Date now = new Date();

			if (specChangeDataSheetRowInfo.getMaterial() != null
					&& !specChangeDataSheetRowInfo.getMaterial().trim().isEmpty()) {

				if (!specChangeDataSheetRowInfo.getMaterial().equals(currentMaterial.toString())) {

					if (tempProjectDevice != null) {
						deviceChangesMap.put(tempProjectDevice, changeList);
						currentMaterial.setLength(0);
						currentMaterial.append(specChangeDataSheetRowInfo.getMaterial().trim());
						changeList = new ArrayList<SpecChange>();
					}
					tempProjectDevice = null;
					if (specChangeDataSheetRowInfo.getDeviceId() != null) {
						tempProjectDevice = existingProjectDevices.stream().filter(
								projectDevice -> projectDevice.getId().equals(specChangeDataSheetRowInfo.getDeviceId()))
								.findFirst().orElse(null);

						if (tempProjectDevice == null) {
							throw new AtssMassUploadException("Project device Id is unknown");
						}

						existingProjectDevices.remove(tempProjectDevice);
						currentMaterial.setLength(0);
						currentMaterial.append(specChangeDataSheetRowInfo.getMaterial().trim());

					} else {
						tempProjectDevice = new ProjectDevice();
						currentMaterial.setLength(0);
						currentMaterial.append(specChangeDataSheetRowInfo.getMaterial().trim());
						tempProjectDevice.setProjectId(projId);
					}

					tempProjectDevice.setMaterial(specChangeDataSheetRowInfo.getMaterial());
					tempProjectDevice.setOldMaterial(specChangeDataSheetRowInfo.getOldMaterial());
					tempProjectDevice.setSpecDevice(specChangeDataSheetRowInfo.getSpecDevice());
				}

			}

			if ( specChangeDataSheetRowInfo.getComponentOccurrance() == null){
				specChangeDataSheetRowInfo.setComponentOccurrance(1);
			}
			SpecChange specChange = null;
			if (specChangeDataSheetRowInfo.getChangeId() != null) {
				specChange = specChangeRepository.findById(specChangeDataSheetRowInfo.getChangeId()).get();

				specChange.setFlowType(specChangeDataSheetRowInfo.getFlowType());
				specChange.setOperationName(specChangeDataSheetRowInfo.getOperationName());
				specChange.setComponentName(specChangeDataSheetRowInfo.getComponentName());
				specChange.setComponentValue(specChangeDataSheetRowInfo.getComponentValue());
				specChange.setComponentOccurrence(specChangeDataSheetRowInfo.getComponentOccurrance());
				specChange.setAttributeName(specChangeDataSheetRowInfo.getAttributeName());
				specChange.setAttributeValue(specChangeDataSheetRowInfo.getAttributeValue());
			} else {
				specChange = new SpecChange(specChangeDataSheetRowInfo.getFlowType(),
						specChangeDataSheetRowInfo.getOperationName(), specChangeDataSheetRowInfo.getComponentName(),
						specChangeDataSheetRowInfo.getComponentValue(), specChangeDataSheetRowInfo.getAttributeName(),
						specChangeDataSheetRowInfo.getAttributeValue(), userId, userId, now, now,
						specChangeDataSheetRowInfo.getComponentOccurrance(), rowIndex);
			}
			specChange.setStatus(specChangeDataSheetRowInfo.getValidationStatus());
			changeList.add(specChange);
		}

		// Save Last Material.
		deviceChangesMap.put(tempProjectDevice, changeList);
		existingProjectDevices.forEach(projectDevice -> projectDeviceRepository.deleteById(projectDevice.getId()));

		return deviceChangesMap;
	}

}
