package com.ti.specteam.vyper.actions

import com.ti.specteam.vyper.audit.AuditService
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.build.vyper.VyperService
import com.ti.specteam.vyper.security.user.User
import com.ti.specteam.vyper.validate.ValidateService
import io.micrometer.core.instrument.config.validate.ValidationException
import spock.lang.Specification

import static com.ti.specteam.vyper.audit.AuditActivity.ADD_OWNER
/**
 * <AUTHOR>
 */
class AddOwnerActionSpec extends Specification {

    VyperService vyperService = Mock(VyperService)
    ValidateService validateService = Mock(ValidateService)
    AuditService auditService = Mock(AuditService)

    AddOwnerAction action = new AddOwnerAction(
            vyperService,
            validateService,
            auditService
    )

    def setup() {
    }

    def vyper1 = new Vyper(vyperNumber: "VYPER1234567")

    def "execute adds multiple owners"() {

        def addOwnerForm1 = new AddOwnerForm(
                vyperNumber: "VYPER1234567",
                userid: "a0000000",
                username: "joe tier")

        def addOwnerForm2 = new AddOwnerForm(
                vyperNumber: "VYPER1234567",
                userid: "b0000000",
                username: "jane tier")

        def addOwnersRequest = new AddOwnersRequest(
                vyperNumber: "VYPER1234567",
                ownersList: [addOwnerForm1, addOwnerForm2])

        when:
        def ret = action.execute(addOwnersRequest)

        then:
        1 * vyperService.fetchVyper(addOwnersRequest.getVyperNumber()) >> vyper1
        1 * validateService.checkOpen(vyper1)
        1 * validateService.checkOwner(vyper1)
        1 * vyperService.saveVyper(vyper1) >> vyper1
        1 * auditService.createVyper(
                "VYPER1234567",
                ADD_OWNER,
                "added owners: joe tier / a0000000, jane tier / b0000000"
        )

        and:
        ret.getOwners().size() == 2
        ret.getOwners()[0].getUserid() == "a0000000"
        ret.getOwners()[0].getUsername() == "joe tier"
        ret.getOwners()[1].getUserid() == "b0000000"
        ret.getOwners()[1].getUsername() == "jane tier"
    }

    def "if user is already an owner, no additional add is done."() {

        def addOwnerForm1 = new AddOwnerForm(
                vyperNumber: "VYPER1234567",
                userid: "a0000000",
                username: "joe tier")

        def addOwnersRequest = new AddOwnersRequest(
                vyperNumber: "VYPER1234567",
                ownersList: [addOwnerForm1])

        when:
        def ret = action.execute(addOwnersRequest)

        then:
        1 * vyperService.fetchVyper(addOwnersRequest.getVyperNumber()) >> vyper1
        1 * validateService.checkOpen(vyper1)
        1 * validateService.checkOwner(vyper1)
        1 * vyperService.saveVyper(vyper1) >> vyper1

        and:
        ret.getOwners().size() == 1
        ret.getOwners()[0].getUserid() == "a0000000"
        ret.getOwners()[0].getUsername() == "joe tier"
    }

}
