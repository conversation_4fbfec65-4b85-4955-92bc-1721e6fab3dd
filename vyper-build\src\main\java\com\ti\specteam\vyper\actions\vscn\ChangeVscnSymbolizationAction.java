package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.atss.paragraph.ReplacementService;
import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeVscnSymbolizationAction {

    private final VyperService vyperService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final CustLoader custLoader;
    private final AuditService auditService;
    private final ReplacementService replacementService;
    private final VscnService vscnService;

    public Vscn execute(ChangeVscnSymbolizationForm changeVscnSymbolizationForm) {
        log.debug("execute(changeVscnSymbolizationForm:{})", changeVscnSymbolizationForm);

        Vscn vscn = vscnService.fetchVscn(changeVscnSymbolizationForm.getVscnNumber());
        Vyper vyper = vyperService.fetchVyper(vscn.getVyperNumber());

        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        changeSymbolization(vscn, vyper, changeVscnSymbolizationForm);

        auditService.createVscn(
                vscn.getVyperNumber(),
                vscn.getVscnNumber(),
                AuditActivity.VSCN_CHANGE_SYMBOLIZATION,
                "changed Symbol to: " + changeVscnSymbolizationForm.display());

        return vscnService.saveVscn(vscn);
    }

    public void changeSymbolization(Vscn vscn, Vyper vyper, ChangeVscnSymbolizationForm changeVscnSymbolizationForm){

        String oldSymbolName  = "";
        if(!vscn.getSymbolization().getSymbols().isEmpty()) {
            oldSymbolName = vscn.getSymbolization().getSymbols().get(0).getName();
        }

        // add the symbol
        Symbol symbol = new Symbol();
        symbol.setObject(changeVscnSymbolizationForm.getSymbol());
        symbol.getSource().appointUser(securityService.user());

        vscn.getSymbolization().getSymbols().clear();
        vscn.getSymbolization().getSymbols().add(symbol);

        Component topsideSymbol = vscn.findComponentByName("Topside Symbol");
        if(topsideSymbol != null){
            ComponentInstance componentInstance = topsideSymbol.getInstances().get(0);
            if (componentInstance != null) {
                ComponentPriority componentPriority = componentInstance.getPriorities().get(0);
                componentPriority.getObject().replace("name",symbol.getName());
                componentPriority.getSource().appointUser(securityService.user());
            }
        }

        // add components for the new cust values
        changeVscnSymbolizationForm.getCustoms().forEach(customObject -> {
            Component component = vscn.findOrCreateComponent(
                    customObject.getName(),
                    new Source().appointUser(securityService.user()));
            component.clear();
            component.setValue(
                    0,
                    0,
                    customObject.getValue(),
                    Engineering.N,
                    new Source().appointUser(securityService.user())
            );

            component.getSource().appointUser(securityService.user());

            component.setKeyValue(
                    0,
                    0,
                    "ignoreBlank",
                    customObject.getIgnoreBlank(),
                    Engineering.N,
                    new Source().appointUser(securityService.user())
            );
        });

        // create/update the ecat
        Source source = new Source().appointUser(securityService.user());
        Component ecat = vscn.findOrCreateComponent("ECAT", source);
        ecat.clear();
        ecat.setValue(
                0,
                0,
                changeVscnSymbolizationForm.getEcat(),
                Engineering.N,
                new Source().appointUser(securityService.user())
        );


        // update the rest of the data
        custLoader.load(vyper, vscn);

        // update the display - do this after setting the selection
        SymbolObject symbolObject = symbol.getObject();
        String text = replacementService.replaceTemplateText(vscn, symbolObject.getPicture());
        symbolObject.setDisplay(text);

        String symbolGroup = "VYPER_"+ vscn.getFacility().getPdbFacility()+"_ASSY_FINISH";
        if(!StringUtils.equalsIgnoreCase(oldSymbolName, symbolObject.getName()) && !vscn.getChangedComponentsGroup().contains(symbolGroup)){
            vscn.getChangedComponentsGroup().add(symbolGroup);
        }
    }
}
