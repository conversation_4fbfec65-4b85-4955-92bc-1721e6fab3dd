package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeOperationCommentForm extends BuildNumberForm {

    @NotNull
    @Size(min = 1)
    private String operation;

    // nulls are allowed
    private String comment;
}
