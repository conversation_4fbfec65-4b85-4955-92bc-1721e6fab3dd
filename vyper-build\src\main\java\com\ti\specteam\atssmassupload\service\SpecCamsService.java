package com.ti.specteam.atssmassupload.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ti.specteam.atssmassupload.domain.DeviceComponent;
import com.ti.specteam.atssmassupload.domain.DeviceComponentPatch;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadException;
import com.ti.specteam.atssmassupload.exception.CamsMassUploadException;
import com.ti.specteam.vyper.vscn.model.AtssFieldError;
import com.ti.specteam.vyper.vscn.model.AtssScnStatus;
import com.ti.specteam.vyper.vscn.repository.VscnValidationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SpecCamsService {

    private final AtssApiService atssApiService;
    private final AtssComponentValidatorService atssComponentValidatorService;
    private final VscnValidationRepository vscnValidationRepository;
    private final ObjectMapper objectMapper;

    public Map<String, Object> createOrUpdateCams(List<String> deviceList, List<DeviceComponent> allCamsRequest){

        if ( allCamsRequest == null || allCamsRequest.isEmpty() ) {
            Map<String, Object> camsOkMap = new HashMap<>();
            camsOkMap.put("success", Boolean.TRUE);
            return camsOkMap;
        }

        List<DeviceComponent> newComponents = new ArrayList<>();
        List<DeviceComponent> existingComponents = new ArrayList<>();
        for (DeviceComponent component: allCamsRequest){
            // Paragraphs cannot be created or updated via vyper
            if (!component.getComponentType().contains("Paragraph")){
                if( component.isExisting()){
                    existingComponents.add(component);
                }else{
                    newComponents.add(component);
                }
            }
        }

        Map<String, Object> camsResult = null;
        Map<String, Object> camsResponse = new HashMap<>();
        try {
            camsResult = createNewCams(newComponents);
            HashMap<String, Object> camsResultBody = (HashMap<String, Object>) camsResult.get("body");
            camsResponse.put("success", camsResult.getOrDefault("success",Boolean.FALSE));
            if ( camsResultBody != null ){
                camsResponse.putIfAbsent("newCamsScnId", camsResultBody.getOrDefault("scnId",null));
                camsResponse.putIfAbsent("newCamsResponse", camsResultBody);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        //Update the cams
        if ( camsResult.getOrDefault("success", Boolean.FALSE).equals(Boolean.TRUE) ) {

            log.debug("Updating the existing components");
            try {
                camsResult =  updateCams(deviceList, existingComponents);
                HashMap<String, Object> camsResultBody = (HashMap<String, Object>) camsResult.get("body");
                camsResponse.put("success", camsResult.getOrDefault("success",Boolean.FALSE));
                if ( camsResultBody != null ){
                    camsResponse.putIfAbsent("updateCamsScnId", camsResponse.getOrDefault("scnId",null));
                    camsResponse.putIfAbsent("updateCamsResponse", camsResultBody);
                }

            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        return camsResponse;
    }

    public Map<String, Object> createNewCams(List<DeviceComponent> newCamsComponents) throws JsonProcessingException {
        log.debug("createNewCams {}", newCamsComponents);

        if ( newCamsComponents.isEmpty()){
            Map<String, Object> camsOkMap = new HashMap<>();
            camsOkMap.put("success", Boolean.TRUE);
            return camsOkMap;
        }

        try {
            return atssApiService.createNewCams(newCamsComponents);
        }
        catch( HttpClientErrorException requestException ) {
            if ( requestException.getMessage() != null && requestException.getMessage().startsWith("[{\"field\"") ){
                String validationErrors = requestException.getMessage().substring(requestException.getMessage().indexOf("[{\"field\""));
                List<AtssFieldError> errors = (List<AtssFieldError>) objectMapper.copy().readValue(validationErrors, List.class);
                throw new CamsMassUploadException("Issue in CAMS Create", errors);
            }
            throw requestException;
        } catch (JsonProcessingException e) {
            throw new AtssMassUploadException("Issue in creating the requested CAMS");
        } catch (Exception ex){
            throw new AtssMassUploadException("Issue in CAMS creation:"+ ex.getMessage());
        }
    }

    public Map<String, Object> updateCams(List<String> deviceList, List<DeviceComponent> existingComponents) throws JsonProcessingException {
        log.debug("updateCams {}", existingComponents);

        if ( existingComponents.isEmpty()){
            Map<String, Object> camsOkMap = new HashMap<>();
            camsOkMap.put("success", Boolean.TRUE);
            return camsOkMap;
        }

        List<DeviceComponentPatch> componentPatches =
                existingComponents.stream()
                .filter( deviceComponent -> deviceComponent.getAttributes() != null && !deviceComponent.getAttributes().isEmpty())
                .map( deviceComponent -> new DeviceComponentPatch(deviceComponent, deviceList))
                .collect(Collectors.toList());
        try {
            return atssApiService.updateExistingCams(componentPatches);
        } catch( HttpClientErrorException requestException ) {
            if ( requestException.getMessage() != null && requestException.getMessage().contains("[{\"field\"") ){
                String validationErrors = requestException.getMessage().substring(requestException.getMessage().indexOf("[{\"field\""));
                List<AtssFieldError> errors = (List<AtssFieldError>) objectMapper.copy().readValue(validationErrors, List.class);
                throw new CamsMassUploadException("Issue in CAMS Update", errors);
            }
            throw requestException;
        } catch(JsonProcessingException e) {
            throw new AtssMassUploadException("Issue in updating the requested CAMS");
        } catch (Exception ex){
            throw new AtssMassUploadException("Issue in CAMS Attribute update:"+ ex.getMessage());
        }
    }

    private boolean isNewComponent( DeviceComponent deviceComponent){
        Map<String, Object> componentData = validateComponentData(deviceComponent);
        String componentType = (String) componentData.getOrDefault("componentType","INVALID");
        // Unknown component name
        if ( !componentType.equals("INVALID") ){

            // Return false if it's Paragraph
            if ( componentType.equalsIgnoreCase("Paragraph") ){
                return false;
            }

            // Return true if component do not exist
            final Boolean componentPresent = (Boolean) componentData.getOrDefault("componentExists", Boolean.FALSE);
            return !componentPresent;
        }

        return false;

    }

    private Map<String, Object> validateComponentData( DeviceComponent deviceComponent){

        return atssComponentValidatorService.getAtssComponentData(deviceComponent.getFacility(),
                        deviceComponent.getName(),
                        deviceComponent.getValue());
    }

    public boolean isCamsScnActive(Map<String, Object> camsResult){
        AtssScnStatus scnStatus = null;
        // New cams scn
        if ( camsResult.get("newCamsScnId") != null){
            scnStatus = vscnValidationRepository.getScnStatus(Long.valueOf(camsResult.get("newCamsScnId").toString()));
            if ( scnStatus != null && !scnStatus.getScnStatus().startsWith("A")){
                return false;
            }
        }
        // Existing cams component scnid
        if ( camsResult.get("updateCamsScnId") != null){
            scnStatus = vscnValidationRepository.getScnStatus(Long.valueOf(camsResult.get("updateCamsScnId").toString()));
            return scnStatus == null || scnStatus.getScnStatus().startsWith("A");
        }

        return true;
    }

}
