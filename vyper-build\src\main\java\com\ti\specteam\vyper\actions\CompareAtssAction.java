package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.bdw.BdwService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.compare.CompareService;
import com.ti.specteam.vyper.atss.spectrav.SpecTravService;
import com.ti.specteam.vyper.apitraveler.export.TravelerToTextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.JDOMException;
import org.springframework.stereotype.Service;

import javax.xml.soap.SOAPException;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompareAtssAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final SpecTravService specTravService;
    private final TravelerToTextService travelerToTextService;
    private final BdwService bdwService;
    private final CompareService compareService;

    /**
     * Compare a Vyper (ATSS Style) traveler to an actual ATSS traveler.
     *
     * @param compareAtssForm CompareTravelerForm
     * @return CompareResult the results
     * @throws SOAPException If accessing SpecTravSS fails
     * @throws IOException   If accessing SpecTravSS fails
     * @throws JDOMException If parsing the traveler xml fails
     */
    public CompareTravelerResult execute(CompareAtssForm compareAtssForm) throws SOAPException, IOException, JDOMException {
        log.debug("execute(compareAtssForm:{})", compareAtssForm);

        @SuppressWarnings("unused")
        Vyper vyper = vyperService.fetchVyper(compareAtssForm.getVyperNumber());
        Build build = buildService.fetchBuild(compareAtssForm.getBuildNumber());

        CompareTravelerResult result = new CompareTravelerResult();

        String specDevice = bdwService.convertMaterialToSpecDevice(compareAtssForm.getMaterial());

        result.getDifferences().addAll(compareService.compare(
                travelerToTextService.getTraveler(build),
                specTravService.getTraveler(compareAtssForm.getFacility(), specDevice, compareAtssForm.getStatus())));

        return result;
    }

}
