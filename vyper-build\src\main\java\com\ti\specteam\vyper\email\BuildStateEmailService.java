package com.ti.specteam.vyper.email;

import com.ti.specteam.vyper.actions.NotifyAtApprovalForm;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.BuildState;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.buildlink.BuildLinkService;
import com.ti.specteam.vyper.taskService.Assignment;
import com.ti.specteam.vyper.taskService.TaskServiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class BuildStateEmailService {

    private final SecurityService securityService;
    private final EmailService emailService;
    private final BuildLinkService buildLinkService;
    private final TaskNotificationProperties taskNotificationProperties;
    private final TaskServiceService taskServiceService;

    private String ALL_GROUPS = "All Groups";

    public void sendAtReworkEmail(Vyper vyper, Build build, NotifyAtApprovalForm notifyAtApprovalForm) throws MessagingException {
        log.debug("sendReworkEmail(notifyAtApprovalForm: {})", notifyAtApprovalForm);

        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());

        EmailTableContext tableContext = new EmailTableContext();

        // Header And Base changes
        tableContext.setHeader(getHeader(build));
        tableContext.setSubheader("The build has been REWORKED");
        tableContext.setCallToAction(getCallToAction(build));
        tableContext.setLink(getBuildLink(vyper, build));
        tableContext.setSubject(getSubject(build));
        tableContext.getTos().addAll(getEmailList(vyper, build, notifyAtApprovalForm.getApprovalGroup()));

        // Adding Rows
        tableContext.put("Action", "Rework");
        tableContext.put("Build State", BuildState.DRAFT.name());
        tableContext.put("Build Number", build.getBuildNumber());
        tableContext.put("Submitter", user.getUsername());
        tableContext.put("Vyper Owner(s)", getOwnersAsString(vyper.getOwners()));
        tableContext.put("Status", "Reworked");
        tableContext.put("Rework Group", notifyAtApprovalForm.getApprovalGroup());
        tableContext.put("Rework Reason", notifyAtApprovalForm.getRejectReason());
        tableContext.put("Device", build.getMaterial().getMaterial());
        tableContext.put("Facility", build.getFacility().getPdbFacility());

        emailService.send(tableContext);
    }

    public void sendBuReworkEmail(Vyper vyper, Build build) throws MessagingException {
        log.debug("sendBuReworkEmail()");

        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());

        EmailTableContext tableContext = new EmailTableContext();

        // Header And Base changes
        tableContext.setHeader(getHeader(build));
        tableContext.setSubheader("The build has been REWORKED");
        tableContext.setCallToAction(getCallToAction(build));
        tableContext.setLink(getBuildLink(vyper, build));
        tableContext.setSubject(getSubject(build));
        tableContext.getTos().addAll(getEmailList(vyper, build, ALL_GROUPS));

        // Adding Rows
        tableContext.put("Action", "Rework");
        tableContext.put("Build State", BuildState.AT_REVIEW_CHANGE.name());
        tableContext.put("Build Number", build.getBuildNumber());
        tableContext.put("Submitter", user.getUsername());
        tableContext.put("Vyper Owner(s)", getOwnersAsString(vyper.getOwners()));
        tableContext.put("Status", "Reworked");
        tableContext.put("Rework Group", "BU");
        tableContext.put("Device", build.getMaterial().getMaterial());
        tableContext.put("Facility", build.getFacility().getPdbFacility());

        emailService.send(tableContext);
    }

    public void sendBuFinalApproveEmail(Vyper vyper, Build build, BuildState buildState) throws MessagingException {
        log.debug("sendBuFinalApproveEmail(buildState: {})", buildState);
        if (buildState != BuildState.BU_REVIEW_CHANGE && buildState != BuildState.FINAL_APPROVED) {
            return;
        }

        EmailTableContext tableContext = new EmailTableContext();

        // Header And Base changes
        tableContext.setHeader(getHeader(build));
        tableContext.setSubheader("The build has moved to " + buildState.name());
        tableContext.setCallToAction(getCallToAction(build));
        tableContext.setLink(getBuildLink(vyper, build));
        tableContext.setSubject(getSubject(build));
        tableContext.getTos().addAll(getEmailList(vyper, build, null));

        // Adding Rows
        if (buildState == BuildState.BU_REVIEW_CHANGE) {
            tableContext.put("Action", "BU Approval");

        } else {
            tableContext.put("Action", "Final Approval");
        }

        tableContext.put("Build State", build.getState().name());
        tableContext.put("Build Number", build.getBuildNumber());
        tableContext.put("Vyper Owner(s)", getOwnersAsString(vyper.getOwners()));
        tableContext.put("Status", "APPROVED");
        tableContext.put("Device", build.getMaterial().getMaterial());
        tableContext.put("Facility", build.getFacility().getPdbFacility());

        emailService.send(tableContext);
    }


    public void sendSubmitEmail(Vyper vyper, Build build) throws MessagingException {
        log.debug("sendSubmitEmail(buildState: SUBMITTED)");

        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());


        EmailTableContext tableContext = new EmailTableContext();

        // Header And Base changes
        tableContext.setHeader(getHeader(build));
        tableContext.setSubheader("The build has been SUBMITTED");
        tableContext.setCallToAction(getCallToAction(build));
        tableContext.setLink(getBuildLink(vyper, build));
        tableContext.setSubject(getSubject(build));
        tableContext.getTos().addAll(getEmailList(vyper, build, ALL_GROUPS));

        // Adding Rows
        tableContext.put("Action", "AT Approval");
        tableContext.put("Build State", build.getState().name());
        tableContext.put("Build Number", build.getBuildNumber());
        tableContext.put("Submitter", user.getUsername());
        tableContext.put("Vyper Owner(s)", getOwnersAsString(vyper.getOwners()));
        tableContext.put("Status", "SUBMITTED");
        tableContext.put("Device", build.getMaterial().getMaterial());
        tableContext.put("Facility", build.getFacility().getPdbFacility());

        emailService.send(tableContext);
    }

    public void sendAtApproveEmail(Vyper vyper, Build build, NotifyAtApprovalForm notifyAtApprovalForm) throws MessagingException {
        log.debug("sendAtApproveEmail(notifyAtApprovalForm: {})", notifyAtApprovalForm);


        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());


        EmailTableContext tableContext = new EmailTableContext();

        // Header And Base changes
        tableContext.setHeader(getHeader(build));
        tableContext.setSubheader("One Of the Approval Groups has APPROVED");
        tableContext.setCallToAction(getCallToAction(build));
        tableContext.setLink(getBuildLink(vyper, build));
        tableContext.setSubject(getSubject(build));
        tableContext.getTos().addAll(getEmailList(vyper, build, notifyAtApprovalForm.getApprovalGroup()));


        // Adding Rows
        tableContext.put("Action", "AT Approval");
        tableContext.put("Build State", build.getState().name());
        tableContext.put("Build Number", build.getBuildNumber());
        tableContext.put("Submitter", user.getUsername());
        tableContext.put("Vyper Owner(s)", getOwnersAsString(vyper.getOwners()));
        tableContext.put("Status", "APPROVED");
        tableContext.put("Approval Group", notifyAtApprovalForm.getApprovalGroup());
        tableContext.put("Device", build.getMaterial().getMaterial());
        tableContext.put("Facility", build.getFacility().getPdbFacility());

        emailService.send(tableContext);
    }

    private List<String> getEmailList(Vyper vyper, Build build, String approvalGroup) {

        Set<String> emailSet = new HashSet<>();

        for (User owner : vyper.getOwners()) {
            emailSet.add(owner.getUserid() + "@ti.com");
        }

        if (approvalGroup != null && taskNotificationProperties.getEnableEmails().equals("true")) {
            List<Assignment> assignments = taskServiceService.getTaskAssignments(build.getBuildNumber());

            for (Assignment assignment : assignments) {
                if (approvalGroup.equals(ALL_GROUPS)) {
                    emailSet.add(getUserIdEmail(assignment.getUserId()));
                } else if (assignment.getFnctName().equals(approvalGroup)) {
                    emailSet.add(getUserIdEmail(assignment.getUserId()));
                }
            }
        }

        return new ArrayList<>(emailSet);
    }

    public String getUserIdEmail(String userId) {
        // subcons contain gmail account as their userId
        if (userId.contains("@")) {
            return userId;
        } else {
            return userId + "@ti.com";
        }
    }

    private String getSubject(Build build) {
        return taskNotificationProperties.getSubject()
        .replaceAll("<buildnumber>",build.getBuildNumber())
        .replaceAll("<device>",build.getMaterial().getMaterial())
        .replaceAll("<facility>", build.getFacility().getPdbFacility())
        .replaceAll("<environment>", taskNotificationProperties.getEnvironment())
        .replaceAll("<sbe>", build.getMaterial().getSbe())
        .replaceAll("<sbe-1>",build.getMaterial().getSbe1());

    }

    private String getBuildLink(Vyper vyper, Build build) {
        return buildLinkService.getSingleBuildPage(
                vyper.getVyperNumber(),
                build.getBuildNumber()
        );
    }

    private String getHeader(Build build) {
        return taskNotificationProperties.getHeader()
                .replaceAll("<buildnumber>", build.getBuildNumber());

    }

    private String getCallToAction(Build build) {
        return taskNotificationProperties.getCalltoaction();

    }

    private String getOwnersAsString(List<User> owners) {
        ArrayList<String> usernames = new ArrayList<>();

        for (User owner : owners) {
            usernames.add(owner.getUsername());
        }
        return StringUtils.join(usernames, ", ");


    }
}