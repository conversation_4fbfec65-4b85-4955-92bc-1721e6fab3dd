package com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils;

/**
 * Wrapper class for DataTable page data.
 */
public final class DataTablePage {
    //---- Members
    private int sEcho;
    private int iTotalRecords;
    private int iTotalDisplayRecords;
    private Object aaData;

    //---- Methods
    /**
     * Main constructor.
     * @param sEcho The DataTable sEcho value.
     */
    public DataTablePage(final int sEcho) {
        this.sEcho = sEcho;
    }

    /**
     * sEcho getter.
     * @return sEcho value
     */
    public int getsEcho() {
        return sEcho;
    }

    /**
     * iTotalRecords getter.
     * @return iTotalRecods value
     */
    public int getiTotalRecords() {
        return iTotalRecords;
    }

    /**
     * iTotalRecords setter.
     * @param iTotalRecords value
     */
    public void setiTotalRecords(final int iTotalRecords) {
        this.iTotalRecords = iTotalRecords;
    }

    /**
     * iTotalDsiplayRecord getter.
     * @return value
     */
    public int getiTotalDisplayRecords() {
        return iTotalDisplayRecords;
    }

    /**
     * iTotalDisplayRecords setter.
     * @param iTotalDisplayRecords value
     */
    public void setiTotalDisplayRecords(final int iTotalDisplayRecords) {
        this.iTotalDisplayRecords = iTotalDisplayRecords;
    }

    /**
     * aaData getter.
     * @return aaData value
     */
    public Object getaaData() {
        // Should not return null as DataTable widget will choke
        if (aaData == null) {
            return new String[][] {};
        }
        return aaData;
    }

    /**
     * aaData setter.
     * @param data aaData value
     */
    public void setaaData(final Object data) {
        this.aaData = data;
    }
}
