package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_OPERATION_COMMENT;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeOperationCommentAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;

    public Build execute(ChangeOperationCommentForm changeOperationCommentForm) {
        log.debug("execute(changeOperationCommentForm:{})", changeOperationCommentForm);

        Vyper vyper = vyperService.fetchVyper(changeOperationCommentForm);
        Build build = buildService.fetchBuild(changeOperationCommentForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAt(vyper, build);
        validateService.checkEditable(vyper, build);

        FlowOperation flowOperation = build.getFlow().findFlowOperation(changeOperationCommentForm.getOperation());
        if (flowOperation == null) {
            return build;
        }

        flowOperation.setComment(changeOperationCommentForm.getComment());

        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_OPERATION_COMMENT,
                "changed operation comment to " + changeOperationCommentForm.getComment()
        );

        return buildService.saveBuild(build);
    }

}
