package com.ti.specteam.atssmassupload.domain;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class ProjectHeaderForm {
    @Size(max = 100)
    @NotNull
    private String projName;

    @Size(max = 30)
    @NotNull
    private String projType;

    @Size(max = 10)
    private String targetFacility;

    @Size(max = 30)
    private String status;

    @Size(max = 30)
    private String cmsNumber;

    @Size(max = 20)
    private String specDevice;

    @Size(max = 10)
    private String facilityAt;

    @Size(max = 1)
    private String refStatus;

}
