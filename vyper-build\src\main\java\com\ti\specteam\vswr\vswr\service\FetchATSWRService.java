package com.ti.specteam.vswr.vswr.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.DieLotStatus;
import com.ti.specteam.vswr.vswr.domain.ForecastedInfo;
import com.ti.specteam.vswr.vswr.domain.IntransitStatus;
import com.ti.specteam.vswr.vswr.domain.RequestorInfo;

public interface FetchATSWRService {

    public Map<String,String> helloWorld();
    
    public RequestorInfo fetchRequestorInfo(String aid);

    public List<HashMap<String, String>> fetchSwrTypeOptions(String vBuildType);

    public List<HashMap<String, String>> fetchPriorityOptions();
    
    public List<HashMap<String, String>> fetchOffloadInfoOptions();

    public List<HashMap<String, String>> fetchFinishedGoodOptions();

    public List<HashMap<String, String>> fetchWaferSkeletonOptions();

    public List<HashMap<String, String>> fetchDieLocationOptions();
    
    public HashMap<String, HashMap<String, String>> fetchShippToPlantOptions();

    public List<HashMap<String, String>> fetchStateOfFinishOptions();
    
    public ForecastedInfo fetchForecastedInfo(String forecastID);
    
    public ATSWRForm fetchVswr(String vswrID);

    public List<DieLotStatus> fetchDieLotStatus(String material, String plant, boolean inPlant);

    public List<IntransitStatus> fetchDieLotIntransitStatus(String material, String plant);
}
