package com.ti.specteam.vyper.topsymbol

import com.ti.specteam.vyper.atss.paragraph.ParagraphService
import com.ti.specteam.vyper.atss.spectrav.SpecTravService
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.BuildFlow
import com.ti.specteam.vyper.build.model.Symbol
import com.ti.specteam.vyper.build.model.SystemName
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.config.flowopn.DeviceFlowOperation
import spock.lang.Specification

import static com.ti.specteam.vyper.build.model.SymbolLocation.TOP

class TopSymbolLoaderSpec extends Specification {

    TopSymbolService topSymbolService = Mock(TopSymbolService)
    ParagraphService textService = Mock(ParagraphService)
    SpecTravService atssTravelerService = Mock(SpecTravService)

    TopSymbolLoader loader = new TopSymbolLoader(topSymbolService, textService, atssTravelerService)
    Vyper vyper1 = new Vyper(vyperNumber: "VYPER0123456")
    Build build1 = new Build(buildNumber: "VYPER0123456-0001")

    def setup() {
        0 * _

        build1.facility.object["PDBFacility"] = "FMX"
        build1.material.object["PackagePin"] = "14"
        build1.material.object["PackageDesignator"] = "D"
        build1.material.object["OldMaterial"] = "THE_OLD_MATERIAL"

        // Initialize BuildFlow and FlowRows
        def buildFlow = new BuildFlow()
        def flowRow = new DeviceFlowOperation(opnName: "Symbolization")
        buildFlow.flowRows = [flowRow] // Add flow rows as needed
        build1.setBuildFlow(buildFlow)
    }

    def "symbol comes from atss"() {

        when:
        loader.load(vyper1, build1)

        then:
        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "A") >> "THE_SYMBOL"
        1 * textService.getTextString("Topside Symbol", "THE_SYMBOL") >> "ABCDEFG"

        and:
        build1.symbolization.symbols.size() == 1
        build1.symbolization.symbols[0].source.system.name == SystemName.ATSS
        build1.symbolization.symbols[0].object.location == TOP
        build1.symbolization.symbols[0].object.name == "THE_SYMBOL"
        build1.symbolization.symbols[0].object.picture == "ABCDEFG"
    }

    def "symbol comes from topSymbolService"() {

        def topSymbol1 = new TopSymbol(componentValue: "VALUE1")

        when:
        loader.load(vyper1, build1)

        then:
        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "A") >> null
        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "W") >> null
        1 * topSymbolService.findByFacilityAtAndPinAndPkg("FMX", "14", "D") >> topSymbol1
        1 * textService.getTextString("Topside Symbol", "VALUE1") >> "ABCDEFG"

        and:
        build1.symbolization.symbols.size() == 1
        build1.symbolization.symbols[0].source.system.name == SystemName.VYPER
        build1.symbolization.symbols[0].object.location == TOP
        build1.symbolization.symbols[0].object.name == "VALUE1"
        build1.symbolization.symbols[0].object.picture == "ABCDEFG"
    }

    def "if no top symbol found, symbol is not added to build"() {

        when:
        loader.load(vyper1, build1)

        then:

        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "A") >> null
        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "W") >> null
        1 * topSymbolService.findByFacilityAtAndPinAndPkg("FMX", "14", "D") >> null

        and:
        build1.symbolization.symbols.isEmpty()
    }

    def "return false if symbol has changed"() {

        def symbol1 = new Symbol()
        symbol1.object.name == "OLD_NAME"
        build1.symbolization.symbols << symbol1

        when:
        def ret = loader.load(vyper1, build1)

        then:
        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "A") >> "THE_SYMBOL"
        1 * textService.getTextString("Topside Symbol", "THE_SYMBOL") >> "ABCDEFG"

        and:
        !ret
    }

    def "return true if symbol has didn't change"() {

        def symbol1 = new Symbol()
        symbol1.object.name == "THE_SYMBOL"
        build1.symbolization.symbols << symbol1

        when:
        def ret = loader.load(vyper1, build1)

        then:
        1 * atssTravelerService.getTopsideSymbol("FMX", "THE_OLD_MATERIAL", "A") >> "THE_SYMBOL"
        1 * textService.getTextString("Topside Symbol", "THE_SYMBOL") >> "ABCDEFG"

        and:
        !ret
    }

}
