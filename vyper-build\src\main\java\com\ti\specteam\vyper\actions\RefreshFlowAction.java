package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.dataloader.FlowLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.REFRESH_FLOW;

@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshFlowAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final FlowLoader flowLoader;
    private final AuditService auditService;

    public Build execute(RefreshFlowForm refreshFlowForm) {

        log.debug("execute(refreshFlowForm:{})", refreshFlowForm);

        Vyper vyper = vyperService.fetchVyper(refreshFlowForm);
        Build build = buildService.fetchBuild(refreshFlowForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        flowLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                REFRESH_FLOW,
                "refreshed flow"
        );

        return buildService.saveBuild(build);
    }

}
