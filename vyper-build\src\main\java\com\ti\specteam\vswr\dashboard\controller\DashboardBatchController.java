package com.ti.specteam.vswr.dashboard.controller;


import java.io.IOException;
import java.util.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import com.ti.specteam.vyper.security.user.UserUtilsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

import com.ti.specteam.vswr.dashboard.BatchProcessing.QuickReports.QuickReportsServiceImpl;
import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.specteam.vswr.dashboard.BatchProcessing.SCSWRService.SCSWRService;
import com.ti.specteam.vswr.dashboard.domain.BpDiff;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig;
import com.ti.specteam.vswr.dashboard.domain.security.VswrRolesContainer;
import com.ti.specteam.vswr.dashboard.service.VswrSecurityService;
import com.ti.specteam.vyper.security.SecurityService;

import lombok.extern.slf4j.Slf4j;

@RestController
@Validated
@Slf4j
@RequestMapping("/v1/batch")
public class DashboardBatchController {

    // Hello world test
    @Autowired
    private QuickReportsServiceImpl service;

    @Autowired
    private SCSWRService swrService;
    
    @Autowired
    private SecurityService securityService;

    @Autowired
    VswrSecurityService vswrSecurityService;

    @Autowired
    private UserUtilsService userUtilsService;
    
    @GetMapping("/getlistswrs")
    public ResponseEntity<List< Map<String, Object>>> getListSwrs(
        @RequestParam("swrTab") final String swrTab
        ){
            userUtilsService.validateUser();

            String uid = securityService.user().getUserid();
            log.info("getListSwrs(principal(name: {}))", uid);

            List<Map<String, Object>> userSec = swrService.getUserSecurity(uid);

            DataTableParams params = new DataTableParams();
            String tableName = "SCSWR_REDBULL_VIEW";
            String swrStat = null;
            List<Map<String, String>> siteExt = new ArrayList<Map<String, String>>();
            String sitecode, sitetype, siteext, adminlvl = siteext = sitetype = sitecode = "";
            List<String> siteextlist = new ArrayList<String>();
        
        if (swrTab != null && !"".equals(swrTab)) {
            if ("voidFcstSwr".equals(swrTab)) {
                swrStat = "'AT_Approved_Forecast', 'AT_Rejected_Forecast', "
                        + "'SBE_Forecast_Submitted', 'SBE_Forecast_Revised',"
                        + "'SBE_Action_Required', 'AT_Action_Required'";
            }
            if ("voidSwr".equals(swrTab)) {
                swrStat = "'Saved', 'Reject'";
            }
            if ("allSwr".equals(swrTab)) {
                swrStat = "'SBE_Forecast_Submitted'," +
                        "'SBE_Forecast_Revised'," +
                        "'AT_Approved_Forecast'," +
                        "'Saved'," +
                        "'AT_Rejected_Forecast'," +
                        "'In_Signoff'," +
                        "'Disapprove'";
            }
        }

        if (!userSec.isEmpty()) {
            for (Map m : userSec) {
                sitecode = (m.get("SITECODE_OVR") != null ? m.get("SITECODE_OVR").toString() : null);
                sitetype = (m.get("SITE_TYPE_OVR") != null ? m.get("SITE_TYPE_OVR").toString() : null);
                siteext = (m.get("SITE_EXT") != null ? m.get("SITE_EXT").toString() : null);
                adminlvl = (m.get("ADMIN_LEVEL") != null ? m.get("ADMIN_LEVEL").toString() : null);
            }

            if (siteext != null) {
                siteextlist = new ArrayList<String>(Arrays.asList(siteext.split(";")));
            }

            if (adminlvl == null || (adminlvl != null && !"Admin".equals(adminlvl))) {
                if ("A/T".equals(sitetype) || "SUB".equals(sitetype)) {
                    Map<String, String> m = new HashMap<String, String>();
                    m.put("atsite", sitecode);
                    siteExt.add(m);
                    if (siteext != null) {
                        for (String se : siteextlist) {
                            m = new HashMap<String, String>();
                            m.put("atsite", se);
                            siteExt.add(m);
                        }
                    }
                }
                if ("SBE".equals(sitetype)) {
                    Map<String, String> m = new HashMap<String, String>();
                    m.put("sbe1site", sitecode);
                    siteExt.add(m);
                    if (siteext != null) {
                        for (String se : siteextlist) {
                            m = new HashMap<String, String>();
                            m.put("sbe1site", se);
                            siteExt.add(m);
                        }
                    }
                }
                if ("BTH".equals(sitetype)) {
                    if (swrService.confirmAtSite(sitecode) > 0) {
                        Map<String, String> m = new HashMap<String, String>();
                        m.put("atsite", sitecode);
                        siteExt.add(m);
                    } else if (swrService.confirmSbeSite(sitecode) > 0) {
                        Map<String, String> m = new HashMap<String, String>();
                        m.put("sbe1site", sitecode);
                        siteExt.add(m);
                    }

                    if (siteext != null) {
                        for (String se : siteextlist) {
                            Map<String, String> m = new HashMap<String, String>();
                            if (swrService.confirmAtSite(se) > 0) {
                                m.put("atsite", se);
                            } else if (swrService.confirmSbeSite(se) > 0) {
                                m.put("sbe1site", se);
                            }
                            siteExt.add(m);
                        }
                    }

                }
            }
        }

        params.addFilter("REQUESTOR_EMPID",uid);
        List<Map<String, Object>> list = service.getPage(tableName, params, siteExt, null, swrStat);
        // service.getPage(tableName, params, siteExt, listColumns, swrStat);
        return ResponseEntity.ok(list);
    }

    @GetMapping("/getAtView")
    public ResponseEntity<List<Map<String, Object>>> getAtView(@RequestParam(required = false) String swrId
        ) throws ParseException {
        userUtilsService.validateUser();
        String tableName = "AT_FORECAST_REVISED_VW";

        VswrRolesContainer vswrRoles = vswrSecurityService.getVswrRolesByAid(securityService.userid());
    List<String> atRoles = vswrRoles.getAtRoles();
    List<Map<String, String>> siteExt = new ArrayList<Map<String, String>>();
        DataTableParams params = new DataTableParams();
        //if not at roles, pass a false statement to return no data
        if (atRoles == null || atRoles.isEmpty()) {
            siteExt.add(Map.of("1", "2"));
        }
        else if(vswrRoles.getAdministrativeLevel() == VswrSecurityService.AdministrativeLevels.ADMIN){
          siteExt.add(Map.of("1", "1"));
        }
        // else return whichever at's the user has access to
        else {
            atRoles.forEach((String at)->{
              siteExt.add(Map.of("atSite", at));
            });
        }
        
        List< Map<String, Object>> list = service.getPage(tableName, params, siteExt, "*", null);
        return ResponseEntity.ok(list);
    }
    
    @GetMapping(value = "/bp-diffs")
    public ResponseEntity<List<BpDiff>> getBpDiffs(@RequestParam( value="uploadedBy", required=false) String uploadedBy, @RequestParam(value = "swrId", required=false) String swrId) {
      List<BpDiff> bpDiffs = swrService.getBpDiffs(uploadedBy, swrId);
      return ResponseEntity.ok(bpDiffs);
    }

    @GetMapping("/getSbeView")
    public ResponseEntity<List<Map<String, Object>>> getSbeView(@RequestParam(required = false) String swrId
        ) throws ParseException {
        userUtilsService.validateUser();
        String uid = securityService.user().getUserid();
        log.info("getSbeView(principal(name: {}))", uid);

        List<Map<String, Object>> list = swrService.getSbeView(uid, swrId);
        List<Map<String, Object>> finalSbeView = new ArrayList<Map<String, Object>>();
        Map<String, Object> tempSbeView = new HashMap<String, Object>();
        Map<String, Object> tempSbeSwrView = new HashMap<String, Object>();
        String colData = "";
        List<ScswrExcelCellConfig> labels = swrService.getSbeExcelCfgs();

        int row = 1;
        String label = "";
        for (Map<String, Object> sbeview : list) {
            for (ScswrExcelCellConfig map : labels) {
                if (!"SWR_ID".equals(sbeview.get("COLUMN_NAME")) && !"UPLOADED_BY".equals(sbeview.get("COLUMN_NAME"))
                        && !"UPLOAD_DTTM".equals(sbeview.get("COLUMN_NAME"))
                        && !sbeview.get("COLUMN_NAME").toString().startsWith("R_")) {
                    if (map.getDbColumnName().equals(sbeview.get("COLUMN_NAME").toString())) {
                        label = map.getExcelLabel();
                    }
                }
            }
            if (row != Integer.parseInt(sbeview.get("ROW_ID").toString())) {
                tempSbeSwrView = new HashMap<String, Object>();
                row++;
            }
            if ("SWR_ID".equals(sbeview.get("COLUMN_NAME"))) {
                tempSbeSwrView.put("SWR_ID", sbeview.get("COLUMN_DATA"));
            } else if ("UPLOADED_BY".equals(sbeview.get("COLUMN_NAME"))) {
                tempSbeSwrView.put("UPLOADED_BY", sbeview.get("COLUMN_DATA"));
            } else if ("UPLOAD_DTTM".equals(sbeview.get("COLUMN_NAME"))) {
                tempSbeSwrView.put("UPLOAD_DTTM", sbeview.get("COLUMN_DATA"));
            } else if (!"SWR_ID".equals(sbeview.get("COLUMN_NAME")) && !"UPLOADED_BY".equals(sbeview.get("COLUMN_NAME"))
                    && !"UPLOAD_DTTM".equals(sbeview.get("COLUMN_NAME"))) {
                if (sbeview.get("COLUMN_NAME").toString().startsWith("R_")) {
                    if ("R_NEED_DATE".equals(sbeview.get("COLUMN_NAME").toString())) {
                        if (sbeview.get("COLUMN_DATA") != null) {
                            SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yy");
                            try {
                                Date date = sdf.parse(sbeview.get("COLUMN_DATA").toString());
                                sdf.applyPattern("MM/dd/yyyy");
                                colData = sdf.format(date);
                            } catch (ParseException p) {
                                colData = sbeview.get("COLUMN_DATA").toString();
                            }
                        } else {
                            colData = "";
                        }
                    } else {
                        colData = sbeview.get("COLUMN_DATA") != null ? sbeview.get("COLUMN_DATA").toString() : "";
                    }
                } else if (!"samevaluefrrequests".equals(sbeview.get("COLUMN_DATA"))) {
                    tempSbeView.put("BEFORE_COLUMN_DATA", colData);
                    tempSbeView.put("COLUMN_NAME", label);
                    tempSbeView.put("COLUMN_DATA", sbeview.get("COLUMN_DATA"));
                    colData = "";
                }
            }

            if (!tempSbeView.isEmpty() && tempSbeView.containsKey("BEFORE_COLUMN_DATA")
                    && tempSbeView.containsKey("COLUMN_DATA")) {
                tempSbeView.putAll(tempSbeSwrView);
                finalSbeView.add(tempSbeView);
                tempSbeView = new HashMap<String, Object>();
            }

        }
        if (!tempSbeView.isEmpty() && tempSbeView.containsKey("BEFORE_COLUMN_DATA")
                && tempSbeView.containsKey("COLUMN_DATA")) {
            tempSbeView.putAll(tempSbeSwrView);
            finalSbeView.add(tempSbeView);
        }

        return ResponseEntity.ok(finalSbeView);
    }

    @GetMapping("/getupdateswrs")
    public ResponseEntity<List<Map<String, Object>>> getUpdateSwrs(
            @RequestParam("tableName") final String tableName,
            @RequestParam("swrTab") final String swrTab
    ) throws ParseException {
        userUtilsService.validateUser();
        String uid = securityService.user().getUserid();

        log.info("getUpdateSwrs(tableName: {}, swrTab: {}, aid: {})", tableName, swrTab, uid);

        DataTableParams params = new DataTableParams();
        String swrStat = null;
        String submitSwrStat = null;

        if (swrTab != null && !"".equals(swrTab)) {

            if ("atApprovalSwr".equals(swrTab)) {
                swrStat = "'AT_Approved_Forecast', 'AT_Rejected_Forecast', "
                        + "'SBE_Forecast_Submitted', 'SBE_Forecast_Revised', "
                        + "'SBE_Action_Required', 'AT_Action_Required'";
            }
            if ("submitSwr".equals(swrTab)) {
                submitSwrStat = "r.swr_id in (select swrid from forecast_dates) and get_days_by_rules(r.swr_id) < SYSDATE";
            }
            if ("saveSwr".equals(swrTab)) {
                swrStat = "'In_Signoff', 'Disapprove'";
            }
            if ("voidFcstSwr".equals(swrTab)) {
                swrStat = "'AT_Approved_Forecast', 'AT_Rejected_Forecast', "
                        + "'SBE_Forecast_Submitted', 'SBE_Forecast_Revised'";
            }
            if ("voidSwr".equals(swrTab)) {
                swrStat = "'Saved', 'Reject'";
            }
        }

        params.addFilter("uploaded_by", uid);
        List< Map<String, Object>> list = swrService.swrListsByStat(tableName, swrStat, submitSwrStat, params);

        return ResponseEntity.ok(list);
    }

    @GetMapping("/fetch")
    public ResponseEntity<Map<String,String>> helloWorld(@RequestParam(required = false, value = "echo") String echo){
        Map<String,String> test = new HashMap<String,String>();
        test.put("Hello", "Worlds");
        test.put("echo", echo);

        return ResponseEntity.ok(test);
    }

    @RequestMapping(value = "/proceedUpdateRequests", method = RequestMethod.POST)
    public ResponseEntity<Object> proceedUpdateRequest(
            @RequestParam(required = true, value = "swrIds") List<String> swrIds,
            @RequestParam(required = false, value = "changeReason") String changeReason,
            @RequestParam(required = true, value = "swrUpdate") String swrUpdate
    ) throws IOException {
        userUtilsService.validateUser();
        log.info("proceedUpdateRequest(swrIds: {}, changeReason: {}, swrUpdate: {})", swrIds, changeReason, swrUpdate);
        String uid = securityService.user().getUserid();
        swrService.proceedUpdateRequest(swrIds, uid, changeReason, swrUpdate);
        return ResponseEntity.ok("Success");
    }

}
