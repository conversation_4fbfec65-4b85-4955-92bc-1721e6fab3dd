package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.ValidatedOperation;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;

import static com.ti.specteam.vyper.audit.AuditActivity.TOGGLE_OPERATION_VALIDATION;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ToggleOperationValidateAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final AuditService auditService;

    public Build execute(ToggleOperationValidateForm toggleOperationValidateForm) {

        Vyper vyper = vyperService.fetchVyper(toggleOperationValidateForm);
        Build build = buildService.fetchBuild(toggleOperationValidateForm);
        String operation = toggleOperationValidateForm.getOperation();

        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());
        String facility = build.getFacility().getPdbFacility();

        validateService.checkInGroup(facility, operation);
        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAt(vyper, build);
        validateService.checkEditable(vyper, build);

        ValidatedOperation validatedOperation = build.getValidatedOperations()
                .stream()
                .filter(vo -> StringUtils.equalsIgnoreCase(vo.getOperation(), toggleOperationValidateForm.getOperation()))
                .findFirst()
                .orElse(null);

        if (null == validatedOperation) {
            validatedOperation = new ValidatedOperation();
            validatedOperation.setOperation(toggleOperationValidateForm.getOperation());
            build.getValidatedOperations().add(validatedOperation);
        }

        String detail;

        if (null == validatedOperation.getWhen()) {
            validatedOperation.setUserid(user.getUserid());
            validatedOperation.setUsername(user.getUsername());
            validatedOperation.setWhen(OffsetDateTime.now());
            detail = "validated operation " + toggleOperationValidateForm.getOperation() + " validated by " + user.getUsername();
        } else {
            validatedOperation.setUserid(null);
            validatedOperation.setUsername(null);
            validatedOperation.setWhen(null);
            detail = "validated operation " + toggleOperationValidateForm.getOperation() + " un-validated by " + user.getUsername();
        }

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                TOGGLE_OPERATION_VALIDATION,
                detail
        );

        return buildService.saveBuild(build);
    }
}
