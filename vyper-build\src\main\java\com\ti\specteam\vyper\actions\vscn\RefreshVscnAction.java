package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.vscn.IVscnVerifier;
import com.ti.specteam.vyper.verifier.VerifierService;
import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshVscnAction {
    private final VyperService vyperService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final UserUtilsService userUtilsService;
    private final VerifierService verifierService;
    private final List<IVscnVerifier> vscnVerifiers;

    public Vscn execute(VscnNumberForm vscnNumberForm){
        log.debug("execute(vscnNumberForm:{})", vscnNumberForm);

        Vscn vscn = vscnService.fetchVscn(vscnNumberForm.getVscnNumber());
        Vyper vyper = vyperService.fetchVyper(vscn.getVyperNumber());
        String facility = vscn.getFacility().getPdbFacility();

        userUtilsService.validateUserByFacility(facility);
        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        execute(vyper,vscn);

        return vscnService.saveVscn(vscn);
    }

    public void execute(Vyper vyper,Vscn vscn){
        // initialize the verifiers
        verifierService.initializeVerifiers(vscn);

        // run the verifiers
        vscnVerifiers.parallelStream().forEach(iVscnVerifier -> iVscnVerifier.verify(vyper,vscn));
    }

}
