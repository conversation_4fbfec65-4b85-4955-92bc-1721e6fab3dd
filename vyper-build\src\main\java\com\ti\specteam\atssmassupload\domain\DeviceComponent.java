package com.ti.specteam.atssmassupload.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceComponent {
    String facility;
    String name;
    String value;
    Set<DeviceComponentAttribute> attributes;
    @JsonIgnore
    boolean isExisting = false;
    @JsonIgnore
    String componentType ;

}
