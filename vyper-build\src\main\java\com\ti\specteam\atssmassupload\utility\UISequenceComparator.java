package com.ti.specteam.atssmassupload.utility;

import java.util.Comparator;

import com.ti.specteam.atssmassupload.domain.SpecChange;

public class UISequenceComparator implements Comparator<SpecChange> {
	@Override
	public int compare(SpecChange specChange1, SpecChange specChange2) {
		return specChange1.getUiSequence().compareTo(specChange2.getUiSequence());
	}

	public UISequenceComparator() {
		super();

	}

}