package com.ti.specteam.vyper.build

import com.ti.specteam.vyper.Required
import com.ti.specteam.vyper.atss.attribute.AttributeService
import com.ti.specteam.vyper.atss.paragraph.ParagraphService
import com.ti.specteam.vyper.atss.paragraph.ReplacementService
import com.ti.specteam.vyper.build.componentmap.ComponentMap
import com.ti.specteam.vyper.build.componentmap.ComponentMapService
import com.ti.specteam.vyper.build.componentmap.util.ComponentUniqueness
import com.ti.specteam.vyper.build.model.*
import com.ti.specteam.vyper.pack.PackService
import com.ti.specteam.vyper.packageniche.PackageNicheService
import com.ti.specteam.vyper.security.SecurityService
import spock.lang.Specification

class TravelerRefreshServiceSpec extends Specification {

    AttributeService attributeService = Mock(AttributeService)
    ParagraphService textService = Mock(ParagraphService)
    ComponentMapService componentMapService = Mock(ComponentMapService)
    ReplacementService replacementService = Mock(ReplacementService)
    PackageNicheService packageNicheService = Mock(PackageNicheService)
    SecurityService securityService = Mock(SecurityService)
    PackService packService = Mock(PackService)
    List<String> custNames = List.of("CUST1", "CUST2")

    TravelerRefreshService service = new TravelerRefreshService(
            attributeService,
            textService,
            componentMapService,
            replacementService,
            packageNicheService,
            securityService,
            packService,
            custNames,
    )

    ComponentMap componentMap1 = new ComponentMap(name: "THE_NAME", atssComponentName: "THE_ATSS_NAME", componentUniqueness: ComponentUniqueness.SINGLE_VALUE)
    Source source1 = Source.BOM_TEMPLATE
    Source source2 = Source.ATSS

    def vyper1 = new Vyper()
    Build build1 = new Build()

    void setup() {
        0 * _
    }

    def "load operations creates operation"() {

        build1.flow.object.operations << new FlowOperation(name: "Backgrind", required: Required.REQUIRED)
        build1.flow.object.operations << new FlowOperation(name: "Tape / Reel", required: Required.OPTIONAL)

        when:
        service.loadOperations(build1)

        then:
        1 * packService.isPack("Backgrind") >> false
        1 * packService.isPack("Tape / Reel") >> true

        and:
        build1.traveler.operations[0].name == "Backgrind"
        build1.traveler.operations[0].required == Required.REQUIRED
        build1.traveler.operations[0].subflowType == "ASSEMBLY"
        build1.traveler.operations[0].engineering == Engineering.N

        build1.traveler.operations[1].name == "Tape / Reel"
        build1.traveler.operations[1].required == Required.OPTIONAL
        build1.traveler.operations[1].subflowType == "PACK"
        build1.traveler.operations[1].engineering == Engineering.N
    }

    def "load engineering operations creates operation"() {

        build1.flow.object.operations << new FlowOperation(name: "Backgrind", required: Required.REQUIRED, engineering: Engineering.Y)

        when:
        service.loadOperations(build1)

        then:
        1 * packService.isPack("Backgrind") >> false

        and:
        build1.traveler.operations[0].name == "Backgrind"
        build1.traveler.operations[0].required == Required.REQUIRED
        build1.traveler.operations[0].subflowType == "ASSEMBLY"
        build1.traveler.operations[0].engineering == Engineering.Y
    }

    def "load operations copies the engineering deleted flag"() {

        build1.flow.object.operations << new FlowOperation(name: "Backgrind", required: Required.REQUIRED, engineering: Engineering.Y, engineeringDeleted: true)

        when:
        service.loadOperations(build1)

        then:
        1 * packService.isPack("Backgrind") >> false

        and:
        build1.traveler.operations[0].engineeringDeleted
    }

    def "load operations creates traveler operations for each flow operation"() {

        def fo1 = new FlowOperation(name: "Backgrind", required: Required.REQUIRED)
        def fo2 = new FlowOperation(name: "Die Prep", required: Required.OPTIONAL)
        fo1.getSource().set(source1)
        fo2.getSource().set(source1)

        build1.flow.object.operations << fo1 << fo2

        when:
        service.loadOperations(build1)

        then:
        1 * packService.isPack("Backgrind") >> false
        1 * packService.isPack("Die Prep") >> false

        and:
        build1.traveler.operations.size() == 2
        build1.traveler.operations[0].name == "Backgrind"
        build1.traveler.operations[0].required == Required.REQUIRED
        build1.traveler.operations[0].source == source1
        build1.traveler.operations[0].engineering == Engineering.N
        build1.traveler.operations[1].name == "Die Prep"
        build1.traveler.operations[1].required == Required.OPTIONAL
        build1.traveler.operations[1].source == source1
        build1.traveler.operations[1].engineering == Engineering.N
    }

    def "loadComponents creates components"() {

        def fo1 = new FlowOperation(name: "Backgrind", required: Required.REQUIRED)
        fo1.getSource().set(source1)
        def fc1 = new FlowComponent(name: "Die", required: Required.REQUIRED)
        fc1.source.set(source2)
        fo1.components << fc1

        build1.flow.object.operations << fo1

        def to1 = new TravelerOperation(name: "Backgrind", required: Required.REQUIRED)
        build1.traveler.operations << to1

        build1.findOrCreateSelection("Backgrind", "Die", source1)

        ComponentMap componentMap1 = new ComponentMap()

        when:
        service.loadComponents(build1)

        then:
        1 * componentMapService.findByName("Die") >> componentMap1
        _ * securityService.user() >> new com.ti.specteam.vyper.security.user.User(userid: "THE_USERID", username: "THE_USERNAME")

        and:
        build1.traveler.operations.size() == 1
        build1.traveler.operations[0].components.size() == 1
        build1.traveler.operations[0].components[0].name == "Die"
        build1.traveler.operations[0].components[0].value == null
        build1.traveler.operations[0].components[0].required == Required.REQUIRED
    }

    def "loadComponents creates components with selected values"() {

        def fo1 = new FlowOperation(name: "Backgrind", required: Required.REQUIRED)
        fo1.getSource().set(source1)
        def fc1 = new FlowComponent(name: "Die", required: Required.REQUIRED)
        fc1.source.set(source2)
        fo1.components << fc1

        build1.flow.object.operations << fo1

        def to1 = new TravelerOperation(name: "Backgrind", required: Required.REQUIRED)
        build1.traveler.operations << to1

        Selection selection1 = build1.findOrCreateSelection("Backgrind", "Die", source1)
        selection1.addItem("THE_VALUE", Engineering.N, source1)

        ComponentMap componentMap1 = new ComponentMap()

        when:
        service.loadComponents(build1)

        then:
        1 * componentMapService.findByName("Die") >> componentMap1
        _ * securityService.user() >> new com.ti.specteam.vyper.security.user.User(userid: "THE_USERID", username: "THE_USERNAME")

        and:
        build1.traveler.operations.size() == 1
        build1.traveler.operations[0].components.size() == 1
        build1.traveler.operations[0].components[0].name == "Die"
        build1.traveler.operations[0].components[0].value == "THE_VALUE"
        build1.traveler.operations[0].components[0].engineering == Engineering.N
        build1.traveler.operations[0].components[0].required == Required.REQUIRED
    }

    def "loadComponents creates components with multiple selected values and sets the priority"() {

        def fo1 = new FlowOperation(name: "Backgrind", required: Required.REQUIRED)
        fo1.getSource().set(source1)
        def fc1 = new FlowComponent(name: "Die", required: Required.REQUIRED)
        fc1.source.set(source2)
        fo1.components << fc1

        build1.flow.object.operations << fo1

        def to1 = new TravelerOperation(name: "Backgrind", required: Required.REQUIRED)
        build1.traveler.operations << to1

        Selection selection1 = build1.findOrCreateSelection("Backgrind", "Die", source1)
        selection1.addItem("VALUE1", Engineering.N, source1)
        selection1.addItem("VALUE2", Engineering.N, source1)
        selection1.addItem("VALUE3", Engineering.N, source1)

        ComponentMap componentMap1 = new ComponentMap()

        when:
        service.loadComponents(build1)

        then:
        1 * componentMapService.findByName("Die") >> componentMap1
        _ * securityService.user() >> new com.ti.specteam.vyper.security.user.User(userid: "THE_USERID", username: "THE_USERNAME")

        and:
        build1.traveler.operations.size() == 1
        build1.traveler.operations[0].components.size() == 3
        build1.traveler.operations[0].components[0].name == "Die"
        build1.traveler.operations[0].components[0].value == "VALUE1"
        build1.traveler.operations[0].components[0].engineering == Engineering.N
        build1.traveler.operations[0].components[0].required == Required.REQUIRED
        build1.traveler.operations[0].components[0].priority == 1

        build1.traveler.operations[0].components[1].name == "Die"
        build1.traveler.operations[0].components[1].value == "VALUE2"
        build1.traveler.operations[0].components[1].engineering == Engineering.N
        build1.traveler.operations[0].components[1].required == Required.REQUIRED
        build1.traveler.operations[0].components[1].priority == 2

        build1.traveler.operations[0].components[2].name == "Die"
        build1.traveler.operations[0].components[2].value == "VALUE3"
        build1.traveler.operations[0].components[2].engineering == Engineering.N
        build1.traveler.operations[0].components[2].required == Required.REQUIRED
        build1.traveler.operations[0].components[2].priority == 3
    }

    def "update selection - 1 to 1"() {
        build1.facility.object.PDBFacility = "FMX"

        TravelerOperation to1 = new TravelerOperation(subflowType: "ASSY", name: "Mount", required: Required.REQUIRED)
        TravelerComponent tc1 = new TravelerComponent(name: "Mount Cure", value: "AAA", required: Required.REQUIRED)
        to1.components << tc1
        build1.traveler.operations << to1

        def selection1 = new Selection(operation: "Mount", name: "Mount Cure")
        selection1.items << new SelectionItem(value: "BBB", engineering: Engineering.Y)
        build1.selections << selection1

        when:
        service.updateSelection(vyper1, build1, selection1)

        then:
        2 * componentMapService.findByName("Mount Cure") >> componentMap1
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "BBB") >> []
        1 * textService.getTextString("Mount Cure", "BBB") >> null

        and:
        build1.traveler.operations[0].components[0].value == "BBB"
        build1.traveler.operations[0].components.size() == 1
    }

    def "update selection - 1 to 1 with surrounding components"() {
        build1.facility.object.PDBFacility = "FMX"

        TravelerOperation to1 = new TravelerOperation(subflowType: "ASSY", name: "Mount", required: Required.REQUIRED)
        TravelerComponent tc1 = new TravelerComponent(name: "BEFORE_NAME", value: "BEFORE_VALUE", required: Required.REQUIRED)
        TravelerComponent tc2 = new TravelerComponent(name: "Mount Cure", value: "AAA", required: Required.REQUIRED)
        TravelerComponent tc3 = new TravelerComponent(name: "AFTER_NAME", value: "AFTER_VALUE", required: Required.REQUIRED)
        to1.components << tc1 << tc2 << tc3
        build1.traveler.operations << to1

        def selection1 = new Selection(operation: "Mount", name: "Mount Cure")
        selection1.items << new SelectionItem(value: "BBB", engineering: Engineering.N)
        build1.selections << selection1

        when:
        service.updateSelection(vyper1, build1, selection1)

        then:
        2 * componentMapService.findByName("Mount Cure") >> componentMap1
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "BBB") >> []
        1 * textService.getTextString("Mount Cure", "BBB") >> null

        and:
        build1.traveler.operations[0].components[0].value == "BEFORE_VALUE"
        build1.traveler.operations[0].components[1].value == "BBB"
        build1.traveler.operations[0].components[2].value == "AFTER_VALUE"
        build1.traveler.operations[0].components.size() == 3
    }

    def "update selection - 2 to 2 with surrounding components"() {
        build1.facility.object.PDBFacility = "FMX"

        TravelerOperation to1 = new TravelerOperation(subflowType: "ASSY", name: "Mount", required: Required.REQUIRED)
        TravelerComponent tc1 = new TravelerComponent(name: "BEFORE_NAME", value: "BEFORE_VALUE", required: Required.REQUIRED)
        TravelerComponent tc2 = new TravelerComponent(name: "Mount Cure", value: "AAA", required: Required.REQUIRED)
        TravelerComponent tc3 = new TravelerComponent(name: "Mount Cure", value: "BBB", required: Required.REQUIRED)
        TravelerComponent tc4 = new TravelerComponent(name: "AFTER_NAME", value: "AFTER_VALUE", required: Required.REQUIRED)
        to1.components << tc1 << tc2 << tc3 << tc4
        build1.traveler.operations << to1

        def selection1 = new Selection(operation: "Mount", name: "Mount Cure")
        selection1.items << new SelectionItem(value: "111", engineering: Engineering.N)
        selection1.items << new SelectionItem(value: "222", engineering: Engineering.N)
        build1.selections << selection1

        when:
        service.updateSelection(vyper1, build1, selection1)

        then:
        3 * componentMapService.findByName("Mount Cure") >> componentMap1
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "111") >> []
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "222") >> []
        1 * textService.getTextString("Mount Cure", "111") >> null
        1 * textService.getTextString("Mount Cure", "222") >> null

        and:
        build1.traveler.operations[0].components[0].value == "BEFORE_VALUE"
        build1.traveler.operations[0].components[1].value == "111"
        build1.traveler.operations[0].components[2].value == "222"
        build1.traveler.operations[0].components[3].value == "AFTER_VALUE"
        build1.traveler.operations[0].components.size() == 4
    }

    def "update selection - 1 to 2 with surrounding components"() {
        build1.facility.object.PDBFacility = "FMX"

        TravelerOperation to1 = new TravelerOperation(subflowType: "ASSY", name: "Mount", required: Required.REQUIRED)
        TravelerComponent tc1 = new TravelerComponent(name: "BEFORE_NAME", value: "BEFORE_VALUE", required: Required.REQUIRED)
        TravelerComponent tc2 = new TravelerComponent(name: "Mount Cure", value: "AAA", required: Required.REQUIRED)
        TravelerComponent tc3 = new TravelerComponent(name: "AFTER_NAME", value: "AFTER_VALUE", required: Required.REQUIRED)
        to1.components << tc1 << tc2 << tc3
        build1.traveler.operations << to1

        def selection1 = new Selection(operation: "Mount", name: "Mount Cure")
        selection1.items << new SelectionItem(value: "111", engineering: Engineering.N)
        selection1.items << new SelectionItem(value: "222", engineering: Engineering.N)
        build1.selections << selection1

        when:
        service.updateSelection(vyper1, build1, selection1)

        then:
        3 * componentMapService.findByName("Mount Cure") >> componentMap1
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "111") >> []
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "222") >> []
        1 * textService.getTextString("Mount Cure", "111") >> null
        1 * textService.getTextString("Mount Cure", "222") >> null

        and:
        build1.traveler.operations[0].components[0].name == "BEFORE_NAME"
        build1.traveler.operations[0].components[0].value == "BEFORE_VALUE"
        build1.traveler.operations[0].components[1].name == "Mount Cure"
        build1.traveler.operations[0].components[1].value == "111"
        build1.traveler.operations[0].components[2].name == "Mount Cure"
        build1.traveler.operations[0].components[2].value == "222"
        build1.traveler.operations[0].components[3].name == "AFTER_NAME"
        build1.traveler.operations[0].components[3].value == "AFTER_VALUE"
        build1.traveler.operations[0].components.size() == 4
    }

    def "update selection - 2 to 1 with surrounding components"() {
        build1.facility.object.PDBFacility = "FMX"

        TravelerOperation to1 = new TravelerOperation(subflowType: "ASSY", name: "Mount", required: Required.REQUIRED)
        TravelerComponent tc1 = new TravelerComponent(name: "BEFORE_NAME", value: "BEFORE_VALUE", required: Required.REQUIRED)
        TravelerComponent tc2 = new TravelerComponent(name: "Mount Cure", value: "AAA", required: Required.REQUIRED)
        TravelerComponent tc3 = new TravelerComponent(name: "Mount Cure", value: "BBB", required: Required.REQUIRED)
        TravelerComponent tc4 = new TravelerComponent(name: "AFTER_NAME", value: "AFTER_VALUE", required: Required.REQUIRED)
        to1.components << tc1 << tc2 << tc3 << tc4
        build1.traveler.operations << to1

        def selection1 = new Selection(operation: "Mount", name: "Mount Cure")
        selection1.items << new SelectionItem(value: "111", engineering: Engineering.N)
        build1.selections << selection1

        when:
        service.updateSelection(vyper1, build1, selection1)

        then:
        2 * componentMapService.findByName("Mount Cure") >> componentMap1
        1 * attributeService.findByName("FMX", "THE_ATSS_NAME", "111") >> []
        1 * textService.getTextString("Mount Cure", "111") >> null

        and:
        build1.traveler.operations[0].components[0].name == "BEFORE_NAME"
        build1.traveler.operations[0].components[0].value == "BEFORE_VALUE"
        build1.traveler.operations[0].components[1].name == "Mount Cure"
        build1.traveler.operations[0].components[1].value == "111"
        build1.traveler.operations[0].components[2].name == "AFTER_NAME"
        build1.traveler.operations[0].components[2].value == "AFTER_VALUE"
        build1.traveler.operations[0].components.size() == 3
    }

    def "loadParagraphs will call replacementService for the paragraphs"() {
        def to1 = new TravelerOperation(subflowType: "ASSY", name: "Mount")
        def tc1 = new TravelerComponent(name: "Opn Txt - Assy", value: "AAA", required: Required.REQUIRED,)
        to1.components << tc1
        build1.traveler.operations << to1

        when:
        service.loadParagraphs(build1)

        then:
        1 * textService.getAllParagraphComponentNames() >> Map.of("Opn Txt - Assy", "Opn Txt - Assy")
        1 * textService.getTextString("Opn Txt - Assy", "AAA") >> "THE_OLD_TEXT"
        1 * replacementService.replaceTemplateText(build1, "THE_OLD_TEXT") >> "THE_NEW_TEXT"

        and:
        tc1.paragraph == "THE_NEW_TEXT"
    }

    def "incoming wafer thick is added if not null"() {

        def die1 = new Die()
        die1.object.name = "THE_DIE_NAME"
        die1.incomingWaferThick = 123
        def dieInstance1 = new DieInstance()
        dieInstance1.dies << die1
        build1.dies.dieInstances << dieInstance1

        def tc1 = new TravelerComponent(name: "Die", value: "THE_DIE_NAME")

        when:
        service.addDieAttributes(build1, tc1)

        then:
        tc1.attributes.size() == 1
        tc1.attributes[0].name == "Incoming Wafer Thick"
        tc1.attributes[0].value == "123UM"
    }

    def "isTestOperation returns true if the operation is in the test"() {

        def fo = new FlowOperation(name: "Final Test 1")
        build1.test.travelerOperations << new TravelerOperation(name: "Final Test 1")

        expect:
        service.isTestOperation(build1, fo)
    }

    def "isTestOperation returns false if the operation is not in the test"() {

        def fo = new FlowOperation(name: "Final Test 1")
        build1.test.travelerOperations << new TravelerOperation(name: "Rescreen")

        expect:
        !service.isTestOperation(build1, fo)
    }

    def "subflow is copied from flow operation to traveler operation"() {

        build1.flow.object.operations << new FlowOperation(name: "Bond", subFlow: SubFlow.ASSEMBLY)
        build1.flow.object.operations << new FlowOperation(name: "Final Test 1", subFlow: SubFlow.TEST)
        build1.flow.object.operations << new FlowOperation(name: "Pack", subFlow: SubFlow.PACK)

        when:
        service.loadOperations(build1)

        then:
        build1.traveler.operations[0].subflowType == "ASSEMBLY"
        build1.traveler.operations[1].subflowType == "TEST"
        build1.traveler.operations[2].subflowType == "PACK"
    }

    def "loadOperations handles null subflows"() {
        build1.flow.object.operations << new FlowOperation(name: "Bond", subFlow: null)

        when:
        service.loadOperations(build1)

        then:
        1 * packService.isPack("Bond") >> false

        notThrown NullPointerException
    }

    def "if atss doesn't have the mb diagram revision, then the value in build.components is used"() {

        given: "build.components have revision B for the MB Diagram"

        ComponentPriority cp1 = new ComponentPriority(object: ["name": "10030712", "Revision": "B"])
        ComponentInstance ci1 = new ComponentInstance()
        ci1.priorities.add cp1
        Component c1 = new Component(name: "MB Diagram")
        c1.instances.add ci1
        build1.components.add c1

        and: "The traveler has the MB Diagram component"

        TravelerComponent tc1 = new TravelerComponent(
                name: "MB Diagram",
                value: "10030712",
                required: Required.REQUIRED
        )
        TravelerOperation to1 = new TravelerOperation(
                subflowType: "ASSEMBLY",
                name: "Bond",
                required: Required.REQUIRED,
                engineering: Engineering.N,
        )
        to1.components.add tc1
        build1.traveler.operations.add to1

        and: "atss doesn't have attributes for the mb diagram"

        1 * attributeService.findByComponentValues(null, ["MB Diagram"], ["10030712"]) >> ["MB Diagram10030712": []]

        when: "the traveler attributes are loaded"

        service.loadAttributes(build1)
        service.updateMbDiagramRevision(build1)

        then: "The traveler attributes will have the B Revision"

        tc1.attributes[0].name == "Revision"
        tc1.attributes[0].value == "B"
    }

}
