package com.ti.specteam.vswr.vswr.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.Comment;
import com.ti.specteam.vswr.vswr.domain.DieInfo;
import com.ti.specteam.vswr.vswr.domain.DieLotInfo;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.domain.MaterialInfo;
import com.ti.specteam.vswr.vswr.repository.ATSWRPostDao;
import com.ti.specteam.vswr.vswr.service.PostATSWRService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PostATSWRServiceImpl implements PostATSWRService{
    @Autowired
    ATSWRPostDao atswrPostDao;

    private List<Comment> commentObjtoList(HashMap<String, List<Comment>> comments){
        List<Comment> listComments = new ArrayList<Comment>();
        comments.forEach((key, value) -> {
            listComments.addAll(value);
        });
        return listComments;
    }

    private List<DieLotInfo> getAllDieLots(List<DieInfo> dieInfo){
        List<DieLotInfo> dieLots = new ArrayList<DieLotInfo>();
        for(DieInfo die : dieInfo){
            dieLots.addAll(die.getDieLots());
        }
        return dieLots;
    }

    @Transactional
    public GeneralInfo saveForm(ATSWRForm atswrForm){
        log.info("insertForm called from PostATSWRServiceImpl");

        String vswrID = atswrPostDao.getNewVswrID();

        GeneralInfo generalInfo = atswrForm.getGeneralInfo();
        generalInfo.setVswrID(vswrID);


        atswrPostDao.insertGeneral(vswrID, atswrForm.getGeneralInfo(), atswrForm.getRequestorInfo());
        atswrPostDao.insertDevice(vswrID, atswrForm.getDeviceInfo());
        // atswrPostDao.insertAssembly(vswrID, atswrForm.getAssemblyInfo());
        
        List<MaterialInfo> bomInfo = atswrForm.getBomInfo();
        if(!bomInfo.isEmpty()){
            atswrPostDao.insertBom(vswrID, bomInfo);
        }
        
        atswrPostDao.insertDies(vswrID, atswrForm.getDieInfo());

        List<DieLotInfo> dieLots = getAllDieLots(atswrForm.getDieInfo());

        if (dieLots != null && !dieLots.isEmpty()) {
            atswrPostDao.insertDieLots(vswrID, dieLots);
        }

        // List<MaterialInfo> packingMaterials = atswrForm.getPackingMaterial();
        // if(!packingMaterials.isEmpty()){
        //     atswrPostDao.insertPackingMaterial(vswrID, packingMaterials);
        // }
        
        // atswrPostDao.insertPackingRequirements(vswrID, atswrForm.getPackingRequirements());
        // atswrPostDao.insertShippingInfo(vswrID, atswrForm.getShippingInfo());
        
        List<Comment> comments = commentObjtoList(atswrForm.getComments());
        if(!comments.isEmpty()){
            atswrPostDao.insertComments(vswrID, comments);
        }

        return generalInfo;
    }
 
    @Transactional
    public GeneralInfo updateForm(ATSWRForm atswrForm){
        log.info("updateForm called from PostATSWRServiceImpl");
        
        GeneralInfo generalInfo = atswrForm.getGeneralInfo();
        String swrID = generalInfo.getVswrID();

        atswrPostDao.updateGeneral(atswrForm.getGeneralInfo(), atswrForm.getRequestorInfo());

        atswrPostDao.resetAllDieSelection(swrID);
        for (DieInfo die : atswrForm.getDieInfo()) {
            if (die.isSelected()) {
                atswrPostDao.updateDieSelection(swrID, die.getMatlMasterDieName(), "Y");
            }
        }

        atswrPostDao.deleteDieLots(swrID);
        List<DieLotInfo> dieLots = getAllDieLots(atswrForm.getDieInfo());
        if (!dieLots.isEmpty()){
            atswrPostDao.insertDieLots(swrID, dieLots);
        }

        List<Comment> comments = commentObjtoList(atswrForm.getComments());
        if(!comments.isEmpty()){
            atswrPostDao.deleteComments(swrID);
            atswrPostDao.insertComments(swrID, comments);
        }

        return generalInfo;
    }
}