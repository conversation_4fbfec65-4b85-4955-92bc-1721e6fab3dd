package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class UpdateBuildForm extends BuildNumberForm {

    @NotNull
    @Size(min = 1)
    private String mode;

    @NotNull
    @Size(min = 1)
    private String material;

    @NotNull
    @Size(min = 1)
    private String facility;

    @NotNull
    private boolean multiBuild;

    @NotNull
    @Size(min = 1)
    private String specDevice;

    @NotNull
    @Size(min = 1)
    private String description;

    @NotNull
    @Size(min = 1)
    private String buildType;

    private String copyBuildNumber;

}
