package com.ti.specteam.vswr.vswr.repository;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.ti.specteam.vswr.vswr.domain.AssemblyInfo;
import com.ti.specteam.vswr.vswr.domain.Comment;
import com.ti.specteam.vswr.vswr.domain.DeviceInfo;
import com.ti.specteam.vswr.vswr.domain.DieInfo;
import com.ti.specteam.vswr.vswr.domain.DieLotInfo;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.domain.MaterialInfo;
import com.ti.specteam.vswr.vswr.domain.PackingRequirements;
import com.ti.specteam.vswr.vswr.domain.RequestorInfo;
import com.ti.specteam.vswr.vswr.domain.ShippingInfo;

@Repository
public interface ATSWRPostDao {

    public String getNewVswrID();

    public void insertGeneral(
        @Param("vswrID") String vswrID,
        @Param("generalInfo") GeneralInfo generalInfo,
        @Param("requestorInfo") RequestorInfo requestorInfo
    );

    public void insertDevice(
        @Param("vswrID") String vswrID,
        @Param("deviceInfo") DeviceInfo deviceInfo
    );

    public void insertAssembly(
        @Param("vswrID") String vswrID,
        @Param("assemblyInfo") AssemblyInfo assemblyInfo
    );

    public void insertBom(
        @Param("vswrID") String vswrID,
        @Param("bomInfo") List<MaterialInfo> bomInfo
    );

    public void insertDies(
        @Param("vswrID") String vswrID,
        @Param("dieInfo") List<DieInfo> dieInfo
    );

    public void insertDieLots(
        @Param("vswrID") String vswrID,
        @Param("dieLotInfo") List<DieLotInfo> dieLotInfo
    );

    public void insertPackingMaterial(
        @Param("vswrID") String vswrID,
        @Param("packingMaterials") List<MaterialInfo> packingMaterials
    );

    public void insertComments(
        @Param("vswrID") String vswrID,
        @Param("comments") List<Comment> comments
    );

    public void insertPackingRequirements(
        @Param("vswrID") String vswrID,
        @Param("packingReq") PackingRequirements packingReq
    );

    public void insertShippingInfo(
        @Param("vswrID") String vswrID,
        @Param("shippingInfo") List<ShippingInfo> shippingInfo
    );

    public void updateGeneral(
        @Param("generalInfo") GeneralInfo generalInfo,
        @Param("requestorInfo") RequestorInfo requestorInfo
    );

    public void updateDieSelection(
            @Param("vswrID") String vswrID,
            @Param("matlMasterdieName") String matlMasterdieName,
            @Param("selected") String selected
    );

    public void resetAllDieSelection(@Param("vswrID") String vswrID);

    public void deleteDieLots(@Param("vswrID") String vswrID);
    
    public void deleteComments(@Param("vswrID") String vswrID);
}
