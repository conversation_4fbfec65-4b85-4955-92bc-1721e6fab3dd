package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.atss.attribute.AttributeService;
import com.ti.specteam.vyper.atss.paragraph.ParagraphService;
import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeVscnSelectionAction {
    private final VyperService vyperService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final AttributeService attributeService;
    private final UserUtilsService userUtilsService;
    private final SecurityService securityService;
    private final ComponentMapService componentMapService;
    private final ParagraphService textService;


    public Vscn execute(ChangeVscnSelectionForm changeVscnSelectionForm) {
        log.debug("execute(changeVscnSelectionForm:{})", changeVscnSelectionForm);

        Vscn vscn = vscnService.fetchVscn(changeVscnSelectionForm.getVscnNumber());
        Vyper vyper = vyperService.fetchVyper(vscn.getVyperNumber());

        String facility = vscn.getFacility().getPdbFacility();
        String componentFormName = changeVscnSelectionForm.getComponent();

        userUtilsService.validateUserByFacility(facility);
        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        // replace the component value
        List<TravelerComponent> travelerComponentList = vscn.getTraveler().getOperations().stream()
                .filter(travelerOperation -> StringUtils.equalsIgnoreCase(travelerOperation.getSubflowType(), "PACK") && StringUtils.equalsIgnoreCase(travelerOperation.getName(), changeVscnSelectionForm.getOperation()))
                .flatMap(travelerOperation -> travelerOperation.getComponents().stream())
                .filter(travelerComponent -> StringUtils.equalsIgnoreCase(travelerComponent.getName(), componentFormName))
                .collect(Collectors.toList());

        int i = 0;
        for (TravelerComponent travelerComponent : travelerComponentList) {
            SelectionItem selectionItem = changeVscnSelectionForm.getItems().get(i);
            i++;
            travelerComponent.setValue(selectionItem.getValue());
            addQueryAttributes(vscn, travelerComponent);
            travelerComponent.getSourceValue().appointUser(securityService.user());

            String text = textService.getTextString(travelerComponent.getName(), travelerComponent.getValue());
            travelerComponent.setParagraph(text);
        }

        auditService.createVscn(
                vscn.getVyperNumber(),
                vscn.getVscnNumber(),
                AuditActivity.VSCN_CHANGE_SELECTION,
                "Changed selection of " + changeVscnSelectionForm.getComponent() +" to "+ changeVscnSelectionForm.display());

        return vscnService.saveVscn(vscn);
    }

    /**
     * Add attributes for the component - query the database for them
     *
     * @param vscn              {@link Vscn} the vscn.
     * @param travelerComponent {@link TravelerComponent} the traveler component.
     */
    public void addQueryAttributes(Vscn vscn, TravelerComponent travelerComponent) {
        // query for the attributes
        String facilityAt = vscn.getFacility().getPdbFacility();

        // get the atss component name for the traveler component
        ComponentMap componentMap = componentMapService.findByName(travelerComponent.getName());
        String atssName;
        if (componentMap == null) {
            atssName = travelerComponent.getName();
        } else {
            atssName = componentMap.getAtssComponentName();
        }

        // get the attributes
        List<Map<String, Object>> attrs = attributeService.findByName(facilityAt, atssName, travelerComponent.getValue());

        // loop through the attributes
        for (Map<String, Object> attr : attrs) {
            String name = (String) attr.get("key");
            String value = attr.get("value").toString();

            // create the attribute
            TravelerAttribute travelerAttribute = new TravelerAttribute();
            travelerAttribute.setName(name);
            travelerAttribute.setValue(value);
            travelerComponent.getAttributes().add(travelerAttribute);
        }
    }
}
