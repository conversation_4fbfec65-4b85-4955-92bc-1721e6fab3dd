<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.vswr.repository.ATSWRPostDao">
   
    <select id="getNewVswrID" resultType="String">
        SELECT VSWR_ID.nextVal FROM DUAL
    </select>

    <insert id="insertGeneral">
            INSERT INTO VSWR_GENERAL_INFO (
                VSWR_ID,
                TITLE,
                VBUILD_ID,
                EXISTING_SCSWR_ID,
                CURRENT_STATUS,
                CURRENT_REQUESTOR,
                PURPOSE,
                PLANT,
                FACILITY,
                SWR_TYPE_CATEGORY,
                SWR_TYPE,
                SWR_TYPE_FLAG,
                PRIORITY,
                SPEC_DEVICE,
                PURCHASE_ORDER,
                LINE_ITEM,
                IO,
                REQUEST_DATE,
                PKG_GROUP_EMAIL,
                COPY_EMAIL,
                VBUILD_MATERIAL,
                SCSWR_MATERIAL,
                SCSWR_PLANT,
                SCSWR_FACILITY,
                SCSWR_SPEC_DEVICE,
                PIN,
                PKG,
                PKG_GROUP,
                BUILD_TYPE,
                REQUESTOR_USER_ID,
                REQUESTOR_USER_NAME,
                REQUESTOR_EMAIL,
                REQUESTOR_PHONE,
                REQUESTOR_COST_CENTER,
                REQUESTOR_GROUP
            )
            VALUES (
                #{vswrID},
                #{generalInfo.title, jdbcType=VARCHAR},
                #{generalInfo.vbuildID, jdbcType=VARCHAR},
                #{generalInfo.existingScswrID, jdbcType=VARCHAR},
                #{generalInfo.currentStatus, jdbcType=VARCHAR},
                #{generalInfo.currentRequestor, jdbcType=VARCHAR},
                #{generalInfo.purpose, jdbcType=VARCHAR},
                #{generalInfo.plant, jdbcType=VARCHAR},
                #{generalInfo.facility, jdbcType=VARCHAR},
                'SWR_TYPE_CATEGORY',
                #{generalInfo.swrType, jdbcType=VARCHAR},
                '',
                #{generalInfo.priority, jdbcType=VARCHAR},
                #{generalInfo.specDevice, jdbcType=VARCHAR},
                #{generalInfo.purchaseOrder, jdbcType=VARCHAR},
                #{generalInfo.lineItem, jdbcType=VARCHAR},
                #{generalInfo.io, jdbcType=VARCHAR},
                SYSDATE,
                #{generalInfo.groupEmail, jdbcType=VARCHAR},
                #{generalInfo.copyEmail, jdbcType=VARCHAR},
                #{generalInfo.vbuildMaterial, jdbcType=VARCHAR},
                #{generalInfo.scswrMaterial, jdbcType=VARCHAR},
                #{generalInfo.scswrPlant, jdbcType=VARCHAR},
                #{generalInfo.scswrFacility, jdbcType=VARCHAR},
                #{generalInfo.scswrSpecDevice, jdbcType=VARCHAR},
                #{generalInfo.pin, jdbcType=VARCHAR},
                #{generalInfo.pkg, jdbcType=VARCHAR},
                #{generalInfo.pkgGroup, jdbcType=VARCHAR},
                #{generalInfo.buildType, jdbcType=VARCHAR},
                LOWER(#{requestorInfo.itssID, jdbcType=VARCHAR}),
                #{requestorInfo.name, jdbcType=VARCHAR},
                #{requestorInfo.email, jdbcType=VARCHAR},
                #{requestorInfo.phone, jdbcType=VARCHAR},
                #{requestorInfo.costCenter, jdbcType=VARCHAR},
                #{requestorInfo.group, jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertDevice">
            INSERT INTO VSWR_DEVICE (
                VSWR_ID,
                SAP_MATERIAL,
                SPEC_DEVICE,
                SAP_BASE_MATERIAL,
                SBE,
                SBE1,
                SBE2,
                INDUSTRY_SECTOR,
                PIN,
                PKG,
                PKG_GROUP,
                WW_ID,
                BUILD_QTY,
                APL,
                ISO,
                MCM,
                OFFLOAD_NAME,
                OLD_MATERIAL,
                PDB_FACILITY,
                NICHE
            )
            VALUES (
                #{vswrID},
                #{deviceInfo.sapMaterial, jdbcType=VARCHAR},
                #{deviceInfo.specDevice, jdbcType=VARCHAR},
                #{deviceInfo.sapBaseMaterial, jdbcType=VARCHAR},
                #{deviceInfo.sbe, jdbcType=VARCHAR},
                #{deviceInfo.sbe1, jdbcType=VARCHAR},
                #{deviceInfo.sbe2, jdbcType=VARCHAR},
                #{deviceInfo.industrySector, jdbcType=VARCHAR},
                #{deviceInfo.pin, jdbcType=VARCHAR},
                #{deviceInfo.pkg, jdbcType=VARCHAR},
                #{deviceInfo.pkgGroup, jdbcType=VARCHAR},
                #{deviceInfo.profitCenter, jdbcType=VARCHAR},
                #{deviceInfo.buildQuantity, jdbcType=VARCHAR},
                #{deviceInfo.apl, jdbcType=VARCHAR},
                #{deviceInfo.iso, jdbcType=VARCHAR},
                #{deviceInfo.mcm, jdbcType=VARCHAR},
                #{deviceInfo.offloadInfo, jdbcType=VARCHAR},
                #{deviceInfo.oldMaterial, jdbcType=VARCHAR},
                #{deviceInfo.pdbFacility, jdbcType=VARCHAR},
                #{deviceInfo.niche, jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertAssembly">
        INSERT INTO VSWR_ASSEMBLY (
            VSWR_ID,
            ASSEMBLY_REQUIRED,
            HEADER,
            BASE_OUTLINE,
            WIRE_DIAMETER,
            BOND_PAD_METALIZATION,
            MB_FILE_LOC,
            MB_FILE_OTHER_LOC,
            FORECAST_FLAG
        )
        VALUES (
            #{vswrID},
            #{assemblyInfo.assemblyReq, jdbcType=VARCHAR},
            #{assemblyInfo.header, jdbcType=VARCHAR},
            #{assemblyInfo.baseOutline, jdbcType=VARCHAR},
            #{assemblyInfo.wireDiameter, jdbcType=VARCHAR},
            #{assemblyInfo.bondPadMetalization, jdbcType=VARCHAR},
            #{assemblyInfo.mbPath, jdbcType=VARCHAR},
            #{assemblyInfo.mbOrArcPath, jdbcType=VARCHAR},
            #{assemblyInfo.forecastedFlag, jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertBom">
        INSERT ALL
            <foreach collection="bomInfo" item="material" index="index" >
                INTO VSWR_BOM (
                    VSWR_ID,
                    SEQUENCE,
                    COMPONENT,
                    TRAVELER_COMPONENT,
                    UNRESTRICTED,
                    STOCK_IN_TFR,
                    QUAL_INSPE, 
                    RESTRICTED,
                    BLOCKED,
                    RETURNS,
                    AVAILABLE,
                    FORECASTED_COMPONENT
                )
                VALUES (
                    #{vswrID},
                    #{material.sequence, jdbcType=VARCHAR},
                    #{material.component, jdbcType=VARCHAR},
                    #{material.travelerComponent, jdbcType=VARCHAR},
                    #{material.unrestricted, jdbcType=VARCHAR},
                    #{material.stock, jdbcType=VARCHAR},
                    #{material.qual, jdbcType=VARCHAR},
                    #{material.restricted, jdbcType=VARCHAR},
                    #{material.blocked, jdbcType=VARCHAR},
                    #{material.returns, jdbcType=VARCHAR},
                    #{material.isAvailable, jdbcType=VARCHAR},
                    #{material.forecastedComponent, jdbcType=VARCHAR}
                )
            </foreach>
        select * from dual
    </insert>

    <insert id="insertDies">
        INSERT ALL
            <foreach collection="dieInfo" item="die" index="index" >
                INTO VSWR_DIE (
                    VSWR_ID,
                    SEQUENCE,
                    PRIORITY,
                    PLANT,
                    MATL_MASTER_DIE_NAME,
                    SELECTED,
                    DIE_REV,
                    DIE_DESIGNATOR,
                    SCRIBE_WIDTH,
                    DIE_SIZE,
                    FAB_CODE,
                    FAB_TECHNOLOGY,
                    WAFER_DIAMETER,
                    WAFER_THICKNESS,
                    BACKGRIND_THICKNESS
                )
                VALUES (
                    #{vswrID},
                    #{die.sequence, jdbcType=VARCHAR},
                    #{die.priority, jdbcType=VARCHAR},
                    #{die.plant, jdbcType=VARCHAR},
                    #{die.matlMasterDieName, jdbcType=VARCHAR},
                    ${die.selected ? "'Y'" : "'N'"},
                    #{die.dieRev, jdbcType=VARCHAR},
                    #{die.dieDesignator, jdbcType=VARCHAR},
                    #{die.scribeWidth, jdbcType=VARCHAR},
                    #{die.dieSize, jdbcType=VARCHAR},
                    #{die.fabCode, jdbcType=VARCHAR},
                    #{die.fabTechnology, jdbcType=VARCHAR},
                    #{die.waferDiameter, jdbcType=VARCHAR},
                    #{die.waferThickness, jdbcType=VARCHAR},
                    #{die.backgrindThickness, jdbcType=VARCHAR}
                )
            </foreach>
        select * from dual
    </insert>

    <insert id="insertDieLots">
        INSERT ALL
            <foreach collection="dieLotInfo" item="dieLot" index="index" >
                INTO VSWR_DIE_LOT (
                    VSWR_ID,
                    MATL_MASTER_DIE_NAME,
                    DIE_LOT,
                    WAFER_NUMBER_TO_USE,
                    PROBED,
                    INKLESS,
                    INKLESS_BUILD_BY,
                    MAP_REQUIRED,
                    MAP_LOCATION,
                    BIN_NUM,
                    USE_GEC,
                    PLANT,
                    INCOMING_MATL_SHIP_STATUS,
                    INCOMING_MATL_SHIP_DATE,
                    INCOMING_MATL_DELIVERY_NOTE,
                    INCOMING_MATL_INVOICE,
                    INCOMING_MATL_SHIP_QTY,
                    LOCATION,
                    INCOMING_MATL_WAYBILL
                )
                VALUES (
                    #{vswrID},
                    #{dieLot.matlMasterDieName, jdbcType=VARCHAR},
                    #{dieLot.dieLot, jdbcType=VARCHAR},
                    #{dieLot.waferNumToUse, jdbcType=VARCHAR},
                    #{dieLot.probed, jdbcType=VARCHAR},
                    #{dieLot.isInkless, jdbcType=VARCHAR},
                    #{dieLot.buildBy, jdbcType=VARCHAR},
                    '',
                    #{dieLot.stdMapLocation, jdbcType=VARCHAR},
                    #{dieLot.pickupBin, jdbcType=VARCHAR},
                    #{dieLot.useGecs, jdbcType=VARCHAR},
                    #{dieLot.plant, jdbcType=VARCHAR},
                    #{dieLot.matShipStatus, jdbcType=VARCHAR},
                    TO_DATE(#{dieLot.dateShipped, jdbcType=DATE}, 'yyyy-mm-dd HH24:MI:SS'),
                    #{dieLot.deliveryNote, jdbcType=VARCHAR},
                    #{dieLot.invoice, jdbcType=VARCHAR},
                    #{dieLot.qtyToShip, jdbcType=VARCHAR},
                    #{dieLot.location, jdbcType=VARCHAR},
                    #{dieLot.sapWaybill, jdbcType=VARCHAR}
                )
            </foreach>
        select * from dual
    </insert>

    <insert id="insertComments">
        INSERT ALL
            <foreach collection="comments" item="comment" index="index" >
                INTO VSWR_COMMENTS (
                    VSWR_ID,
                    OPERATION,
                    USER_ID,
                    USER_NAME,
                    DTTM,
                    "COMMENT"
                )
                VALUES (
                    #{vswrID},
                    #{comment.operation, jdbcType=VARCHAR},
                    #{comment.userID, jdbcType=VARCHAR},
                    #{comment.userName, jdbcType=VARCHAR},
                    TO_DATE(#{comment.dttm, jdbcType=DATE}, 'mm/dd/yyyy hh12:mi:ss am'),
                    #{comment.comment, jdbcType=VARCHAR}
                )
            </foreach>
        select * from dual
    </insert>

    <insert id="insertPackingMaterial">
        INSERT ALL
            <foreach collection="packingMaterials" item="material" index="index" >
                INTO VSWR_PACKING_MATERIAL (
                    VSWR_ID,
                    SEQUENCE,
                    COMPONENT,
                    TRAVELER_COMPONENT,
                    UNRESTRICTED,
                    STOCK_IN_TFR,
                    QUAL_INSPE, 
                    RESTRICTED,
                    BLOCKED,
                    RETURNS,
                    AVAILABLE
                )
                VALUES (
                    #{vswrID},
                    #{material.sequence, jdbcType=VARCHAR},
                    #{material.component, jdbcType=VARCHAR},
                    #{material.travelerComponent, jdbcType=VARCHAR},
                    #{material.unrestricted, jdbcType=VARCHAR},
                    #{material.stock, jdbcType=VARCHAR},
                    #{material.qual, jdbcType=VARCHAR},
                    #{material.restricted, jdbcType=VARCHAR},
                    #{material.blocked, jdbcType=VARCHAR},
                    #{material.returns, jdbcType=VARCHAR},
                    #{material.isAvailable, jdbcType=VARCHAR}
                )
            </foreach>
        select * from dual
    </insert>

    <insert id="insertPackingRequirements">
        INSERT INTO VSWR_PACKING_REQUIREMENTS (
            VSWR_ID,
            STICKERTYPE,
            WAIVER_NUMBER,
            DISPOSITION_PARTIAL_FG,
            WAFER_SKELETON_DISPOSITION,
            WAFER_SKELETON_RETURN_TO,
            RMR_RETEST,
            PDC_UNRESTRICTED_SALE
        )
        VALUES (
            #{vswrID},
            #{packingReq.stickerType, jdbcType=VARCHAR},
            #{packingReq.eWaiver, jdbcType=VARCHAR},
            #{packingReq.finishedGoodsDispo, jdbcType=VARCHAR},
            #{packingReq.waferSkeleton, jdbcType=VARCHAR},
            #{packingReq.plantCode, jdbcType=VARCHAR},
            #{packingReq.isRetestRMR, jdbcType=VARCHAR},
            #{packingReq.pdcUnrestrictedSale, jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertShippingInfo">
        INSERT ALL
            <foreach collection="shippingInfo" item="info" index="index" >
                INTO VSWR_SHIP (
                    VSWR_ID,
                    ATTENTION,
                    MAIL_STATION,
                    PLANT,
                    ADDRESS,
                    QUANTITY,
                    STATE_OF_FINISH,
                    SHIP_DEVICE_NAME
                )
                VALUES (
                    #{vswrID},
                    #{info.attention, jdbcType=VARCHAR},
                    #{info.mailStation, jdbcType=VARCHAR},
                    #{info.plant, jdbcType=VARCHAR},
                    #{info.address, jdbcType=VARCHAR},
                    #{info.quantity, jdbcType=VARCHAR},
                    #{info.stateOfFinish, jdbcType=VARCHAR},
                    #{info.shipDeviceName, jdbcType=VARCHAR}
                )
            </foreach>
        select * from dual
    </insert>

  <update id="updateGeneral">
        UPDATE VSWR_GENERAL_INFO 
        SET
            TITLE                 = #{generalInfo.title, jdbcType=VARCHAR},
            VBUILD_ID             = #{generalInfo.vbuildID, jdbcType=VARCHAR},
            EXISTING_SCSWR_ID     = #{generalInfo.existingScswrID, jdbcType=VARCHAR},
            CURRENT_STATUS        = #{generalInfo.currentStatus, jdbcType=VARCHAR},
            CURRENT_REQUESTOR     = #{generalInfo.currentRequestor, jdbcType=VARCHAR},
            PURPOSE               = #{generalInfo.purpose, jdbcType=VARCHAR},
            PLANT                 = #{generalInfo.plant, jdbcType=VARCHAR},
            FACILITY              = #{generalInfo.facility, jdbcType=VARCHAR},
            SWR_TYPE_CATEGORY     = 'SWR_TYPE_CATEGORY',
            SWR_TYPE              = #{generalInfo.swrType, jdbcType=VARCHAR},
            SWR_TYPE_FLAG         = 'tst',
            PRIORITY              = #{generalInfo.priority, jdbcType=VARCHAR},
            SPEC_DEVICE           = #{generalInfo.specDevice, jdbcType=VARCHAR},
            PURCHASE_ORDER        = #{generalInfo.purchaseOrder, jdbcType=VARCHAR},
            LINE_ITEM             = #{generalInfo.lineItem, jdbcType=VARCHAR},
            IO                    = #{generalInfo.io, jdbcType=VARCHAR},
            REQUEST_DATE          = SYSDATE,
            PKG_GROUP_EMAIL       = #{generalInfo.groupEmail, jdbcType=VARCHAR},
            COPY_EMAIL            = #{generalInfo.copyEmail, jdbcType=VARCHAR},
            VBUILD_MATERIAL       = #{generalInfo.vbuildMaterial, jdbcType=VARCHAR},
            SCSWR_MATERIAL        = #{generalInfo.scswrMaterial, jdbcType=VARCHAR},
            SCSWR_PLANT           = #{generalInfo.scswrPlant, jdbcType=VARCHAR},
            SCSWR_FACILITY        = #{generalInfo.scswrFacility, jdbcType=VARCHAR},
            SCSWR_SPEC_DEVICE     = #{generalInfo.scswrSpecDevice, jdbcType=VARCHAR},
            PIN                   = #{generalInfo.pin, jdbcType=VARCHAR},
            PKG                   = #{generalInfo.pkg, jdbcType=VARCHAR},
            PKG_GROUP             = #{generalInfo.pkgGroup, jdbcType=VARCHAR},
            REQUESTOR_USER_ID     = LOWER(#{requestorInfo.itssID, jdbcType=VARCHAR}),
            REQUESTOR_USER_NAME   = #{requestorInfo.name, jdbcType=VARCHAR},
            REQUESTOR_EMAIL       = #{requestorInfo.email, jdbcType=VARCHAR},
            REQUESTOR_PHONE       = #{requestorInfo.phone, jdbcType=VARCHAR},
            REQUESTOR_COST_CENTER = #{requestorInfo.costCenter, jdbcType=VARCHAR},
            REQUESTOR_GROUP       = #{requestorInfo.group, jdbcType=VARCHAR}
        WHERE VSWR_ID = #{generalInfo.vswrID}
    </update>

    <update id="updateDieSelection">
        UPDATE VSWR_DIE
        SET SELECTED = #{selected}
        WHERE VSWR_ID = #{vswrID}
           AND MATL_MASTER_DIE_NAME = #{matlMasterdieName}
    </update>

    <update id="resetAllDieSelection">
        UPDATE VSWR_DIE
        SET SELECTED = 'N'
        WHERE VSWR_ID = #{vswrID}
    </update>

    <delete id="deleteDies">
        DELETE FROM VSWR_DIE WHERE VSWR_ID = #{vswrID}
    </delete>

    <delete id="deleteDieLots">
        DELETE FROM VSWR_DIE_LOT WHERE VSWR_ID = #{vswrID}
    </delete>

    <delete id="deleteComments">
        DELETE FROM VSWR_COMMENTS WHERE VSWR_ID = #{vswrID}
    </delete>
</mapper>
