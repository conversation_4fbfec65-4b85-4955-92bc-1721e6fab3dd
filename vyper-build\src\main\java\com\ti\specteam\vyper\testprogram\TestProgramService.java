package com.ti.specteam.vyper.testprogram;

import com.ti.specteam.vyper.Required;
import com.ti.specteam.vyper.atss.operations.OperationRepository;
import com.ti.specteam.vyper.atss.traveler.Component;
import com.ti.specteam.vyper.atss.traveler.SubFlow;
import com.ti.specteam.vyper.atss.traveler.*;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.oss.OSSService;
import com.ti.specteam.vyper.pack.PackService;
import com.ti.specteam.vyper.security.user.User;
import com.ti.specteam.vyper.vscn.model.Vscn;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.lang.System;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ti.specteam.vyper.build.model.SubFlow.TEST;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TestProgramService implements CommandLineRunner {

    private final OperationRepository operationRepository;
    private final OSSService ossService;
    private final PackService packService;

    private final List<String> allOperationNames = new ArrayList<>();
    private final List<String> allComponentNames = new ArrayList<>();
    private final List<String> allAttributeNames = new ArrayList<>();

    @Override
    public void run(String... args) throws Exception {
        allOperationNames.addAll(operationRepository.allOperations());
        allComponentNames.addAll(operationRepository.allComponents());
        allAttributeNames.addAll(operationRepository.allAttributes());
    }

    public void parse(Build build, com.ti.specteam.vyper.security.user.User user) {
        log.debug("parse(build:{})", build.getBuildNumber());

        clear(build);

        if(StringUtils.isBlank(build.getTest().getContent())) {
            return;
        }

        build.getTest().getSource().appointUser(user);
        loadTestProgram(build);
        validate(build);
        setSourceIcons(build, user);
        updateFlow(build);
        setSelections(build);
    }

    public void clear(Build build) {
        clearTestFlowOperations(build);
        clearSelections(build);
        clearTravelerOperations(build);
        build.getTest().setSource(new Source());
    }

    @SuppressWarnings("CodeBlock2Expr")
    public void clearTestFlowOperations(Build build) {
        // exit if we don't have any operations
        if (build.getTest().getTravelerOperations().isEmpty()) {
            return;
        }

        // find the index of the 1st test operation
        String name = build.getTest().getTravelerOperations().get(0).getName();
        FlowOperation fo = build.getFlow().getObject().getOperations().stream()
                .filter(flowOperation -> StringUtils.equalsIgnoreCase(flowOperation.getName(), name))
                .findFirst()
                .orElse(null);
        if (fo == null) {
            return;
        }
        int pos = build.getFlow().getObject().getOperations().indexOf(fo);
        if(-1 == pos) {
            return;
        }

        // clear the test operations
        build.getTest().getTravelerOperations().forEach(travelerOperation -> {
            build.getFlow().getObject().getOperations().removeIf(flowOperation -> {
                return StringUtils.equalsIgnoreCase(flowOperation.getName(), travelerOperation.getName());
            });
        });

        // insert the "TEST" operation
        fo = new FlowOperation();
        fo.setName("TEST");
        fo.setRequired(Required.OPTIONAL);
        fo.getSource().appointSystem(SystemName.BOM_TEMPLATE);

        build.getFlow().getObject().getOperations().add(pos, fo);
    }

    // clear the selections for the test operations
    @SuppressWarnings("CodeBlock2Expr")
    public void clearSelections(Build build) {
        build.getTest().getTravelerOperations().forEach(travelerOperation -> {
            travelerOperation.getComponents().forEach(travelerComponent -> {
                build.clearSelections(travelerComponent.getName());
            });
        });
    }

    public void clearTravelerOperations(Build build) {
        build.getTest().getTravelerOperations().clear();
    }

    public void loadTestProgram(Build build) {
        if (null == build.getTest().getContent()) {
            return;
        }
        build.getTest().getTravelerOperations().addAll(parse(build.getTest().getContent()));
    }

    public void setSourceIcons(Build build, User user) {
        build.getTest().getTravelerOperations().forEach(travelerOperation -> {
            travelerOperation.getSource().appointUser(user);
            travelerOperation.getComponents().forEach(travelerComponent -> {
                travelerComponent.getSourceName().appointUser(user);
                travelerComponent.getSourceValue().appointUser(user);
            });
        });
    }
    public void setSourceIcons(Build build, SystemName sysName) {
        build.getTest().getTravelerOperations().forEach(travelerOperation -> {
            travelerOperation.getSource().appointSystem(sysName);
            travelerOperation.getComponents().forEach(travelerComponent -> {
                travelerComponent.getSourceName().appointSystem(sysName);
                travelerComponent.getSourceValue().appointSystem(sysName);
            });
        });
    }

    public void parse(Vscn vscn, com.ti.specteam.vyper.security.user.User user) {
        log.debug("parse(vscn:{})", vscn.getVscnNumber());

        clear(vscn);

        if(StringUtils.isBlank(vscn.getTest().getContent())) {
            return;
        }

        vscn.getTest().getSource().appointUser(user);
        loadTestProgram(vscn);
        validate(vscn);
        setSourceIcons(vscn, user);
    }

    public void loadTestProgram(Vscn vscn) {
        if (null == vscn.getTest().getContent()) {
            return;
        }
        vscn.getTest().getTravelerOperations().addAll(parse(vscn.getTest().getContent()));
    }

    public void validate(Vscn vscn) {
        validate(vscn.getTest().getTravelerOperations());
    }

    public void clear(Vscn vscn) {
        clearTravelerOperations(vscn);
        vscn.getTest().setSource(new Source());
    }

    public void clearTravelerOperations(Vscn vscn) {
        vscn.getTest().getTravelerOperations().clear();
    }

    public void setSourceIcons(Vscn vscn, User user) {
        vscn.getTest().getTravelerOperations().forEach(travelerOperation -> {
            travelerOperation.getSource().appointUser(user);
            travelerOperation.getComponents().forEach(travelerComponent -> {
                travelerComponent.getSourceName().appointUser(user);
                travelerComponent.getSourceValue().appointUser(user);
            });
        });
    }

    /**
     * Loop through the lines of text, and create the operations and components.
     *
     * @param content {@link String} The test program content
     * @return List<TravelerOperation> The list of parsed traveler operations
     */

    public List<TravelerOperation> parse(String content) {
        log.debug("parse(content:{} chars)", content.length());
        List<TravelerOperation> operations = new ArrayList<>();

        content.lines().forEach(line2 -> {
            String line = line2.trim();
            if (line.isEmpty()) {
                return;
            }

            String newLine = line.replace((char) 160, ' ').replace((char) 9, ' ');

            boolean hasPriority = hasPriority(newLine);
            boolean hasColon = line.contains(":");

            if (!hasPriority && !hasColon) {
                addOperation(operations, newLine);
            }

            if (hasPriority && hasColon) {
                addComponent(operations, newLine);
            }

            if (!hasPriority && hasColon) {
                addAttribute(operations, newLine);
            }

        });

        return operations;
    }

    /**
     * Return true if the line ends with a priority value
     *
     * @param line {@link String} The line of the program
     * @return Boolean true if the line ends in a priority value, else false
     */
    public boolean hasPriority(String line) {

        if (line == null || line.isEmpty()) {
            return false;
        }


        // to match the priority, then end of the line must be 5 whitespace (space or tab) and a 2 digit number
        Pattern pattern1 = Pattern.compile("\\s{5}(\\d{2})$");
        Matcher matcher1 = pattern1.matcher(line);
        return matcher1.find();
    }

    /**
     * Add an operation
     *
     * @param operations List<{@link TravelerOperation}> The traveler operations
     * @param oName      {@link String} the operation name
     */
    public void addOperation(List<TravelerOperation> operations, String oName) {
        if (oName == null || oName.isEmpty()) {
            throw new TestProgramException("The operation name is invalid");
        }

        // remove the test log point
        // Final Test 1 [XXXX] => Final Test 1
        int pos = oName.indexOf("[");
        if (-1 != pos) {
            oName = oName.substring(0, pos - 1);
        }

        // create the operation
        TravelerOperation operation = new TravelerOperation();
        operation.setSubflowType("TEST");
        operation.setRequired(Required.OPTIONAL);
        operation.setName(oName.trim());
        operations.add(operation);
    }

    public void addComponent(List<TravelerOperation> operations, String line) {
        if (line == null || line.isEmpty()) {
            throw new TestProgramException("The component line is invalid");
        }

        String[] values = line.split(":");
        if (values.length < 2) {
            throw new TestProgramException("The component line is missing the colon");
        }

        if (operations.isEmpty()) {
            throw new TestProgramException("The operation is missing.");
        }

        // split the line into name, value, priority
        String line2 = line.trim();
        int pos1 = line2.indexOf(":");
        int pos2 = line2.lastIndexOf(" ");
        String name = line2.substring(0, pos1).trim();
        String value = line2.substring(pos1 + 1, pos2).trim();

        if(StringUtils.startsWithIgnoreCase(name, ATLimitandValue.ATLIMITSTRING) || StringUtils.startsWithIgnoreCase(name, ATLimitandValue.LIMITSTRING)){
            return;
        }

        // create the component
        TravelerComponent component = new TravelerComponent();
        component.setName(name);
        component.setValue(value);
        component.setRequired(Required.OPTIONAL);

        // add component to the last operation
        operations.get(operations.size() - 1).getComponents().add(component);
    }

    public void addAttribute(List<TravelerOperation> operations, String line) {
        if (line == null || line.isEmpty()) {
            throw new TestProgramException("The attribute line is invalid");
        }

        String[] values = line.split(":");
        if (values.length < 2) {
            throw new TestProgramException("The attribute line is missing the colon");
        }

        if (operations.isEmpty()) {
            throw new TestProgramException("Found attribute, but there is no operation.");
        }

        TravelerOperation operation = operations.get(operations.size() - 1);
        if (operation.getComponents().isEmpty()) {
            throw new TestProgramException("Found attribute, but there is no component.");
        }

        // split the name and value
        String line2 = line.trim();
        int pos1 = line2.indexOf(":");
        String name = line2.substring(0, pos1).trim();
        String value = line2.substring(pos1 + 1).trim();

        // create the attribute
        TravelerAttribute attribute = new TravelerAttribute();
        attribute.setName(name);
        attribute.setValue(value);

        // get the last operation, or create one
        TravelerComponent component = operation.getComponents().get(operation.getComponents().size() - 1);
        component.getAttributes().add(attribute);
    }

    public void validate(Build build) {
        validate(build.getTest().getTravelerOperations());
    }

    public void validate(List<TravelerOperation> operations) {
        validateOperations(operations);
        validateComponents(operations);
        validateAttributes(operations);
    }

    public void validateOperations(List<TravelerOperation> operations) {
        operations.forEach(travelerOperation -> {
            if (!allOperationNames.contains(travelerOperation.getName())) {
                throw new TestProgramException("The operation name is not a valid ATSS name: " + travelerOperation.getName());
            }
        });
    }

    public void validateComponents(List<TravelerOperation> operations) {
        operations.stream().flatMap(travelerOperation -> travelerOperation.getComponents().stream()).forEach(travelerComponent -> {
            if (!allComponentNames.contains(travelerComponent.getName())) {
                throw new TestProgramException("The component name is not a valid ATSS name: " + travelerComponent.getName());
            }
        });
    }

    public void validateAttributes(List<TravelerOperation> operations) {
        operations.stream().flatMap(travelerOperation -> travelerOperation.getComponents().stream()).flatMap(travelerComponent -> travelerComponent.getAttributes().stream()).forEach(travelerAttribute -> {
            if (!allAttributeNames.contains(travelerAttribute.getName())) {
                throw new TestProgramException("The attribute name is not a valid ATSS name: " + travelerAttribute.getName());
            }
        });
    }

    public void updateFlow(Build build) {

        List<FlowOperation> flowOperations = createFlow(build);

        // removing test subflow operations before upating it from the above flowOperations
        build.getFlow().getObject().getOperations().removeIf(flowOperation -> flowOperation.getSubFlow() != null && StringUtils.equalsIgnoreCase(flowOperation.getSubFlow().toString(),"TEST"));

        // is there a test operation?
        FlowOperation fo = build.getFlow().getObject().getOperations().stream()
                .filter(flowOperation -> StringUtils.equalsIgnoreCase("TEST", flowOperation.getName()))
                .findFirst()
                .orElse(null);

        if (null == fo) {
            updateFlowBeforePack(build, flowOperations);
        } else {
            updateFlowReplaceTest(build, fo, flowOperations);
        }

     //   addAtLimits(build);
    }

    // build the flow operations and components
    public List<FlowOperation> createFlow(Build build) {

        List<FlowOperation> flowOperations = new ArrayList<>();

        build.getTest().getTravelerOperations().forEach(travelerOperation -> {
            FlowOperation fo = new FlowOperation();
            fo.setSubFlow(TEST);
            fo.setName(travelerOperation.getName());
            fo.setRequired(travelerOperation.getRequired());
            fo.getSource().set(travelerOperation.getSource());

            travelerOperation.getComponents().forEach(travelerComponent -> {
                FlowComponent fc = new FlowComponent();
                fc.setName(travelerComponent.getName());
                fc.setRequired(travelerComponent.getRequired());
                fc.getSource().set(travelerComponent.getSourceValue());
                fo.getComponents().add(fc);
            });

            flowOperations.add(fo);
        });

        return flowOperations;
    }

    public void updateFlowReplaceTest(Build build, FlowOperation testFlowOperation, List<FlowOperation> flowOperations) {

        // get the index of the test flow operation
        int pos = build.getFlow().getObject().getOperations().indexOf(testFlowOperation);
        if (-1 == pos) {
            updateFlowBeforePack(build, flowOperations);
            return;
        }

        build.getFlow().getObject().getOperations().remove(pos);
        build.getFlow().getObject().getOperations().addAll(pos, flowOperations);
    }

    public void updateFlowBeforePack(Build build, List<FlowOperation> flowOperations) {
        FlowOperation fo = build.getFlow().getObject().getOperations().stream()
                .filter(flowOperation -> packService.isPack(flowOperation.getName()))
                .findFirst()
                .orElse(null);

        int pos = (null == fo) ? -1 : build.getFlow().getObject().getOperations().indexOf(fo);

        // if none were found, add the test operations to the end, otherwise add them at the position
        if (pos == -1) {
            build.getFlow().getObject().getOperations().addAll(flowOperations);
        } else {
            build.getFlow().getObject().getOperations().addAll(pos, flowOperations);
        }

    }

    // for each component, add it to the selections
    @SuppressWarnings("CodeBlock2Expr")
    public void setSelections(Build build) {
        build.getTest().getTravelerOperations().forEach(travelerOperation -> {
            travelerOperation.getComponents().forEach(travelerComponent -> {
                Selection selection = build.findOrCreateSelection(travelerOperation.getName(), travelerComponent.getName(), travelerComponent.getSourceValue());
                selection.addItem(travelerComponent.getValue(), Engineering.N, travelerComponent.getSourceValue());
            });
        });
    }

    public void addAtLimits(Build build){
        ossService.loadAtLimitsToTest(build);
    }

    public void addContentstoBuild(Build build){
        StringBuilder contentString = new StringBuilder();
        String operationFormat = "%s\n";
        String componentFormat = "   %1$-23s: %2$-52s%3$02d\n";
        String attributeFormat = "      %1$-20s: %2$s\n";
        for(TravelerOperation to: build.getTest().getTravelerOperations()){
            contentString.append(String.format(operationFormat, to.getName()));
            for(TravelerComponent tc: to.getComponents()){
                contentString.append(String.format(componentFormat, tc.getName(), tc.getValue(), 1));
                for(TravelerAttribute ta: tc.getAttributes()){
                    contentString.append(String.format(attributeFormat, ta.getName(), ta.getValue()));

                }
            }
        }
        if (contentString.length() != 0) {
            build.getTest().setContent(contentString.toString());
        } else {
            build.getTest().setContent(null);
        }

    }

    public List<TravelerOperation> getTravelerOperationsFromSubflow(SubFlow subflow, SystemName sysName){
        if(subflow.getOperations().isEmpty()){
            return Collections.emptyList();
        }
        List<TravelerOperation> travelerOperations = new ArrayList<>();
        for(Operation operation: subflow.getOperations()){
            TravelerOperation travelerOperation = new TravelerOperation();
            travelerOperation.setSubflowType(subflow.getType());
            travelerOperation.setName(operation.getName().replaceAll("\\[.*?\\]","").trim());
            travelerOperation.setRequired(Required.OPTIONAL);
            travelerOperation.getSource().appointSystem(sysName);

            for(Component component: operation.getComponents()){
                if(!component.getName().contains("A/T Limit")){
                    TravelerComponent tc = new TravelerComponent();
                    tc.setName(component.getName());
                    tc.setValue(component.getValue());
                    tc.setRequired(Required.OPTIONAL);
                    tc.getSourceName().appointSystem(sysName);
                    tc.getSourceValue().appointSystem(sysName);

                    for(Attribute attribute: component.getAttributes()){
                        TravelerAttribute ta = new TravelerAttribute(attribute.getName(), attribute.getValue());
                        tc.getAttributes().add(ta);
                    }
                    travelerOperation.getComponents().add(tc);
                }
            }
            travelerOperations.add(travelerOperation);

        }
        return travelerOperations;


    }

}
