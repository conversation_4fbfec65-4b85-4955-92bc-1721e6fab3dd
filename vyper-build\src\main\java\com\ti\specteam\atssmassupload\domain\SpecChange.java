package com.ti.specteam.atssmassupload.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "MU_PROJ_SPEC_CHANGE")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class SpecChange {

	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private String id;

	@Column(name = "FLOW_TYPE", length = 50, nullable = false)
	private String flowType;

	@Column(name = "OPERATION_NAME", length = 20, nullable = true)
	private String operationName;

	@Column(name = "COMPONENT_NAME", length = 50, nullable = false)
	private String componentName;

	@Column(name = "COMPONENT_VALUE", length = 50, nullable = true)
	private String componentValue;

	@Column(name = "ATTRIBUTE_NAME", length = 50, nullable = true)
	private String attributeName;

	@Column(name = "ATTRIBUTE_VALUE", length = 50, nullable = true)
	private String attributeValue;

	@Column(name = "STATUS", length = 250, nullable = true)
	private String status;

	@Column(name = "UI_SEQUENCE", length = 10, nullable = true)
	private Integer uiSequence;

	@Column(name = "PROJ_DEVICE_ID", nullable = false, insertable = false, updatable = false)
	private String projectDeviceID;

	@Column(name = "CREATED_BY", length = 20, nullable = false)
	private String createdBy;

	@Column(name = "UPDATED_BY", length = 20, nullable = false)
	private String updatedBy;

	@Column(name = "UPDATED_DTTM", length = 30, nullable = false)
	private Date updatedDttm;

	@Column(name = "CREATED_DTTM", length = 30, nullable = false)
	private Date createdDttm;

	@Column(name = "COMPONENT_OCCURRENCE", length = 10)
	private Integer componentOccurrence;
    
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "PROJ_DEVICE_ID")
	@JsonIgnore
	private ProjectDevice projectDevice;

	public SpecChange(String flowType, String operationName, String componentName, String componentValue,
			String attributeName, String attributeValue, String createdBy, String updatedBy, Date updatedDttm,
			Date createdDttm, Integer componentOccurrance, Integer uiSequence) {
		super();
		this.flowType = flowType;
		this.operationName = operationName;
		this.componentName = componentName;
		this.componentValue = componentValue;
		this.attributeName = attributeName;
		this.attributeValue = attributeValue;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.updatedDttm = updatedDttm;
		this.createdDttm = createdDttm;
		this.componentOccurrence = componentOccurrance;
		this.uiSequence = uiSequence;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) return true;
		if (!(obj instanceof SpecChange )) return false;
		return id != null && id.equals(((SpecChange) obj).getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
