package com.ti.specteam.vswr.vswr.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.Comment;
import com.ti.specteam.vswr.vswr.domain.DieLotStatus;
import com.ti.specteam.vswr.vswr.domain.ForecastedInfo;
import com.ti.specteam.vswr.vswr.domain.IntransitStatus;
import com.ti.specteam.vswr.vswr.domain.RequestorInfo;
import com.ti.specteam.vswr.vswr.repository.ATSWRDao;
import com.ti.specteam.vswr.vswr.repository.BDWDao;
import com.ti.specteam.vswr.vswr.service.FetchATSWRService;
import com.ti.specteam.vswr.vswr.service.FetchVyperService;

import org.springframework.beans.factory.annotation.Autowired;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class FetchATSWRServiceImpl implements FetchATSWRService{
    
    @Autowired
    ATSWRDao atswrDao;
    
    @Autowired
    BDWDao bdwDao;

    @Autowired
    FetchVyperService fetchVyperService;
    


    public Map<String,String> helloWorld(){
        Map<String,String> test = new HashMap<String,String>();
        test.put("Hello", "World");
        return test;
    }

    public RequestorInfo fetchRequestorInfo(String aid){
        log.info("fetchRequestorInfo called from FetchATSWRServiceImpl");
        return bdwDao.fetchRequestorInfo(aid.toUpperCase());
    }

    public List<HashMap<String, String>> fetchSwrTypeOptions(String vBuildType){
        log.info("fetchSwrTypeOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchSwrTypeOptions(vBuildType);
    }

    public List<HashMap<String, String>> fetchPriorityOptions(){
        log.info("fetchPriorityOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchPriorityOptions();
    }

    public List<HashMap<String, String>> fetchOffloadInfoOptions(){
        log.info("fetchOffloadInfoOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchOffloadInfoOptions();
    }
    
    public List<HashMap<String, String>> fetchFinishedGoodOptions(){
        log.info("fetchFinishedGoodOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchFinishedGoodOptions();
    }

    public List<HashMap<String, String>> fetchWaferSkeletonOptions(){
        log.info("fetchWaferSkeletonOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchWaferSkeletonOptions();
    }
    
    public HashMap<String, HashMap<String, String>> fetchShippToPlantOptions(){
        log.info("fetchShippToPlantOptions called from FetchATSWRServiceImpl");
        HashMap<String, HashMap<String, String>> shipToPlantOptions = new HashMap<String, HashMap<String, String>>();
        List<HashMap<String, String>> shipToPlantlist = atswrDao.fetchShippToPlantOptions();

        for(HashMap<String, String> option : shipToPlantlist){
            shipToPlantOptions.put(option.get("plant"), option);
        }

        return shipToPlantOptions;
    }

    public List<HashMap<String, String>> fetchDieLocationOptions(){
        log.info("fetchDieLocationOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchDieLocationOptions();
    }

    public List<HashMap<String, String>> fetchStateOfFinishOptions(){
        log.info("fetchStateOfFinishOptions called from FetchATSWRServiceImpl");
        return atswrDao.fetchStateOfFinishOptions();
    }

    public ForecastedInfo fetchForecastedInfo(String forecastID){
        log.info("fetchForecastedInfo called from FetchATSWRServiceImpl");
        return atswrDao.fetchForecastedInfo(forecastID);
    }

    private HashMap<String, List<Comment>> commentListToMap(List<Comment> comments){
        HashMap<String, List<Comment>> commentsMap = new HashMap<String, List<Comment>>();
        for(Comment comment : comments){
            String key = comment.getOperation();
            
            if(!commentsMap.containsKey(key)){
                commentsMap.put(key, new ArrayList<Comment>());
            }

            commentsMap.get(key).add(comment);
        }
        return commentsMap;
    }

    public ATSWRForm fetchVswr(String vswrID){
        ATSWRForm atswrForm = new ATSWRForm();
        atswrForm.setGeneralInfo(atswrDao.fetchGeneralInfo(vswrID));
        String vbuildID = atswrForm.getGeneralInfo().getVbuildID();

        atswrForm.setRequestorInfo(atswrDao.fetchRequestorInfo(vswrID));
        atswrForm.setDeviceInfo(atswrDao.fetchDeviceInfo(vswrID));
        atswrForm.setAssemblyInfo(atswrDao.fetchAssemblyInfo(vswrID));
        atswrForm.setBomInfo(atswrDao.fetchBomInfo(vswrID));
        atswrForm.setPackingRequirements(atswrDao.fetchPackingRequirements(vswrID));
        atswrForm.setPackingMaterial(atswrDao.fetchPackingMaterials(vswrID));
        atswrForm.setDieInfo(atswrDao.fetchDieInfo(vswrID));
        
        HashMap<String, List<Comment>> commentsMap = commentListToMap(atswrDao.fetchComments(vswrID));
        atswrForm.setComments(commentsMap);

        atswrForm.setShippingInfo(atswrDao.fetchShippingInfo(vswrID));

        List<String> attributesToFetch = new ArrayList<String>();
        attributesToFetch.add("traveler");
        Object traveler = fetchVyperService.fetchData(vbuildID, attributesToFetch).get("traveler");        
        atswrForm.setTraveler(traveler);
        
        return atswrForm;
    }

    public List<DieLotStatus> fetchDieLotStatus(String material, String plant, boolean inPlant){
        return atswrDao.fetchDieLotStatus(material, plant, inPlant);
    }

    public List<IntransitStatus> fetchDieLotIntransitStatus(String material, String plant){
        return atswrDao.fetchDieLotIntransitStatus(material, plant);
    }
}