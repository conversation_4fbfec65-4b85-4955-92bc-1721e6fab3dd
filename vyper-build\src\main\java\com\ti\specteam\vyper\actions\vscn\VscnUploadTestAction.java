package com.ti.specteam.vyper.actions.vscn;


import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.testprogram.TestProgramService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.verifier.vscn.VscnTestProgramVerifier;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.VSCN_UPLOAD_PROGRAM;

@Service
@Slf4j
@RequiredArgsConstructor
public class VscnUploadTestAction {
    private final VyperService vyperService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final UserUtilsService userUtilsService;
    private final SecurityService securityService;
    private final TestProgramService testProgramService;
    private final VscnTestProgramVerifier vscnTestProgramVerifier;

    public Vscn execute(VscnUploadTestForm vscnUploadTestForm) {
        log.debug("execute(vscnUploadTestForm:{})", vscnUploadTestForm);

        Vscn vscn = vscnService.fetchVscn(vscnUploadTestForm.getVscnNumber());
        Vyper vyper = vyperService.fetchVyper(vscn.getVyperNumber());
        String facility = vscn.getFacility().getPdbFacility();

        userUtilsService.validateUserByFacility(facility);
        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        changeTestInformation(vscn, vscnUploadTestForm);
        vscnTestProgramVerifier.verify(vyper,vscn);

        auditService.createVscn(
                vyper.getVyperNumber(),
                vscn.getVscnNumber(),
                VSCN_UPLOAD_PROGRAM,
                "uploaded test program file"
        );

        return vscnService.saveVscn(vscn);
    }

    public void changeTestInformation(Vscn vscn, VscnUploadTestForm vscnUploadTestForm){
        String facility = vscn.getFacility().getPdbFacility();
        TravelerComponent oldTestComponent = vscn.findTestTravelerComponentByComponentName("Test Program 1");

        vscn.getTest().setContent(vscnUploadTestForm.getContent());
        testProgramService.parse(vscn, securityService.user());

        String testGroup1 = "VYPER_"+facility+"_TEST_"+ vscn.getMaterial().getSbe();
        String testGroup2 = "VYPER_"+facility+"_TEST_"+ vscn.getMaterial().getSbe()+"_"+vscn.getMaterial().getSbe1();

        if (!vscn.getChangedComponentsGroup().contains(testGroup1)) {
            vscn.getChangedComponentsGroup().add(testGroup1);
        }
        if (!vscn.getChangedComponentsGroup().contains(testGroup2)) {
            vscn.getChangedComponentsGroup().add(testGroup2);
        }

        TravelerComponent newTestComponent = vscn.findTestTravelerComponentByComponentName("Test Program 1");
        if (!StringUtils.equalsIgnoreCase(oldTestComponent.getValue(), newTestComponent.getValue())) {
            for (Verifier verifier : vscn.getVerifiers()) {
                if (verifier.match("Test Program 1", oldTestComponent.getValue())) {
                    verifier.setValue(newTestComponent.getValue());
                }
            }
        }
    }
    
}
