package com.ti.specteam.vswr.dashboard.BatchProcessing.SCSWRService;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

import org.springframework.validation.annotation.Validated;

import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.specteam.vswr.dashboard.domain.BpDiff;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellValueMap;

/**
 *
 * <AUTHOR>
 */
@Validated
public interface SCSWRService {

  public List<String> getATSites();

  public List<String> getSBE1();

  public List<String> getSwrType();

  public List<Map<String, String>> requestsColumns();

  public List<String> getMarketCat();

  public void replaceRedbullRequest(Map scswrList);

  public int validateSwrId(String swrId);

  public List<Map<String, Object>> getUserSecurity(String userId);

  public int confirmAtSite(String atSite);

  public void delRedbullMax(String swrId, String userId);

  public int confirmSbeSite(String sbe1);

  public void handleSwrStatusUpdate(String swrId, String changeReason, String uid, String swrUpdate, boolean isBatch);

  public List<Map<String, Object>> getSbeView(String uid, String swrId);

  public List<String> getSwrIdUpdateList(String uid);

  public List<ScswrExcelCellConfig> getSbeExcelCfgs();
  public List<ScswrExcelCellConfig> getConstrainedSbeExcelConfigs();
  public ScswrExcelCellConfig getSbeExcelCfgByExcelLabel(String excelLabel);

  public List<String> getAtCoordCatEmail(String atSite);

  public Map<String, String> getLdapxref(String org);

  List<Map<String, String>> checkSwrsNoForecast(String swrId, String status);

  void calculateDates(String swrId, String status);

  void updateTarget(String targetDate, String swrId, String status);

  void updateForecastDates(String updatedDate, String swrId, String status);

  void insertForecastDates(String updatedDate, String swrId,
      String status, String targetDate, String actualDate);

  void updateEstDate(String atStartTargetDate,
      String swrId, String atShippedTargetDate);

  Map<String, Object> getRequestRedbull(String swrId, String userId);

  int validateDeviceName(String deviceName, String atSite);

  String getPlantCode(String atSite);

  int validateMatlSpecDevice(String deviceName, String specDevice);

  List<Map<String, Object>> swrListsByStat(String tableName, String swrStat,
      String submitSwrStat, DataTableParams params);

  int countSwrListsByStat(String tableName, String swrStat,
      String submitSwrStat, DataTableParams params);

  public void createRequestsHistoryRecordByRequestRecord(String swrId);


  public static class SWR_UPDATE_ACTION {
    final public static String AT_APPROVAL_SWR = "atApprovalSwr";
    final public static String SUBMIT_SWR = "submitSwr";
    final public static String SAVE_SWR = "saveSwr";
    final public static String VOID_FCST_SWR = "voidFcstSwr";
    final public static String VOID_SWR = "voidSwr";
  }

  void proceedUpdateRequest(List<String> swrIds, String userId, String changeReason,
      @Valid @Pattern(regexp = SWR_UPDATE_ACTION.AT_APPROVAL_SWR
          + "|" + SWR_UPDATE_ACTION.SUBMIT_SWR + "|" + SWR_UPDATE_ACTION.SAVE_SWR + "|"
          + SWR_UPDATE_ACTION.VOID_FCST_SWR + "|" + SWR_UPDATE_ACTION.VOID_SWR) String swrUpdate);

  public class SWR_STATUS {
    final static String VOID = "Void";
    final static String VOID_FORECAST = "Void_Forecast";
    final static String SBE_FORECAST_REVISED = "SBE_Forecast_Revised";
    final static String EDIT = "Edit";
    final static String IN_SIGNOFF = "In_Signoff";
    final static String SBE_SHIPPED_PAPERWORK = "SBE_Shipped_Paperwork";
    final static String SAVED = "Saved";
    final static String DISAPPROVE = "Disapprove";
  }

  public class APPROVAL_TYPE {
    final static String SBE_1 = "y";
    final static String GROUP = "g";
    final static String ITSS_ID = "i";
  }
  void sendSuccessfulSwrUpdateMail(String swrId, String changeReason, Map<String, Object> swrInfo, String userId,
      String swrUpdate, boolean isBatch)
      throws Exception;

  void updateForecastDates(Map<String, Object> swrInfo, String swrId);

  public List<ScswrExcelCellValueMap> getDropdownValuesFromSql(String sqlValueSource);

  public List<BpDiff> getBpDiffs(String uploadedBy, String swrId);

  public boolean isBpDiff(String currentUserId, String swrId);
}
