package com.ti.specteam.vyper.config.generic;

import com.ti.specteam.vyper.config.flowopnconfig.FlowOpnsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/v1/config/lov")
@CrossOrigin(origins = {"*"})
public class LOVConfigController {

    @Autowired
    LOVConfigService lovConfigService;

    @GetMapping
    public ResponseEntity<List<LOVConfigEntity>> getLOVConfig() {
        log.debug("getLOVConfig");
        return ResponseEntity.ok(lovConfigService.getAllOptions());
    }

    @PostMapping
    public ResponseEntity<LOVConfigEntity> createNewOption(@Valid @RequestBody LOVConfigOptionForm newOption) {
        log.debug("createNewOption");
        return ResponseEntity.ok(lovConfigService.addOption(newOption));
    }

    @PutMapping("/{id}")
    public ResponseEntity<LOVConfigEntity> updateOption(@PathVariable Long id, @Valid @RequestBody LOVConfigOptionForm newOption) {
        log.debug("updateOption");
        return ResponseEntity.ok(lovConfigService.updateOption(id, newOption));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteOption(@PathVariable Long id) {
        log.debug("deleteOption");
        return ResponseEntity.ok("Deleted successfully");
    }

    @GetMapping("/name")
    public ResponseEntity<List<LOVConfigEntity>> getLOVOptions(@PathVariable String lovName) {
        log.debug("getLOVOptions");
        return ResponseEntity.ok(lovConfigService.getOptionsForLov(lovName));
    }

}
