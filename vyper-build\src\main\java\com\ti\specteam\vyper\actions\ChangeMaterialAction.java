package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.dataloader.MaterialExtrasLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.SystemName;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfig;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfigService;
import com.ti.specteam.vyper.pgs.PgsService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_DEVICE;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeMaterialAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final PgsService pgsService;
    private final MaterialExtrasLoader materialExtrasLoader;
    private final AuditService auditService;
    private final FlowPackConfigService flowPackConfigService;

    public Build execute(ChangeMaterialForm changeMaterialForm) {
        log.debug("execute(changeMaterialForm:{})", changeMaterialForm);

        Vyper vyper = vyperService.fetchVyper(changeMaterialForm);
        Build build = buildService.fetchBuild(changeMaterialForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        vyper = execute(vyper, build, changeMaterialForm);

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_DEVICE,
                "changed device to: " + changeMaterialForm.getMaterial()
        );

        return buildService.saveBuild(build);
    }

    public Vyper execute(Vyper vyper, Build build, ChangeMaterialForm changeMaterialForm) {
        return execute(vyper, build, changeMaterialForm.getMaterial());
    }

    public Vyper execute(Vyper vyper, Build build, String material) {

        build.resetMaterial();

        build.getMaterial().getObject().put("Material", material);
        build.getMaterial().getSource().appointUser(securityService.user());
        fetchMaterialAndFacilities(material, build);
        materialExtrasLoader.load(vyper, build);

        return vyper;
    }

    /**
     * Retrieve the material info from pgs.
     *
     * @param material The SAP Material to retrieve.
     * @param build    The build in which to store the results.
     */
    public void fetchMaterialAndFacilities(String material, Build build) {
        Map<String, Object> pgsData = pgsService.fetchMaterialSearchUrl(material, 100);
        loadMaterial(pgsData, build);
        loadFacilities(pgsData, build);
        loadPackConfig(build);
    }

    /**
     * Parse the pgs pgsData and extract the material information.
     *
     * @param pgsData The PGS Data Structure.
     * @param build   The build in which to store the results.
     */
    protected void loadMaterial(Map<String, Object> pgsData, Build build) {

        List<Map<String, Object>> items = (List<Map<String, Object>>) pgsData.get("items");
        if (items == null) return;

        items.stream()
                .filter(item -> "Device".equals(item.get("type")))
                .map(item -> {
                    Map<String, Object> rels = (Map<String, Object>) item.get("rels");
                    if (rels == null) return new ArrayList<Map<String, Object>>();

                    Map<String, Object> materialDevice = (Map<String, Object>) rels.get("MaterialDevice");
                    if (materialDevice == null) return new ArrayList<Map<String, Object>>();

                    List<Map<String, Object>> sources = (List<Map<String, Object>>) materialDevice.get("sources");
                    if (sources == null) return new ArrayList<Map<String, Object>>();
                    return sources;
                })
                .flatMap(Collection::stream)
                .map(source -> source == null ? null : (String) source.get("idRef"))
                .distinct()
                .map(id -> {
                    Map<String, Object> metadata = (Map<String, Object>) pgsData.get("metadata");
                    if (metadata == null) return null;

                    Map<String, Object> refObjects = (Map<String, Object>) metadata.get("refObjects");
                    if (refObjects == null) return null;

                    Map<String, Object> refObject = (Map<String, Object>) refObjects.get(id);
                    if (refObject == null) return null;

                    return (Map<String, Object>) refObject.get("attrs");
                })
                .filter(Objects::nonNull)
                .findFirst()
                .ifPresent(material -> build.getMaterial().getObject().putAll(material));
    }

    /**
     * Parse the pgs pgsData and extract the facilities information.
     *
     * @param pgsData The PGS Data Structure.
     * @param build   The build in which to store the results.
     */
    public void loadFacilities(Map<String, Object> pgsData, Build build) {

        List<Map<String, Object>> items = (List<Map<String, Object>>) pgsData.get("items");
        if (items == null) return;

        List<Map<String, Object>> facilities = items.stream()
                .filter(item -> "Device".equals(item.get("type")))
                .map(item -> {
                    Map<String, Object> rels = (Map<String, Object>) item.get("rels");
                    if (rels == null) return new ArrayList<Map<String, Object>>();

                    Map<String, Object> deviceAssySite = (Map<String, Object>) rels.get("DeviceAssySite");
                    if (deviceAssySite == null) return new ArrayList<Map<String, Object>>();

                    List<Map<String, Object>> targets = (List<Map<String, Object>>) deviceAssySite.get("targets");
                    if (targets == null) return new ArrayList<Map<String, Object>>();
                    return targets;
                })
                .flatMap(Collection::stream)
                .map(target -> target == null ? null : (String) target.get("idRef"))
                .distinct()
                .map(id -> {
                    Map<String, Object> metadata = (Map<String, Object>) pgsData.get("metadata");
                    if (null == metadata) return null;

                    Map<String, Object> refObjects = (Map<String, Object>) metadata.get("refObjects");
                    if (null == refObjects) return null;

                    Map<String, Object> refObject = (Map<String, Object>) refObjects.get(id);
                    if (null == refObject) return null;

                    return (Map<String, Object>) refObject.get("attrs");
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        build.getMaterial().getFacilities().addAll(facilities);
    }

    /**
     * Parse the pack configuration value from the pgs data
     *
     * @param build The build in which to store the results.
     */
    public void loadPackConfig(Build build) {

        Object value = build.getMaterial().getObject().get("PackingConfig");
        if(build.getBuildFlow().getFlowId() == null || build.getBuildFlow().getFlowName().equalsIgnoreCase("TKY") || build.getBuildFlow().getFlowName().equalsIgnoreCase("Backend Assembly")){
            build.getPackConfig().getObject().put("value", value);
            build.getPackConfig().getSource().appointSystem(SystemName.PGS);
        }else{
            List<FlowPackConfig> flowPackConfigs = flowPackConfigService.getAllPackConfigByFlowId(build.getBuildFlow().getFlowId());
            List<String> packConfigList =  flowPackConfigs.stream().map(flowPackConfig -> flowPackConfig.getPackConfig().getValue().toString()).collect(Collectors.toList());

            if(packConfigList.size() == 1){
                build.getPackConfig().getObject().put("value", packConfigList.get(0));
                build.getPackConfig().getSource().appointSystem(SystemName.VYPER);
            }else if(packConfigList.indexOf(value) > -1){
                build.getPackConfig().getObject().put("value", value);
                build.getPackConfig().getSource().appointSystem(SystemName.PGS);
            }
        }

    }

}