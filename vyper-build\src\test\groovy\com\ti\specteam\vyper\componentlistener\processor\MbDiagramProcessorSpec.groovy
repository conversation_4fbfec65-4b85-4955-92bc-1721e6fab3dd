package com.ti.specteam.vyper.componentlistener.processor

import com.ti.specteam.vyper.atss.traveler.Traveler
import com.ti.specteam.vyper.build.model.BomName
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.Engineering
import com.ti.specteam.vyper.build.model.Source
import com.ti.specteam.vyper.build.model.SystemName
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.componentlistener.context.AtssLoaderListenerContext
import com.ti.specteam.vyper.componentlistener.context.PgsListenerContext
import com.ti.specteam.vyper.pgs.PgsParserService
import spock.lang.Specification 

class MbDiagramProcessorSpec extends Specification {

    PgsParserService pgsParserService = Mock(PgsParserService)

    MbDiagramProcessor processor = new MbDiagramProcessor(
            pgsParserService
    )

    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _
    }

    def "onPgsLoader - load the MB Diagrams"() {
        def bomName1 = new BomName(id: "ID1", name: "BOM1")

        build1.findOrCreateComponent("MB Diagram", Source.VYPER)

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.metadataRefObjectById(_, "ID1") >> [
                attrs: [MBDiagramNumRev: ["NUMBER1 REV1", "NUMBER2 REV2"]]
        ]

        and:
        build1.getComponentKeyValue("MB Diagram", 0, 0, "name") == "NUMBER1"
        build1.getComponentKeyValue("MB Diagram", 0, 0, "revision") == "REV1"
        build1.getComponentKeyValue("MB Diagram", 0, 1, "name") == "NUMBER2"
        build1.getComponentKeyValue("MB Diagram", 0, 1, "revision") == "REV2"

    }

    def "onAtssLoader - traveler component not found - marked as processed"() {

        def traveler = new Traveler()
        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("MB Diagram")
    }

    def "onAtssLoader - component not found - marked as processed"() {

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("MB Diagram", "CVALUE1")
        tc.create("Revision", "A")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("MB Diagram")
    }

    def "onAtssLoader - sets the build component priority value"() {
        build1.findOrCreateComponent("MB Diagram", Source.VYPER)

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("MB Diagram", "CVALUE1")
        tc.create("Revision", "A")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("MB Diagram")

        and:
        build1.getComponentKeyValue("MB Diagram", 0, 0, "name") == "CVALUE1"
        build1.getComponentKeyValue("MB Diagram", 0, 0, "revision") == "A"
    }

    def "onAtssLoader - change pgs source to atss"() {
        def c1 = build1.findOrCreateComponent("MB Diagram", Source.PGS)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "CVALUE1", Engineering.N)
        p1.put "revision", "A"

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("MB Diagram", "CVALUE1")
        tc.create("Revision", "A")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        build1.findComponentByName("MB Diagram").getPriority(0, 0).source.system.name == SystemName.ATSS
    }
    
}
