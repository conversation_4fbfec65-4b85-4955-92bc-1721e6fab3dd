spring.main.allow-circular-references=true
#
#
#
server.port=10004
server.servlet.context-path=/
spring.application.name=vyper-build
#
# logging
#
logging.file.name=vyper-build
logging.level.com.ti.specteam=DEBUG
#logging.level.org.springframework.mvc=INFO
#logging.level.web=INFO
#logging.level.sql=INFO
#logging.level.org.springframework.batch=TRACE
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
#logging.level.org.springframework.cache=trace
#
# actuator
#
management.endpoints.web.exposure.include=*
management.endpoint.health.probes.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.readiness.include=db
management.endpoint.health.show-details=always

info.app.encoding=UTF-8
info.app.java.source=21.0.7
info.app.java.target=21.0.7
#
# JPA
#
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.order_by.default_null_ordering=last
#
# vyper database (password in naps)
#
spring.datasource.vyper.jdbc-url=*********************************************************************
spring.datasource.vyper.username=LEDVYPER_RW
spring.datasource.vyper.password=dVWcimx1yUFlrEs
spring.datasource.vyper.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.vyper.hikari.connection-test-query=select 1 from dual
#
# spring batch
#
# create the batch database schema
spring.batch.jdbc.initialize-schema=always
#
# second, minute, hour, day of month, month, day of week
# 7am, 7pm daily
batch.refresh.cron=0 0 7,19 ? * *
#
# atss_cams_table
# 8am, 8pm daily
#
atss_cams_table.refresh.cron=0 0 8,20 ? * *
#
# refresh the approval operations hourly
#
approval_operation.refresh.cron=0 0 * * * ?

# false = don't run the jobs on startup
spring.batch.job.enabled=false
#
# configure the batch job
vyper.batch.fetchSize=200
vyper.batch.chunkSize=200
#
# security
#
app.security.models[0].name=oidc
app.security.models[0].headerPrefix=X-ZUUL-TI-
app.security.models[0].entityHeader=UID
app.security.models[0].onBehalfOfHeader=X-ZUUL-TI-AUTH-UID
app.security.models[0].authHeaders=ROLE
app.security.models[0].enabled=true
#
app.security.models[1].name=jwt
app.security.models[1].headerPrefix=X-ZUUL-TI-
app.security.models[1].entityHeader=UID
app.security.models[1].onBehalfOfHeader=X-ZUUL-TI-AUTH-UID
app.security.models[1].authHeaders=ROLE
app.security.models[1].enabled=false
#
# product golden source
#
pgs.api.header-name=PGS-CLIENT
pgs.api.header-value=VYPER
pgs.api.base-url=https://api.itg.ti.com
pgs.api.access-token-uri=/v1/oauth/accesstoken
pgs.api.client-id=rSJpPDyqpZqYV0wSuBZAW1vA8JyNpANH
pgs.api.client-secret=eFTzMoFizkHt6ASs
pgs.api.grant-type=client_credentials
pgs.api.material-search-uri=/v1/pgs/objects/Device?limit=10&expandFromRel=DeviceAssySite&expandToRel=MaterialDevice&q=Material:<material>
pgs.api.material-search-by-plant-uri=/v1/pgs/objects/Device?limit=<size>&expandFromRel=DeviceAssySite&expandToRel=MaterialDevice&q=Material:<material>&q=AssemblyPlantCode:<plantcodes>
pgs.api.device-lookup-uri=/v1/pgs/objects/Device?expandFromRel=AssemblyDieConf,AssyBOM,AssyBOMWire,AssyBOMLeadframe,AssyBOMMountComp,AssyBOMMoldComp,AssyBOMDie,DieBump,DieFabSite,AssyBOMDieConf&expandToRel=MaterialDevice&expandLevel=4&matchState=G&q=Material:<material>,AssemblyPlantCode:<plantcode>
pgs.api.die-lookup-uri=/v1/pgs/objects/Die?limit=500&q=Die:<die>
pgs.api.arm-arc-job-lookup-uri:/v1/pgs/objects/Device?expandFromRel=AssyBOM,AssyBOMARCJob&expandToRel=MaterialDevice&expandLevel=4&matchState=G&q=Material:<material>,AssemblyPlantCode:<plantcode>

#
# package niche
#
pkgniche.api.base-url=https://api.itg.ti.com
pkgniche.api.access-token-uri=/v1/oauth/accesstoken
pkgniche.api.client-id=rSJpPDyqpZqYV0wSuBZAW1vA8JyNpANH
pkgniche.api.client-secret=eFTzMoFizkHt6ASs
pkgniche.api.grant-type=client_credentials
pkgniche.api.material-niche-by-material-uri=/v1/pkgDB/Material/NicheByMaterial?material=<material>
pkgniche.api.material-niche-by-ppp-uri=/v1/pkgDB/Material/NicheByPPP?pkgGroup=<pkgGroup>&pkgDesg=<pkgDesg>&pinCount=<pinCount>
pkgniche.api.pkgniche-info-uri=/v1/pkgDB/PkgNiche/Info?pkgNiche=<pkgniche>
#
# bom template
#
bomtemplate.api.header-name=${VYPER_BOMTEMPLATE_API_HEADER_NAME}
bomtemplate.api.header-value=${VYPER_BOMTEMPLATE_API_HEADER_VALUE}
bomtemplate.api.base-url=https://api.itg.ti.com
bomtemplate.api.access-token-uri=/v1/oauth/accesstoken
bomtemplate.api.client-id=rSJpPDyqpZqYV0wSuBZAW1vA8JyNpANH
bomtemplate.api.client-secret=eFTzMoFizkHt6ASs
bomtemplate.api.grant-type=client_credentials
bomtemplate.api.flowtemplates-search-uri=/v1/pkgDB/FlowTemplates/Search?pkgNiche=<pkgniche>&atSite=<atsite>
#
# rolesvc
#
rolesvc.api.base-url=https://api-dev.itg.ti.com
rolesvc.api.access-token-uri=/v1/oauth/accesstoken
rolesvc.api.client-id=lW3OAM437GGTl8JHwRRGrpgSCcysPLAG
rolesvc.api.client-secret=rw2DhOBdRoAJkXyr
rolesvc.api.grant-type=client_credentials
rolesvc.api.authorities-uri=/v1/foundation/rolemgmt/me/authorities?uid=<userid>
rolesvc.api.permissions-uri=/v1/foundation/rolemgmt/AF3FDF2298A1BE7FE05343CBB40A375C/permission/list
#
# jackson config
#
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
#
# ATSS Traveler
#
atss.spectrav.url=https://spectrav-dev.itg.ti.com/travss/services
#
# email
#
spring.mail.host=smtp.mail.ti.com
vyper.email.from=<EMAIL>
vyper.batchprocessing.email.from=<EMAIL>
vyper.batchprocessing.email.serverName=scswr-dev.itg.ti.com
vyper.batchprocessing.email.swrDashboardToolLink=https://simba-dev.itg.ti.com/vyper/batch/listswrs
spring.mail.properties.mail.smtp.sendpartial=true
scswr.bom.comments.enable=true
scswr.bom.Fields=LeadFrame, MountComp, MoldComp, Wire
#
# bom template request email
#
bom-template-request-email.to=<EMAIL>
bom-template-request-email.subject=Bill of Process Template Review
#
# Vyper Build link
#
vyper.link.baseUrl=https://simba-dev.itg.ti.com/vyper
vyper.link.buildSelectionLink=/projects/<vypernumber>/builds/<buildnumber>/selection
vyper.link.buildSingleBuildPage=/projects/<vypernumber>/builds/<buildnumber>
vyper.link.vscnSingleVscnPage=/projects/<vypernumber>/vscns/<vscnNumber>
#
# Comment Notification Email
#
comment-notification.subject=[VYPER] Comment Notification - <buildnumber> (<device>/<facility>)
#
# Build Task Email
#
task-notification.subject=[VYPER - <environment>] Task Notification - <buildnumber> (<device>/<facility>) - <sbe>/<sbe-1>
task-notification.header=[VYPER] - Task Notification <buildnumber>
task-notification.calltoaction=To View This Task
task-notification.enable-emails=false
task-notification.environment=Dev
#
# SCSWR
#
spring.datasource.scswr.jdbc-url=**************************************************************
spring.datasource.scswr.username=SCSWR
spring.datasource.scswr.password=msfI35rbHOm8qB2ust
spring.datasource.scswr.driver-class-name=oracle.jdbc.OracleDriver
#
# Task Service 2.0
#
task-service.api.base-url=https://api-dev.itg.ti.com
task-service.api.access-token-uri=/v1/oauth/accesstoken
task-service.api.client-id=lW3OAM437GGTl8JHwRRGrpgSCcysPLAG
task-service.api.client-secret=rw2DhOBdRoAJkXyr
task-service.vyper-uuid=AF3FDF2298A1BE7FE05343CBB40A375C
#
# External Manufacturing api
#
emas.api.base-url=https://api-dev.itg.ti.com
emas.api.access-token-uri=/v1/oauth/accesstoken
emas.api.client-id=lW3OAM437GGTl8JHwRRGrpgSCcysPLAG
emas.api.client-secret=rw2DhOBdRoAJkXyr
emas.api.vyper-uuid=EFAE1D165BCF394DE0530868AC0A427A

#
# System userid(s) which should be allowed access to Vyper API
#
vyper.api.config.system-users-list=TASKSVC

#
# kafka and topics configuration
#
app.kafka.enabled=true
app.kafka.bootstrap-servers=lelvrpmfgd01.itg.ti.com:9092,lelvrpmfgd02.itg.ti.com:9092,lelvrpmfgd03.itg.ti.com:9092
app.kafka.security.protocol=SASL_SSL
app.kafka.security.sasl-mechanism=SCRAM-SHA-512
app.kafka.security.sasl-jaas-username=dhms_vyper
app.kafka.security.sasl-jaas-password=M,amtvz6p-QE
app.kafka.security.sasl-jaas-config=org.apache.kafka.common.security.scram.ScramLoginModule required \
  username="${app.kafka.security.sasl-jaas-username}" \
  password="${app.kafka.security.sasl-jaas-password}";
app.kafka.consumer.group-id=KAFKA_CONSUMER_GROUP_SHARATH
app.kafka.consumer.topic.atss-api=internal.dhms.vyper.atssresponse
app.kafka.producer.topic.atss-api=internal.dhms.vyper.atssrequest
app.kafka.notification.message=[ATSS Vyper Response - Dev ] <message>
app.kafka.max-request-size=104857600
#
# ATSS API Configuration
#
atss.api.config.base-url=http://localhost:8080/api/v1
atss.api.config.api-secret=8a419124-7c75-4069-a935-a707da0f9072
atss.api.config.cams-component-url=/cams/components/bulk

server.error.include-message=always