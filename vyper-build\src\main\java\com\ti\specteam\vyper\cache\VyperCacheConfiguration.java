package com.ti.specteam.vyper.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
public class VyperCacheConfiguration {

    @Primary
    @Bean
    public CacheManager vyperCacheManager() {

        CaffeineCacheManager cacheManager = new CaffeineCacheManager(
                "component.choices",
                "componentmap.findbyname",
                "autocomplete.buildnumber",
                "attributes",
                "userdetails.loaduserbyusername",
                "approvaloperation.findByOperation",
                "rolesvc.getroles",
                "basename.findAllComponentNamesByComponentName",
                "emaspermissions",
                "approvaloperation.findListByGroup",
                "attribute.transformRules"
        );

        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(10_000)
                .expireAfterWrite(1, TimeUnit.HOURS));
        return cacheManager;
    }

    @Bean
    public CacheManager atssCacheManager() {

        CaffeineCacheManager cacheManager = new CaffeineCacheManager(
                "atss.operations",
                "atss.components",
                "atss.attributes",
                "atss.component.attributes",
                "sap.die.attributes",
                "atss.attributeUnitMap"
        );

        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(10_000)
                .expireAfterWrite(2, TimeUnit.HOURS));

        return cacheManager;
    }

    @Bean
    public CacheManager praCacheManager() {

        CaffeineCacheManager cacheManager = new CaffeineCacheManager(
                "init");

        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(10_000)
                .expireAfterWrite(2, TimeUnit.HOURS));

        return cacheManager;
    }
}
