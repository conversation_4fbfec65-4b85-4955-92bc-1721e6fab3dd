package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.atss.cams.AtssCams;
import com.ti.specteam.vyper.atss.cams.AtssCamsService;
import com.ti.specteam.vyper.atss.paragraph.ParagraphService;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.dataloader.PgsLoader;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.componentlistener.listener.ChangeSelectionListener;
import com.ti.specteam.vyper.componentlistener.context.ChangeSelectionListenerContext;
import com.ti.specteam.vyper.pgs.PgsService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_SELECTION;
import static com.ti.specteam.vyper.build.componentmap.util.ComponentUniqueness.SINGLE_VALUE;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeSelectionAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final ComponentMapService componentMapService;
    private final ChangeSymbolizationAction changeSymbolizationAction;
    private final ParagraphService textService;
    private final AtssCamsService atssCamsService;
    private final PgsService pgsService;
    private final PgsLoader pgsLoader;
    private final AuditService auditService;
    private final List<ChangeSelectionListener> listeners;

    public Build execute(ChangeSelectionForm changeSelectionForm) {
        log.debug("execute(changeSelectionForm:{})", changeSelectionForm);

        Vyper vyper = vyperService.fetchVyper(changeSelectionForm);
        Build build = buildService.fetchBuild(changeSelectionForm);
        String cName = changeSelectionForm.getName();

        // if the symbol is changing, handle it
        if ("Topside Symbol".equals(cName)) {
            return changeSymbol(changeSelectionForm, vyper, build);
        }

        ComponentMap componentMap = componentMapService.findByName(cName);
        if (componentMap == null) {
            return build;
        }

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        if (StringUtils.equalsIgnoreCase("Die", cName)) {
            changeDie(changeSelectionForm, build);
        } else if (StringUtils.equalsIgnoreCase("Backgrind", cName)) {
            changeBackgrind(changeSelectionForm, build);
        } else {
            changeComponent(changeSelectionForm, build, cName, componentMap);
        }

        // update selections - this is for the components that are not on the build form
        // this sets their values
        updateSelection(vyper, build, componentMap, changeSelectionForm.getOperation(), changeSelectionForm.getItems());

        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // notify the listeners
        ChangeSelectionListenerContext context = ChangeSelectionListenerContext.builder()
                .vyper(vyper)
                .build(build)
                .form(changeSelectionForm)
                .componentName(cName)
                .build();

        listeners.forEach(listener -> listener.onChangeSelection(context));

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_SELECTION,
                "changed selection for " + changeSelectionForm.getName() + " to : " + changeSelectionForm.display()
        );

        return buildService.saveBuild(build);
    }

    public void changeBackgrind(ChangeSelectionForm changeSelectionForm, Build build) {
        Backgrind backgrind = build.getBackgrind();

        backgrind.getBackgrindSelected().clear();
        backgrind.getBackgrindSelected().addAll(changeSelectionForm.getItems());
        backgrind.getSource().getUser().setUserid(securityService.user().getUserid());
        backgrind.getSource().getUser().setUsername(securityService.user().getUsername());
    }

    /**
     * Handle a change of symbol by delegating to ChangeSymbolAction.
     */
    public Build changeSymbol(ChangeSelectionForm changeSelectionForm, Vyper ignoredVyper, Build build) {
        // delegate to the change symbol action for symbols
        if (changeSelectionForm.getItems().isEmpty()) {
            return build;
        }
        // create a component for the symbolization
        SelectionItem selectionItem = changeSelectionForm.getItems().get(0);
        ChangeSymbolizationForm form = new ChangeSymbolizationForm();
        form.setVyperNumber(changeSelectionForm.getVyperNumber());
        form.setBuildNumber(changeSelectionForm.getBuildNumber());
        form.setSymbol(new SymbolObject());
        form.getSymbol().setLocation(SymbolLocation.TOP);
        form.getSymbol().setName(selectionItem.getValue());
        form.getSymbol().setPicture(textService.getTextString("Topside Symbol", selectionItem.getValue()));
        form.setCustoms(new ArrayList<>());
        // add the custx objects
        Symbol.findAllCustPlaceholders(form.getSymbol().getPicture()).forEach(custx -> {
            Selection selection = build.findSelection("HEADER", custx);
            String value = null;
            if (selection != null) {
                if (!selection.getItems().isEmpty()) {
                    value = selection.getItems().get(0).getValue();
                }
            }
            CustomObject customObject = new CustomObject();
            customObject.setName(custx);
            customObject.setValue(value);
            form.getCustoms().add(customObject);
        });
        return changeSymbolizationAction.execute(form);
    }

    public void changeComponent(ChangeSelectionForm changeSelectionForm, Build build, String cName, ComponentMap componentMap) {
        // update the component

        // update the components if
        // 1. there is a component map
        // 2. componentMap.selectionChangeAlsoChangesComponent is true or null
        // 3. the component exists

        // otherwise, we just clear the values from the component

        Component component = build.findComponentByName(cName);

        switch (componentMap.getName()) {
            case "Leadframe":
            case "Mold Compound":
            case "Mount Compound":
            case "Wire":
                changeComponentSpecial(changeSelectionForm, build, componentMap, component);
                break;

            default:
                changeComponentNormal(changeSelectionForm, build, componentMap, component);
        }
    }

    private void changeComponentNormal(ChangeSelectionForm changeSelectionForm, Build build, ComponentMap componentMap, Component component) {
        if (build.componentExists(componentMap.getName())) {
            component.clear();
            for (SelectionItem selectionItem : changeSelectionForm.getItems()) {
                ComponentInstance componentInstance1 = new ComponentInstance();
                component.getInstances().add(componentInstance1);
                ComponentPriority componentPriority = new ComponentPriority();
                componentInstance1.getPriorities().add(componentPriority);

                componentPriority.setEngineering(selectionItem.getEngineering());
                componentPriority.getObject().put("name", selectionItem.getValue());
                componentPriority.getSource().appointUser(securityService.user());
                component.getSource().appointUser(securityService.user());
            }
        }
    }

    private void changeComponentSpecial(ChangeSelectionForm changeSelectionForm, Build build, ComponentMap componentMap, Component component) {
        // clear the component values and start a new instance
        component.clear();
        ComponentInstance componentInstance = new ComponentInstance();
        component.getInstances().add(componentInstance);

        // loop through the items
        changeSelectionForm.getItems().forEach(selectionItem -> {

            // query for the atss_cams record
            AtssCams atssCams = atssCamsService.findByFacilityAndComponentNameAndComponentValue(
                    build.getFacility().getPdbFacility(),
                    componentMap.getAtssComponentName(),
                    selectionItem.getValue());

            // if we have a record
            if (null == atssCams) {
                addDefault(componentInstance, selectionItem);
            } else if (StringUtils.equalsIgnoreCase("Wire", componentMap.getName())) {
                addWire(componentInstance, selectionItem, atssCams);
            } else if (StringUtils.equalsIgnoreCase("Leadframe", componentMap.getName())) {
                addLeadframe(componentInstance, selectionItem);
            } else {
                addWithAttribute(componentInstance, selectionItem, atssCams.getComponentValue(), "SupplierPartNumber", atssCams.getSupplierNumber());
            }

        });
    }

    private void addDefault(ComponentInstance componentInstance, SelectionItem selectionItem) {
        ComponentPriority componentPriority = new ComponentPriority();
        componentPriority.getObject().put("name", selectionItem.getValue());
        componentPriority.setEngineering(selectionItem.getEngineering());
        componentPriority.getSource().appointUser(securityService.user());
        componentInstance.getPriorities().add(componentPriority);
    }

    private void addLeadframe(ComponentInstance componentInstance, SelectionItem selectionItem) {
        addWithAttribute(componentInstance, selectionItem, selectionItem.getValue(), "LeadframePartNumber", selectionItem.getValue());
    }

    private void addWithAttribute(ComponentInstance componentInstance, SelectionItem selectionItem,
                                  String componentValue, String aName, String aValue) {
        ComponentPriority componentPriority = new ComponentPriority();
        componentPriority.getObject().put("PartNumber", componentValue);
        componentPriority.getObject().put(aName, aValue);
        componentPriority.getObject().put("name", aValue);
        componentPriority.setEngineering(selectionItem.getEngineering());
        componentPriority.getSource().appointUser(securityService.user());
        componentInstance.getPriorities().add(componentPriority);
    }

    private void addWire(ComponentInstance componentInstance, SelectionItem selectionItem, AtssCams atssCams) {
        // split the wire into type and diameter
        String type = null;
        String diameter = null;
        String[] fields = atssCams.getWire().split(" ");
        if (2 == fields.length) {
            type = fields[0];
            diameter = fields[1];
        }

        // add the wire attributes
        ComponentPriority componentPriority = new ComponentPriority();
        componentPriority.getObject().put("PartNumber", atssCams.getComponentValue());
        componentPriority.getObject().put("WireDiameterMils", diameter);
        componentPriority.getObject().put("WireType", type);
        componentPriority.getObject().put("name", atssCams.getWire());
        componentPriority.setEngineering(selectionItem.getEngineering());
        componentPriority.getSource().appointUser(securityService.user());
        componentInstance.getPriorities().add(componentPriority);
    }

    public void changeDie(ChangeSelectionForm changeSelectionForm, Build build) {

        // find the die instance that we are changing
        DieInstance dieInstance = build.getDies().findDieInstanceByType(changeSelectionForm.getName());
        if (dieInstance == null) {
            // not found? create it
            dieInstance = new DieInstance();
            dieInstance.setType(changeSelectionForm.getName());
            build.getDies().getDieInstances().add(dieInstance);
        } else {
            // found? clear it
            dieInstance.clear();
        }

        // now create the dies for the instance
        List<Die> dies = changeSelectionForm.getItems()
                .stream()
                .map(selectionItem -> {
                    Die die = new Die();
                    die.setName(selectionItem.getValue());
                    die.setEngineering(selectionItem.getEngineering());
                    die.setIncomingWaferThick(0);

                    // retrieve the die attributes from pgs
                    Map<String, Object> attrs = pgsService.fetchDieAttribute(die.getName());
                    attrs.keySet().forEach(key -> die.getObject().put(pgsLoader.fixAttributeName(key), attrs.get(key)));

                    return die;
                })
                .collect(Collectors.toList());

        dieInstance.getDies().addAll(dies);

    }

    /**
     * update the selections object.
     */
    public void updateSelection(@SuppressWarnings("unused") Vyper vyper, Build build, ComponentMap componentMap, String oName, List<SelectionItem> items) {

        String cName = componentMap.getName();

        // update the selections
        if (null == componentMap.getComponentUniqueness() || componentMap.getComponentUniqueness() == SINGLE_VALUE) {

            // handle values global to all operations
            for (FlowOperation flowOperation : build.findAllFlowOperationsByComponentName(cName)) {
                Selection selection = build.findOrCreateSelection(flowOperation.getName(), cName, new Source().appointUser(securityService.user()));
                selection.getItems().clear();
                selection.getItems().addAll(items);
                selection.getItems().forEach(selectionItem -> selectionItem.getSource().appointUser(securityService.user()));
            }
        } else {
            // handle values unique to their operations
            Selection selection = build.findOrCreateSelection(oName, cName, new Source().appointUser(securityService.user()));
            selection.getItems().clear();
            selection.getItems().addAll(items);
            selection.getItems().forEach(selectionItem -> selectionItem.getSource().appointUser(securityService.user()));
        }
    }

}
