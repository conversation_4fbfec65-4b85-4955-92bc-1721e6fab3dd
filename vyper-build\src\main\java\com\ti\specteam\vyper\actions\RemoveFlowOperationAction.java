package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.Required;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.RequiredComponentLoader;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Engineering;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.util.AuthorizedOperationService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.REMOVE_FLOW_OPERATION;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RemoveFlowOperationAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final AuthorizedOperationService authorizedOperationService;

    public Build execute(RemoveFlowOperationForm removeFlowOperationForm) {
        log.debug("execute(removeFlowOperationForm:{})", removeFlowOperationForm);

        Vyper vyper = vyperService.fetchVyper(removeFlowOperationForm);
        Build build = buildService.fetchBuild(removeFlowOperationForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        if (!authorizedOperationService.canUpdateOperation(vyper, build, removeFlowOperationForm.getName())) {
            throw new VyperException("Insufficient privilege rights for the user to delete operation.");
        }

        // if the operation is required, then we'll do an engineering delete

        FlowOperation flowOperation = build.getFlow().getObject().getOperations().get(removeFlowOperationForm.getIndex());

        if (null != flowOperation) {

            boolean engineeringBuild = build.getFlow().getObject().getEngineering() == Engineering.Y;
            boolean engineeringForm = removeFlowOperationForm.getEngineering() == Engineering.Y;
            boolean required = flowOperation.getRequired() == Required.REQUIRED;

            /*
                engB | engF | req | action
                N       N      N      normal delete
                N       N      Y      throw
                N       Y      N      soft delete
                N       Y      Y      soft delete
                Y       N      N      soft delete
                Y       N      Y      soft delete
                Y       Y      N      soft delete
                Y       Y      Y      soft delete
             */

            if (!engineeringBuild && !engineeringForm && !required) {

                // remove the operation
                build.getFlow().getObject().getOperations().remove(removeFlowOperationForm.getIndex());

            } else if (!engineeringBuild && !engineeringForm && required) {

                throw new VyperException("Cannot delete required operation. The buildtype is not Experimental.");

            } else {

                flowOperation.setEngineeringDeleted(true);

                build.getFlow().getObject().setEngineering(Engineering.Y);
            }

        }

        // refresh
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                REMOVE_FLOW_OPERATION,
                "removed flow operation: " + removeFlowOperationForm.getName()
        );

        return buildService.saveBuild(build);
    }

}
