package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ti.specteam.vyper.audit.AuditActivity.ADD_OWNER;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AddOwnerAction {

    private final VyperService vyperService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Vyper execute(AddOwnersRequest addOwnersRequest) {
        log.debug("execute(vyperNumber:{}, ownersList:{})", addOwnersRequest.getVyperNumber(), addOwnersRequest.getOwnersList());

        Vyper vyper = vyperService.fetchVyper(addOwnersRequest.getVyperNumber());

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);

        List<String> auditDetails = new ArrayList<>();
        addOwnersRequest.getOwnersList().forEach(addOwnerForm -> {
            // don't add if the user is already an owner
            User user = vyper.getOwners().stream()
                    .filter(u -> u.getUserid().equalsIgnoreCase(addOwnerForm.getUserid()))
                    .findFirst()
                    .orElse(null);

            if (null == user) {
                vyper.getOwners().add(new User(
                        addOwnerForm.getUserid(),
                        addOwnerForm.getUsername()
                ));
                auditDetails.add(addOwnerForm.display());
            }
        });

        // Join the audit details into a single string
        String detail = String.join(", ", auditDetails);

        // save the audit
        auditService.createVyper(
                vyper.getVyperNumber(),
                ADD_OWNER,
                "added owners: " + detail
        );

        return vyperService.saveVyper(vyper);
    }


}
