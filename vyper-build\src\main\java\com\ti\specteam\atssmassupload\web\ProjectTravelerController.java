package com.ti.specteam.atssmassupload.web;

import com.ti.specteam.atssmassupload.domain.AtssTravelerCreateRequest;
import com.ti.specteam.atssmassupload.domain.TravelerMode;
import com.ti.specteam.atssmassupload.service.ProjectDeviceTravelerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/v1/atssmassupload/traveler")
@CrossOrigin(origins = {"*"})
@Slf4j
@RequiredArgsConstructor
public class ProjectTravelerController {

    private final ProjectDeviceTravelerService projectDeviceTravelerService;

    @GetMapping("/{projectId}")
    public ResponseEntity<?> getProjectTravelers(@PathVariable String projectId,
                                                                     @RequestParam(defaultValue = "ATSS") TravelerMode travelerMode){
        log.debug("getProjectTravelers {}",projectId);
        if(travelerMode == TravelerMode.ATSS) {
            return ResponseEntity.ok(projectDeviceTravelerService.getProjectTraveler(projectId, travelerMode));
        }else {
            return ResponseEntity.ok(projectDeviceTravelerService.getProjectDeviceTravelerVyper(projectId, travelerMode));
        }
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping(value = "/create", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> createAtssTraveler(@RequestBody AtssTravelerCreateRequest atssTravelerCreateRequest){
        log.debug("createAtssTraveler {}",atssTravelerCreateRequest);
        projectDeviceTravelerService.createAtssTraveler(atssTravelerCreateRequest);
        return ResponseEntity.status(HttpStatus.CREATED).body(atssTravelerCreateRequest);
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @GetMapping("/{projectId}/camsdiff")
    public ResponseEntity<?> getCamsDiff(@PathVariable String projectId){
        log.debug("getCamsDiff {}",projectId);
        return ResponseEntity.ok(projectDeviceTravelerService.getCamsDiff(projectId));
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/{projectId}/cams/create")
    public ResponseEntity<Map<String, Object>> createProjectCams(@PathVariable String projectId){
        log.debug("createProjectCams {}",projectId);
        return ResponseEntity.ok(projectDeviceTravelerService.createNewComponentsInAtss(projectId));
    }
}
