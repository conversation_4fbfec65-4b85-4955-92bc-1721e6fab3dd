package com.ti.specteam.vyper.actions.vscn;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ti.specteam.vyper.build.model.CustomObject;
import com.ti.specteam.vyper.build.model.SymbolObject;
import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@Jacksonized
public class ChangeVscnSymbolizationForm extends VscnNumberForm {

    @NotNull
    private SymbolObject symbol;

    @NotNull
    private List<CustomObject> customs;

    @NotNull
    private String ecat;

    @JsonIgnore
    public String display() {
        List<String> names = new ArrayList<>();
        names.add(symbol.getName());
        for (CustomObject customObject : customs) {
            names.add(customObject.getValue());
        }
        return String.join(", ", names);
    }
}
