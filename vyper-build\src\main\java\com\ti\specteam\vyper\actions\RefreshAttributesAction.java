package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.AtssLoader;
import com.ti.specteam.vyper.build.dataloader.PgsLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.topsymbol.TopSymbolLoader;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class RefreshAttributesAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final PgsLoader pgsLoader;
    private final TopSymbolLoader topSymbolLoader;
    private final AtssLoader atssLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;

    public Build execute(RefreshAttributesForm refreshAttributesForm) {
        log.debug("execute(refreshAttributesForm:{})", refreshAttributesForm);

        Vyper vyper = vyperService.fetchVyper(refreshAttributesForm);
        Build build = buildService.fetchBuild(refreshAttributesForm);

        validateService.checkEditable(vyper, build);

        pgsLoader.refreshAllDieAttributes(build);
        topSymbolLoader.load(vyper, build);
        atssLoader.loadAttributes(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        return buildService.saveBuild(build);
    }

}
