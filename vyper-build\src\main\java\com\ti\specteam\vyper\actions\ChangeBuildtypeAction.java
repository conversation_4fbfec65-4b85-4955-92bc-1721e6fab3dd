package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.validate.ValidateService;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_BUILDTYPE;


@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeBuildtypeAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Build execute(ChangeBuildtypeForm changeBuildtypeForm) {
        log.debug("execute(changeBuildtypeForm:{})", changeBuildtypeForm);

        Vyper vyper = vyperService.fetchVyper(changeBuildtypeForm);
        Build build = buildService.fetchBuild(changeBuildtypeForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        if (StringUtils.isBlank(changeBuildtypeForm.getBuildtype())) throw new VyperException("The Buildtype is invalid.");

        execute(vyper, build, changeBuildtypeForm);

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_BUILDTYPE,
                "changed buildtype to: " + build.getBuildtype()
        );

        return buildService.saveBuild(build);
    }

    public Vyper execute(Vyper vyper, Build build, ChangeBuildtypeForm changeBuildtypeForm) {
        return execute(vyper, build, changeBuildtypeForm.getBuildtype().trim());
    }

    public Vyper execute(Vyper vyper, Build build, String buildtype) {
        build.setBuildtype(buildtype);
        return vyper;
    }


}
