package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DieLotInfo {

    private String vswrID;
    private String dieLot;
    private String plant;
    private String matShipStatus;
    private String deliveryNote;
    private String dateShipped;
    private String sapWaybill;
    private String qtyToShip;
    private String location;

    private String matlMasterDieName;
    private String waferNumToUse;
    private String probed;
    private String isInkless;
    private String buildBy;
    private String stdMapLocation;
    private String pickupBin;
    private String useGecs;
    private String invoice;
}
