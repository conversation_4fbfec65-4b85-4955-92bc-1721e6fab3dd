package com.ti.specteam.vyper.build.vyper;

import com.ti.specteam.vyper.actions.*;
import com.ti.specteam.vyper.atss.attribute.AttributeService;
import com.ti.specteam.vyper.atss.child.Child;
import com.ti.specteam.vyper.atss.child.ChildService;
import com.ti.specteam.vyper.atss.device.AtssMaterialService;
import com.ti.specteam.vyper.atss.facility.AtssFacilityService;
import com.ti.specteam.vyper.atss.facility.FacilityAndPlantCode;
import com.ti.specteam.vyper.atss.facility.FacilityService;
import com.ti.specteam.vyper.atss.operations.OperationRepository;
import com.ti.specteam.vyper.build.ComponentService;
import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.sandbox.PgsSandboxResult;
import com.ti.specteam.vyper.build.sandbox.PgsSandboxService;
import com.ti.specteam.vyper.dashboard.DashBoardService;
import com.ti.specteam.vyper.dashboard.DashboardResult;
import com.ti.specteam.vyper.device.Device;
import com.ti.specteam.vyper.device.DeviceReportRepository;
import com.ti.specteam.vyper.device.DeviceService;
import com.ti.specteam.vyper.entity.Project;
import com.ti.specteam.vyper.entity.build.BuildEntityService;
import com.ti.specteam.vyper.entity.praBuildReport.PraBuildReportEntity;
import com.ti.specteam.vyper.entity.vyper.VyperEntity;
import com.ti.specteam.vyper.entity.vyper.VyperEntityNotFoundException;
import com.ti.specteam.vyper.entity.vyper.VyperEntityService;
import com.ti.specteam.vyper.packageniche.PackageNicheService;
import com.ti.specteam.vyper.praBuildReport.PraBuildReportService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.workflow.RejectReasonsConfigService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.JDOMException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.mail.MessagingException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.soap.SOAPException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Woods
 */
@RestController
@RequestMapping("/v1/vyper")
@CrossOrigin(origins = {"*"})
@Slf4j
@RequiredArgsConstructor
public class VyperController {

    private final VyperEntityService vyperEntityService;
    private final OperationRepository operationRepository;
    private final ComponentService componentService;
    private final AttributeService attributeService;
    private final ChangeTitleAction changeTitleAction;
    private final ChangeDescriptionAction changeDescriptionAction;
    private final ChangeScswrControlNumberAction changeScswrControlNumberAction;
    private final ChangeBuildtypeAction changeBuildtypeAction;
    private final AddOwnerAction addOwnerAction;
    private final RemoveOwnerAction removeOwnerAction;
    private final UploadTestAction uploadTestAction;
    private final ChangeMaterialAction changeMaterialAction;
    private final ChangeWorkflowAction changeWorkflowAction;
    private final ChangeFacilityAction changeFacilityAction;
    private final AddCommentAction addCommentAction;
    private final ChangeEslAction changeEslAction;
    private final ChangePackConfigAction changePackConfigAction;
    private final AddBuildAction addBuildAction;
    private final ChangeArmarcAction changeArmarcAction;
    private final ChangeAtssAction changeAtssAction;
    private final ChangeDiesAction changeDiesAction;
    private final ChangeComponentsAction changeComponentsAction;
    private final ChangeSymbolizationAction changeSymbolizationAction;
    private final AddFlowOperationAction addFlowOperationAction;
    private final RemoveFlowOperationAction removeFlowOperationAction;
    private final AddFlowComponentAction addFlowComponentAction;
    private final RemoveFlowComponentAction removeFlowComponentAction;
    private final ChangeSelectionAction changeSelectionAction;
    private final PgsSandboxService pgsSandboxService;
    private final ChangeTurnkeyAction changeTurnkeyAction;
    private final ChangeChangelinkChangeAction changeChangelinkChangeAction;
    private final ChangeChangelinkPcnAction changeChangelinkPcnAction;
    private final ChangeFlowOperationAction changeFlowOperationAction;
    private final ChangeFlowComponentAction changeFlowComponentAction;
    private final ChangePkgNicheAction changePkgNicheAction;
    private final CanApproveAction canApproveAction;
    private final CompareAtssAction compareAtssAction;
    private final ToggleOperationValidateAction toggleOperationValidateAction;
    private final CompareTravelerAction compareTravelerAction;
    private final DashBoardService dashBoardService;
    private final ChangeDryBakeAction changeDryBakeAction;
    private final FacilityService facilityService;
    private final DeviceService deviceService;
    private final RefreshPgsAction refreshPgsAction;
    private final RefreshBomTemplateAction refreshBomTemplateAction;
    private final RefreshFlowAction refreshFlowAction;
    private final RejectReasonsConfigService rejectReasonsConfigService;
    private final ReviewBomTemplateAction reviewBomTemplateAction;
    private final FillComponentVyperAction fillComponentVyperAction;
    private final FillComponentAtssAction fillComponentAtssAction;
    private final ComponentMapService componentMapService;
    private final VyperCopyBuildAction vyperCopyBuildAction;
    private final ChangeBackgrindAction changeBackgrindAction;
    private final FillComponentClearAction fillComponentClearAction;
    private final AddBuildCopyAction addBuildCopyAction;
    private final ChangeOperationCommentAction changeOperationCommentAction;
    private final AtssMaterialService atssMaterialService;
    private final AtssFacilityService atssFacilityService;
    private final ChangeWaferSawMethodAction changeWaferSawMethodAction;
    private final PackageNicheService packageNicheService;
    private final CreateVyperAndCopyBuildAction createVyperAndCopyBuildAction;
    private final ChildService childService;
    private final BuildService buildService;
    private final BuildEntityService buildEntityService;
    private final DownloadVyperExcelAction downloadVyperExcelAction;
    private final RestoreFlowOperationAction restoreFlowOperationAction;
    private final UpdateBuildAction updateBuildAction;
    private final PraBuildReportService praBuildReportService;
    private final ApproveDiagramAction approveDiagramAction;
    private final DeviceReportRepository deviceReportRepository;
    private final UserUtilsService userUtilsService;
    private final ReviewPkgNicheBomTemplateAction reviewPkgNicheBomTemplateAction;
    private final ListPackConfigAction listPackConfigAction;
    private final CheckArmarcAction checkArmarcAction;
    private final RefreshAttributesAction refreshAttributesAction;

    @PostMapping("/")
    public ResponseEntity<Vyper> create() {
        log.info("create()");

        // Validate if ext user
        userUtilsService.validateUser();

        VyperEntity vyperEntity = vyperEntityService.create();
        Vyper vyper = vyperEntityService.fromJson(vyperEntity.getJson());
        return ResponseEntity.ok(vyper);
    }

    @DeleteMapping("/{vyperNumber}")
    public ResponseEntity<Object> delete(@PathVariable String vyperNumber) {
        log.info("delete(vyperNumber:{})", vyperNumber);

        // Validate if ext user
        userUtilsService.validateUser();

        VyperEntity vyperEntity = vyperEntityService.findByVyperNumber(vyperNumber);
        if (null == vyperEntity) throw new VyperEntityNotFoundException(vyperNumber);
        if (!vyperEntityService.canDelete(vyperEntity)) {
            throw new CantDeleteVyperException(vyperNumber);
        }
        vyperEntityService.delete(vyperEntity.getId());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{vyperId}")
    public ResponseEntity<Vyper> read(
            @PathVariable Long vyperId
    ) {
        log.info("read(vyperId:{})", vyperId);

        // Validate if ext user
        userUtilsService.validateUser();

        VyperEntity vyperEntity = vyperEntityService.read(vyperId);
        Vyper vyper = vyperEntityService.fromJson(vyperEntity.getJson());
        return ResponseEntity.ok(vyper);
    }

    @PostMapping("/{vyperId}")
    public ResponseEntity<Vyper> update(
            @PathVariable Long vyperId,
            @Valid @RequestBody Vyper vyper
    ) {
        log.info("update(vyperId:{}", vyperId);
        // Validate if ext user
        userUtilsService.validateUser();

        VyperEntity vyperEntity = vyperEntityService.read(vyperId);
        vyperEntity.setJson(vyperEntityService.toJson(vyper));
        vyperEntity = vyperEntityService.update(vyperEntity);
        Vyper vyper2 = vyperEntityService.fromJson(vyperEntity.getJson());
        return ResponseEntity.ok(vyper2);
    }

    @GetMapping("/project/all")
    public ResponseEntity<Page<Project>> projectsAll(
            Pageable pageable,
            @RequestParam(name = "filter", required = false) List<String> filters
    ) {
        log.info("projectsAll(pageable:{}, filters:{})", pageable, filters);
        return ResponseEntity.ok(vyperEntityService.projectsAll(pageable, filters).getPage());
    }

    @GetMapping("/project/my")
    public ResponseEntity<Page<Project>> projectsMy(
            Pageable pageable,
            @RequestParam String userid,
            @RequestParam(name = "filter", required = false) List<String> filters
    ) {
        log.info("projectsMy(userid:{}, pageable:{}, filters:{})", userid, pageable, filters);
        return ResponseEntity.ok(vyperEntityService.projectsMy(userid, pageable, filters).getPage());
    }

    @GetMapping("/project/device")
    public ResponseEntity<Page<Device>> projectDevice(
            Pageable pageable,
            @RequestParam(name = "filter", required = false) List<String> filters
    ) {
        log.info("projectDevice(pageable:{}, filters:{})", pageable, filters);
        return ResponseEntity.ok(deviceService.search(pageable, filters).getPage());
    }

    @GetMapping("/project/findLastApprovedBuilds")
    public ResponseEntity<List<Device>> projectBuilds(
            @RequestParam(name = "xdays", required = false, defaultValue="10") int approvedInDays
    ) {
        log.info("projectBuilds(approvedInDays:{})", approvedInDays);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(deviceReportRepository.findLastApprovedBuilds(approvedInDays));
    }

    @GetMapping("/findByVyperNumber/{vyperNumber}")
    public ResponseEntity<Vyper> findByVyperNumber(
            @PathVariable String vyperNumber
    ) {
        log.info("findByVyperNumber(vyperNumber:{})", vyperNumber);
        VyperEntity vyperEntity = vyperEntityService.findByVyperNumber(vyperNumber);
        if (null == vyperEntity) throw new VyperEntityNotFoundException(vyperNumber);
        Vyper vyper = vyperEntityService.fromJson(vyperEntity.getJson());

        // Check user access by vyper #
        userUtilsService.checkUserAuthorizationForVyper(vyper);

        return ResponseEntity.ok(vyper);
    }

    @GetMapping("/findByBuildNumber/{buildNumber}")
    public ResponseEntity<Vyper> findByBuildNumber(
            @PathVariable String buildNumber
    ) {
        log.info("findByBuildNumber(buildNumber:{})", buildNumber);
        String vyperNumber = Build.convertBuildNumbertoVyperNumber(buildNumber);
        return findByVyperNumber(vyperNumber);
    }

    @PostMapping("/title")
    public ResponseEntity<Vyper> changeTitle(
            @Valid @RequestBody ChangeTitleForm changeTitleForm
    ) {
        log.info("changeTitle(changeTitleForm:{})", changeTitleForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeTitleAction.execute(changeTitleForm));
    }

    @PostMapping("/description")
    public ResponseEntity<Build> changeDescription(
            @Valid @RequestBody ChangeDescriptionForm changeDescriptionForm
    ) {
        log.info("changeDescription(changeDescriptionForm:{})", changeDescriptionForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeDescriptionAction.execute(changeDescriptionForm));
    }

    @PostMapping("/scswrcontrolnumber")
    public ResponseEntity<Build> changeScswrControlNumber(
            @Valid @RequestBody ChangeScswrControlNumberForm changeScswrControlNumberForm
    ) {
        log.info("changeScswrControlNumber(changeScswrControlNumberForm:{})", changeScswrControlNumberForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeScswrControlNumberAction.execute(changeScswrControlNumberForm));
    }

    @PostMapping("/buildtype")
    public ResponseEntity<Build> changeBuildtype(
            @Valid @RequestBody ChangeBuildtypeForm changeBuildtypeForm
    ) {
        log.info("changeBuildtype(changeBuildtypeForm:{})", changeBuildtypeForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeBuildtypeAction.execute(changeBuildtypeForm));
    }

    @PostMapping("/owner/add")
    public ResponseEntity<Vyper> ownerAdd(
            @Valid @RequestBody AddOwnersRequest addOwnersRequest
    ) {
        log.info("ownerAdd(vyperNumber:{}, ownersList:{})",
                addOwnersRequest.getVyperNumber(),
                addOwnersRequest.getOwnersList());
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(addOwnerAction.execute(addOwnersRequest));
    }

    @PostMapping("/owner/remove")
    public ResponseEntity<Vyper> ownerRemove(
            @Valid @RequestBody RemoveOwnerForm removeOwnerForm
    ) {
        log.info("ownerRemove(removeOwnerForm:{})", removeOwnerForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(removeOwnerAction.execute(removeOwnerForm));
    }

    @PostMapping("/addbuild")
    public ResponseEntity<Build> addBuild(
            @Valid @RequestBody AddBuildForm addBuildForm
    ) {
        log.info("addBuild(addBuildForm:{})", addBuildForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(addBuildAction.execute(addBuildForm));
    }

    @PostMapping("/addmffbuild")
    public ResponseEntity<List<Build>> addMffBuild(
            @Valid @RequestBody List<AddBuildForm> addBuildForms
    ) {
        log.info("addMffBuild(addBuildForms:{})", addBuildForms);
        // Validate if ext user
        userUtilsService.validateUser();
        final List<Build> createdBuilds =
                addBuildForms.stream()
                .map(addBuildAction::execute)
                .collect( Collectors.toList());
        return ResponseEntity.ok(createdBuilds);
    }

    @Data
    @ToString
    public static class PgsComponentsForm {
        @NotNull
        @Size(min = 1)
        private String buildNumber;

        @NotNull
        @Size(min = 1)
        private String material;

        @NotNull
        @Size(min = 1)
        private String plantCode;

        @NotNull
        @Size(min = 1)
        private List<String> operations;
    }

    @GetMapping("/pgscomponents")
    public ResponseEntity<PgsSandboxResult> pgsComponents(
            PgsComponentsForm pgsComponentsForm
    ) {
        log.debug("pgsComponents(pgsComponentsForm:{})", pgsComponentsForm);
        /* Validate user by facility code */
        userUtilsService.validateUserByFacility(pgsComponentsForm.getPlantCode());
        return ResponseEntity.ok(pgsSandboxService.execute(pgsComponentsForm));
    }

    @PostMapping("/material")
    public ResponseEntity<Build> material(
            @Valid ChangeMaterialForm changeMaterialForm
    ) {
        log.info("material(changeMaterialForm:{})", changeMaterialForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeMaterialAction.execute(changeMaterialForm));
    }

    @PostMapping("/facility")
    public ResponseEntity<Build> changeFacility(
            @Valid @RequestBody ChangeFacilityForm changeFacilityForm
    ) {
        log.info("changeFacility(changeFacilityForm:{})", changeFacilityForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeFacilityAction.execute(changeFacilityForm));
    }

    @PostMapping("/armarc")
    public ResponseEntity<Build> changeArmarc(
            @Valid @RequestBody ChangeArmArcForm changeArmArcForm
    ) {
        log.info("changeArmarc(armArcForm:{})", changeArmArcForm);
        return ResponseEntity.ok(changeArmarcAction.execute(changeArmArcForm));
    }

    @PostMapping("/dies")
    public ResponseEntity<Build> changeDies(
            @Valid @RequestBody ChangeDiesForm changeDiesForm
    ) {
        log.info("changeDies(changeDiesForm:{})", changeDiesForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeDiesAction.execute(changeDiesForm));
    }

    @GetMapping("/choices")
    public ResponseEntity<List<String>> componentChoices(
            ComponentChoiceForm componentChoiceForm
    ) {

        log.info("componentChoices(ComponentChoiceForm:{})", componentChoiceForm);
        return ResponseEntity.ok(componentService.componentChoices(componentChoiceForm));
    }

    @GetMapping("/atss/facilities")
    public ResponseEntity<List<String>> atssFacilities() {
        log.info("atssFacilities()");
        return ResponseEntity.ok(facilityService.list());
    }

    @GetMapping("/atss/plantcodes")
    public ResponseEntity<List<FacilityAndPlantCode>> atssPlantcodes() {
        log.info("atssPlantcodes()");
        return ResponseEntity.ok(atssFacilityService.plantcodes());
    }

    @PostMapping("/atss")
    public ResponseEntity<Build> changeAtss(
            @Valid @RequestBody ChangeAtssForm changeAtssForm
    ) {
        log.info("changeAtss(changeAtssForm:{})", changeAtssForm);
        // Validate if ext user
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeAtssAction.execute(changeAtssForm));
    }

    @GetMapping("/atss/materials")
    public ResponseEntity<List<String>> atssMaterials(
            @RequestParam(name = "facilityAt") @NotBlank String facilityAt,
            @RequestParam(name = "specDevice") @NotBlank String specDevice
    ) {
        log.info("atssMaterials(facilityAt: {}, specDevice: {})", facilityAt, specDevice);
        /* Validate user by facility code */
        userUtilsService.validateUserByFacility(facilityAt);
        return ResponseEntity.ok(atssMaterialService.findAllSpecDeviceByFacilityAndSpecDevice(facilityAt, specDevice));
    }

    @GetMapping("/atss/facility/lookup")
    public ResponseEntity<List<String>> atssFacilityLookup(
            @RequestParam(name = "device") @NotBlank String device
    ) {
        log.info("atssFacilityLookup(device:{})", device);
        return ResponseEntity.ok(atssFacilityService.autocompleteFacility(device));
    }

    @GetMapping("/atss/status/lookup")
    public ResponseEntity<List<String>> atssStatusLookup(
            @RequestParam(name = "specDevice") @NotBlank String specDevice,
            @RequestParam(name = "facility") @NotBlank String facility
    ) {
        log.info("atssFacilityLookup(specDevice:{}, facility:{})", specDevice,facility);
        return ResponseEntity.ok(atssFacilityService.fetchAtssStatus(specDevice,facility));
    }

    @GetMapping("/atss/multibuild")
    public ResponseEntity<Boolean> atssMultiBuild(
            @RequestParam String device,
            @RequestParam String facilityAt
    ) {
        log.info("atssMultiBuild(device:{}, facilityAt:{})", device, facilityAt);
        /* Validate user by facility code */
        userUtilsService.validateUserByFacility(facilityAt);
        return ResponseEntity.ok(atssMaterialService.isMultiBuild(device, facilityAt));
    }

    @GetMapping("/atss/multibuildspecs")
    public ResponseEntity<List<String>> atssMultiBuildSpecs(
            @RequestParam String device,
            @RequestParam String facilityAt
    ) {
        log.info("atssMultiBuildSpecs(device:{}, facilityAt:{})", device, facilityAt);
        /* Validate user by facility code */
        userUtilsService.validateUserByFacility(facilityAt);
        return ResponseEntity.ok(atssMaterialService.multiBuildSpecs(device, facilityAt));
    }

    /**
     * Returns the attributes that match the values
     *
     * @param name   the component name
     * @param values a list of component values
     * @return a list of maps of attributes
     */
    @GetMapping("/attributes")
    public ResponseEntity<List<Map<String, Object>>> attributes(
            @RequestParam String facilityAt,
            @RequestParam String name,
            @RequestParam List<String> values) {
        log.info("attributes(facilityAt:{}, name:{}, values:{})", facilityAt, name, values);

        /* Validate user by facility code */
        userUtilsService.validateUserByFacility(facilityAt);


        // name is the vyper-component name. convert to atss name

        ComponentMap componentMap = componentMapService.findByName(name);
        if (componentMap == null) {
            return ResponseEntity.ok(attributeService.findByName(facilityAt, name, values));
        } else {
            return ResponseEntity.ok(attributeService.findByName(facilityAt, componentMap.getAtssComponentName(), values));
        }
    }

    @GetMapping("/child")
    public ResponseEntity<Child> child(
            @RequestParam String facilityAt,
            @RequestParam String name,
            @RequestParam List<String> values) {
        log.info("child(facilityAt:{}, name:{}, values:{})", facilityAt, name, values);

        /* Validate user by facility code */
        userUtilsService.validateUserByFacility(facilityAt);


        ComponentMap componentMap = componentMapService.findByName(name);
        if (componentMap == null) {
            return ResponseEntity.ok(childService.findByName(facilityAt, name, values));
        } else {
            return ResponseEntity.ok(childService.findByName(facilityAt, componentMap.getAtssComponentName(), values));
        }
    }

    @PostMapping("/components")
    public ResponseEntity<Build> changeComponents(
            @Valid @RequestBody ChangeComponentsForm changeComponentsForm
    ) {
        // Validate if ext user
        log.info("changeComponents(changeComponentsForm:{})", changeComponentsForm);
        userUtilsService.validateUserByBuild(changeComponentsForm.getBuildNumber());
        return ResponseEntity.ok(changeComponentsAction.execute(changeComponentsForm));
    }

    @PostMapping("/symbolization")
    public ResponseEntity<Build> changeSymbolization(
            @Valid @RequestBody ChangeSymbolizationForm changeSymbolizationForm
    ) {
        log.info("changeSymbolization(changeSymbolizationForm:{})", changeSymbolizationForm);
        // Validate if ext user
        userUtilsService.validateUserByBuild(changeSymbolizationForm.getBuildNumber());

        //TODO: Verify if needed
        return ResponseEntity.ok(changeSymbolizationAction.execute(changeSymbolizationForm));
    }

    @PostMapping("/esl")
    public ResponseEntity<Build> esl(
            @Valid @RequestBody ChangeEslForm changeEslForm
    ) {
        log.info("esl(eslForm:{})", changeEslForm);
        userUtilsService.validateUserByBuild(changeEslForm.getBuildNumber());
        return ResponseEntity.ok(changeEslAction.execute(changeEslForm));
    }

    @PostMapping("/wafersawmethod")
    public ResponseEntity<Build> waferSawMethod(
            @Valid @RequestBody ChangeWaferSawMethodForm changeWaferSawMethod
    ) {
        log.info("waferSawMethod(changeWaferSawMethod:{})", changeWaferSawMethod);
        userUtilsService.validateUserByBuild(changeWaferSawMethod.getBuildNumber());
        return ResponseEntity.ok(changeWaferSawMethodAction.execute(changeWaferSawMethod));
    }

    @GetMapping("/packconfig")
    public ResponseEntity<List<String>> listPackConfigs(BuildNumberForm buildNumberForm
    ) {
        log.info("listPackConfigs()");
        userUtilsService.validateUserByBuild(buildNumberForm.getBuildNumber());
        return ResponseEntity.ok(listPackConfigAction.execute(buildNumberForm));
    }

    @PostMapping("/packconfig")
    public ResponseEntity<Build> packConfig(
            @Valid @RequestBody ChangePackConfigForm changePackConfigForm
    ) {
        log.info("packConfig(changePackConfigForm:{})", changePackConfigForm);
        userUtilsService.validateUserByBuild(changePackConfigForm.getBuildNumber());
         return ResponseEntity.ok(changePackConfigAction.execute(changePackConfigForm));
    }

    @PostMapping("/drybake")
    public ResponseEntity<Build> dryBake(
            @Valid @RequestBody ChangeDryBakeForm changeDryBakeForm
    ) {
        log.info("dryBake(changeDryBakeForm:{})", changeDryBakeForm);
        userUtilsService.validateUserByBuild(changeDryBakeForm.getBuildNumber());
        return ResponseEntity.ok(changeDryBakeAction.execute(changeDryBakeForm));
    }

    @PostMapping("/turnkey")
    public ResponseEntity<Build> turnkey(
            @Valid @RequestBody ChangeTurnkeyForm changeTurnkeyForm
    ) {
        log.info("turnkey(changeTurnkeyForm:{})", changeTurnkeyForm);
        userUtilsService.validateUserByBuild(changeTurnkeyForm.getBuildNumber());
        return ResponseEntity.ok(changeTurnkeyAction.execute(changeTurnkeyForm));
    }

    @PostMapping("/workflow")
    public ResponseEntity<Build> workflow(
            @Valid @RequestBody ChangeWorkflowForm changeWorkflowForm
    ) throws MessagingException {
        log.info("workflow(workflowForm:{})", changeWorkflowForm);
        userUtilsService.validateUserByBuild(changeWorkflowForm.getBuildNumber());
        //TODO: Check if workflow can work with external users ie. foundational service
        return ResponseEntity.ok(changeWorkflowAction.execute(changeWorkflowForm));
    }

    @PostMapping("/comment")
    public ResponseEntity<Build> comment(
            @Valid @RequestBody AddCommentForm addCommentForm
    ) throws MessagingException {
        log.info("comment(commentForm:{})", addCommentForm);
        userUtilsService.validateUserByBuild(addCommentForm.getBuildNumber());
        return ResponseEntity.ok(addCommentAction.execute(addCommentForm));
    }

    @GetMapping("/allOperations")
    public ResponseEntity<List<String>> allOperations() {
        log.info("allOperations()");
        return ResponseEntity.ok(operationRepository.allOperations());
    }

    @GetMapping("/allComponents")
    public ResponseEntity<List<String>> allComponents() {
        log.info("allComponents()");
        return ResponseEntity.ok(operationRepository.allComponents());
    }

    @PostMapping("/flow/operation")
    public ResponseEntity<Build> addOperation(
            @Valid @RequestBody AddFlowOperationForm addFlowOperationForm
    ) {
        log.info("addOperation(addFlowOperationForm:{})", addFlowOperationForm);
        userUtilsService.validateUserByBuild(addFlowOperationForm.getBuildNumber());
        return ResponseEntity.ok(addFlowOperationAction.execute(addFlowOperationForm));
    }

    @PostMapping("/flow/operation/change")
    public ResponseEntity<Build> changeOperation(
            @Valid @RequestBody ChangeFlowOperationForm changeFlowOperationForm
    ) {
        log.info("changeFlowOperationForm(changeFlowOperationForm:{})", changeFlowOperationForm);
        userUtilsService.validateUserByBuild(changeFlowOperationForm.getBuildNumber());
        return ResponseEntity.ok(changeFlowOperationAction.execute(changeFlowOperationForm));
    }

    @DeleteMapping("/flow/operation")
    public ResponseEntity<Build> removeOperation(
            @Valid @RequestBody RemoveFlowOperationForm removeFlowOperationForm
    ) {
        log.info("removeOperation(removeFlowOperationForm:{})", removeFlowOperationForm);
        userUtilsService.validateUserByBuild(removeFlowOperationForm.getBuildNumber());
        return ResponseEntity.ok(removeFlowOperationAction.execute(removeFlowOperationForm));
    }

    @PostMapping("/flow/operation/restore")
    public ResponseEntity<Build> restoreOperation(
            @Valid @RequestBody RestoreFlowOperationForm restoreFlowOperationForm
    ) {
        log.info("restoreOperation(restoreOperationForm:{})", restoreFlowOperationForm);
        userUtilsService.validateUserByBuild(restoreFlowOperationForm.getBuildNumber());
        return ResponseEntity.ok(restoreFlowOperationAction.execute(restoreFlowOperationForm));
    }

    @PostMapping("/flow/component")
    public ResponseEntity<Build> addComponent(
            @Valid @RequestBody AddFlowComponentForm addFlowComponentForm
    ) {
        log.info("addComponent(addFlowComponentForm:{})", addFlowComponentForm);
        userUtilsService.validateUserByBuild(addFlowComponentForm.getBuildNumber());
        return ResponseEntity.ok(addFlowComponentAction.execute(addFlowComponentForm));
    }

    @PostMapping("/flow/component/change")
    public ResponseEntity<Build> changeComponent(
            @Valid @RequestBody ChangeFlowComponentForm changeFlowComponentForm
    ) {
        log.info("changeComponent(changeFlowComponentForm:{})", changeFlowComponentForm);
        userUtilsService.validateUserByBuild(changeFlowComponentForm.getBuildNumber());
        return ResponseEntity.ok(changeFlowComponentAction.execute(changeFlowComponentForm));
    }

    @DeleteMapping("/flow/component")
    public ResponseEntity<Build> removeComponent(
            @Valid @RequestBody RemoveFlowComponentForm removeFlowComponentForm
    ) {
        log.info("removeComponent(removeComponentComponentForm:{})", removeFlowComponentForm);
        userUtilsService.validateUserByBuild(removeFlowComponentForm.getBuildNumber());
        return ResponseEntity.ok(removeFlowComponentAction.execute(removeFlowComponentForm));
    }

    @PostMapping("/selection")
    public ResponseEntity<Build> changeSelection(
            @Valid @RequestBody ChangeSelectionForm changeSelectionForm
    ) {
        log.info("selection(changeSelectionForm:{})", changeSelectionForm);
        userUtilsService.validateUserByBuild(changeSelectionForm.getBuildNumber());
        return ResponseEntity.ok(changeSelectionAction.execute(changeSelectionForm));
    }

    @PostMapping("/test/upload")
    public ResponseEntity<Build> testUpload(
            @Valid @RequestBody UploadTestForm uploadTestForm
    ) {
        log.info("testUpload(uploadTestForm:{})", uploadTestForm);
        userUtilsService.validateUserByBuild(uploadTestForm.getBuildNumber());
        return ResponseEntity.ok(uploadTestAction.execute(uploadTestForm));
    }

    @GetMapping("/test/download/{vyperNumber}/{buildNumber}")
    public void testDownload(
            @SuppressWarnings("unused") @PathVariable String vyperNumber,
            @PathVariable String buildNumber,
            HttpServletResponse response
    ) throws IOException {
        log.info("testDownload()");
        userUtilsService.validateUserByBuild(buildNumber);

        Build build = buildService.fetchBuild(buildNumber);
        String contentDisposition = "attachment; filename=\"" + buildNumber + " - test flow.txt\";";
        response.setHeader("Content-disposition", contentDisposition);
        response.setContentType("text/plain");

        response.getOutputStream().print(build.getTest().getContent());
    }

    @PostMapping("/changelink/change")
    public ResponseEntity<Build> changelinkChange(
            @Valid @RequestBody ChangeChangeLinkChangeForm changeChangeLinkChangeForm
    ) {
        log.info("changelinkChange(changeChangeLinkChangeForm:{})", changeChangeLinkChangeForm);
        userUtilsService.validateUserByBuild(changeChangeLinkChangeForm.getBuildNumber());
        return ResponseEntity.ok(changeChangelinkChangeAction.execute(changeChangeLinkChangeForm));
    }

    @PostMapping("/changelink/pcn")
    public ResponseEntity<Build> changelinkPcn(
            @Valid @RequestBody ChangeChangeLinkPcnForm changeChangeLinkPcnForm
    ) {
        log.info("changelinkPcn(changeChangeLinkPcnForm:{})", changeChangeLinkPcnForm);
        userUtilsService.validateUserByBuild(changeChangeLinkPcnForm.getBuildNumber());
        return ResponseEntity.ok(changeChangelinkPcnAction.execute(changeChangeLinkPcnForm));
    }

    @PostMapping("/pkgniche")
    public ResponseEntity<Build> pkgNiche(
            @Valid @RequestBody ChangePkgNicheForm changePkgNicheForm
    ) {
        log.info("pkgNiche(changePkgNicheForm:{})", changePkgNicheForm);
        userUtilsService.validateUserByBuild(changePkgNicheForm.getBuildNumber());
        return ResponseEntity.ok(changePkgNicheAction.execute(changePkgNicheForm));
    }

    @GetMapping("/canapprove")
    public ResponseEntity<CanApproveResult> canApprove(
            @Valid CanApproveForm canApproveForm
    ) {
        log.info("canApprove(canApproveForm:{})", canApproveForm);
        userUtilsService.validateUserByBuild(canApproveForm.getBuildNumber());
        return ResponseEntity.ok(canApproveAction.execute(canApproveForm));
    }

    @GetMapping("/findplantcodebyfacility")
    ResponseEntity<String> findPlantCodeByFacility(
            @RequestParam("facility") String facility
    ) {
        log.info("findPlantCodeByFacility(facility:{})", facility);
        userUtilsService.validateUserByFacility(facility);
        return ResponseEntity.ok(vyperEntityService.findPlantCodeByFacility(facility));
    }

    /**
     * This endpoint listens for task service approvals/reworks, and moves the build accordingly.
     *
     * @param taskJson String
     * @return ResponseEntity with a Vyper as the payload
     */
    @PostMapping("/atApproveCallBack")
    public ResponseEntity<Vyper> atApproveCallBack(@RequestBody(required = false) HashMap<String, Object> taskJson) {
        log.info("atApproveCallBack(json:{})", taskJson);

        String buildNumber = (String) taskJson.get("taskName");
        String taskState = (String) taskJson.get("stateName");

        userUtilsService.validateUserByBuild(buildNumber);

        return ResponseEntity.ok(changeWorkflowAction.atApproverCallBack(buildNumber, taskState));
    }

    @GetMapping("/compare/atss")
    ResponseEntity<CompareTravelerResult> compareAtss(
            CompareAtssForm compareAtssForm
    ) throws SOAPException, IOException, JDOMException {
        log.info("compareAtss(compareAtssForm:{})", compareAtssForm);
        userUtilsService.validateUserByBuild(compareAtssForm.getBuildNumber());
        userUtilsService.validateUserByFacility(compareAtssForm.getFacility());
        return ResponseEntity.ok(compareAtssAction.execute(compareAtssForm));
    }

    @GetMapping("/compare/traveler")
    ResponseEntity<CompareTravelerResult> compareTraveler(
            CompareTravelerForm compareTravelerForm
    ) throws SOAPException, IOException, JDOMException {
        log.info("compareTraveler(compareTravelerForm:{})", compareTravelerForm);
        userUtilsService.validateUserByBuild(compareTravelerForm.getBuildNumber1());
        userUtilsService.validateUserByBuild(compareTravelerForm.getBuildNumber2());

        return ResponseEntity.ok(compareTravelerAction.execute(compareTravelerForm));
    }

    @PostMapping("/operation/validate")
    public ResponseEntity<Build> operationValidate(
            @Valid @RequestBody ToggleOperationValidateForm toggleOperationValidateForm
    ) {
        log.info("operationValidate(operationValidateForm:{})", toggleOperationValidateForm);
        userUtilsService.validateUserByBuild(toggleOperationValidateForm.getBuildNumber());
        return ResponseEntity.ok(toggleOperationValidateAction.execute(toggleOperationValidateForm));
    }

    @GetMapping("/dashboard/view")
    public ResponseEntity<DashboardResult> dashboardView(
            @RequestParam("userid") String userid
    ) {
        log.info("dashboardView(userid:{})", userid);
        return ResponseEntity.ok(dashBoardService.fetch(userid));
    }

    @PostMapping("/refreshPgs")
    public ResponseEntity<Build> refreshPgs(
            @Valid @RequestBody RefreshPgsForm refreshPgsForm
    ) {
        log.info("refreshPgs(refreshPgsForm:{})", refreshPgsForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(refreshPgsAction.execute(refreshPgsForm));
    }

    @PostMapping("/refreshBomTemplate")
    public ResponseEntity<Build> refreshBomTemplate(
            @Valid @RequestBody RefreshBomTemplateForm refreshBomTemplateForm
    ) {
        log.info("refreshBomTemplate(refreshBomTemplateForm:{})", refreshBomTemplateForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(refreshBomTemplateAction.execute(refreshBomTemplateForm));
    }

    @PostMapping("/refreshFlow")
    public ResponseEntity<Build> refreshFlow(
            @Valid @RequestBody RefreshFlowForm refreshFlowForm
    ) {
        log.info("refreshFlow(refreshFlowForm:{})", refreshFlowForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(refreshFlowAction.execute(refreshFlowForm));
    }

    @GetMapping("/searchReasons")
    public ResponseEntity<List<String>> searchReasons(
            @RequestParam(name = "grp") @NotBlank String grp
    ) {
        return ResponseEntity.ok(rejectReasonsConfigService.searchReasons(grp));
    }

    @PostMapping("/bomtemplate/review")
    public ResponseEntity<Build> reviewBomTemplate(
            @Valid @RequestBody ReviewBomTemplateForm reviewBomTemplateForm
    ) throws IOException, MessagingException {
        log.info("reviewBomTemplate(reviewBomTemplateForm:{})", reviewBomTemplateForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(reviewBomTemplateAction.execute(reviewBomTemplateForm));
    }

    @PostMapping("/packageniche/bomtemplate/review")
    public ResponseEntity<Build> reviewPackageNicheBomTemplate(
            @Valid @RequestBody ReviewPkgNicheBomTemplateForm reviewPkgNicheBomTemplateForm
    ) throws IOException, MessagingException {
        log.info("reviewPackageNicheBomTemplate(ReviewBomTemplateForm:{})", reviewPkgNicheBomTemplateForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(reviewPkgNicheBomTemplateAction.execute(reviewPkgNicheBomTemplateForm));
    }

    @PostMapping("/fillcomponent/vyper")
    public ResponseEntity<Build> fillComponentVyper(
            @Valid @RequestBody FillComponentVyperForm fillComponentVyperForm
    ) {
        log.info("fillComponentVyper(fillComponentVyperForm:{})", fillComponentVyperForm);
        userUtilsService.validateUserByBuild(fillComponentVyperForm.getBuildNumber());
        return ResponseEntity.ok(fillComponentVyperAction.execute(fillComponentVyperForm));
    }

    @PostMapping("/fillcomponent/atss")
    public ResponseEntity<Build> fillComponentAtss(
            @Valid @RequestBody FillComponentAtssForm fillComponentAtssForm
    ) {
        log.info("fillComponentAtss(fillComponentAtssForm:{})", fillComponentAtssForm);
        userUtilsService.validateUserByFacility(fillComponentAtssForm.getFacility());
        userUtilsService.validateUserByBuild(fillComponentAtssForm.getBuildNumber());
        return ResponseEntity.ok(fillComponentAtssAction.execute(fillComponentAtssForm));
    }

    @PostMapping("/fillcomponent/clear")
    public ResponseEntity<Build> fillComponentClear(
            @Valid @RequestBody FillComponentClearForm fillComponentClearForm
    ) {
        log.info("fillComponentClear(fillComponentAtssForm:{})", fillComponentClearForm);
        userUtilsService.validateUserByBuild(fillComponentClearForm.getBuildNumber());
        return ResponseEntity.ok(fillComponentClearAction.execute(fillComponentClearForm));
    }

    @PostMapping("/vyperCopy")
    public ResponseEntity<Build> vyperCopyBuild(
            @Valid @RequestBody VyperCopyBuildForm vyperCopyBuildForm
    ) {
        log.info("vyperCopyBuild(vyperCopyBuildForm:{})", vyperCopyBuildForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(vyperCopyBuildAction.execute(vyperCopyBuildForm));
    }

    // this should return a Vyper, because it may create a new vyper in it's action
    @PostMapping("/addBuildCopy")
    public ResponseEntity<Build> vyperAddBuildCopy(
            @Valid @RequestBody AddBuildCopyForm addBuildCopyForm
    ) {
        log.info("vyperAddBuildCopy(addBuildCopyForm:{})", addBuildCopyForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(addBuildCopyAction.execute(addBuildCopyForm));
    }

    @GetMapping("/findAllDistinctFacilities")
    public ResponseEntity<List<String>> findAllDistinctFacility() {
        log.info("findAllDistinctFacility()");
        userUtilsService.validateUser();
        return ResponseEntity.ok(buildEntityService.findDistinctAllFacilities());
    }

    @GetMapping("/findAllDistinctMaterials")
    public ResponseEntity<List<String>> findAllDistinctMaterials() {
        log.info("findAllDistinctMaterials()");
        userUtilsService.validateUser();
        return ResponseEntity.ok(buildEntityService.findAllDistinctMaterials());
    }

    @GetMapping("/findAllDistinctMaterialsByFacilty")
    public ResponseEntity<List<String>> findAllDistinctMaterialsByFacility(
            @RequestParam(name = "facility") @NotBlank String facility) {
        log.info("findAllDistinctMaterialByFacility(facility: {})", facility);
        userUtilsService.validateUserByFacility(facility);
        return ResponseEntity.ok(buildEntityService.findAllDistinctMaterialByFacility(facility));
    }

    @GetMapping("/findAllDistinctFacilityByMaterial")
    public ResponseEntity<List<String>> findAllDistinctFacilityByMaterial(
            @RequestParam(name = "material") @NotBlank String material) {
        log.info("findAllDistinctFacilityByMaterial(material: {})", material);
        userUtilsService.validateUser();
        return ResponseEntity.ok(buildEntityService.findAllDistinctFacilityByMaterial(material));
    }

    @GetMapping("/findAllVyperBuildsByMaterialAndFacility")
    public ResponseEntity<List<String>> findAllVyperBuildsByMaterialAndFacility(
            @RequestParam(name = "facility") @NotBlank String facility,
            @RequestParam(name = "material") @NotBlank String material,
            @RequestParam(name = "flow")  String flow) {
        log.info("findAllVyperBuildsByMaterialAndFacility(facility: {}, material: {}, flow:{})", facility, material, flow);
        userUtilsService.validateUserByFacility(facility);
        return ResponseEntity.ok(buildEntityService.findAllVyperBuildsByMaterialAndFacility(facility, material, flow));
    }

    @GetMapping("/findAllVyperBuildsByComponentPartNumbers")
    public ResponseEntity<List<Map<String, Object>>> findAllVyperBuildsByComponentPartNumbers(
            @RequestParam(name = "facility") @NotBlank String facility,
            @RequestParam(name = "material") @NotBlank String material,
            @RequestParam(name = "wire_partnumber") @NotBlank String wire_partnumber,
            @RequestParam(name = "leadframe_partnumber") @NotBlank String leadframe_partnumber,
            @RequestParam(name = "mount_partnumber") @NotBlank String mount_partnumber,
            @RequestParam(name = "mold_partnumber") @NotBlank String mold_partnumber
    ) {
        log.info("findAllVyperBuildsByComponentPartNumbers(facility: {}, material: {}, wire Partnumber:{}, leadframe Partnumber:{}, Mount Compound:{}, Mold Compound:{})", facility, material, wire_partnumber, leadframe_partnumber, mount_partnumber, mold_partnumber);
        userUtilsService.validateUserByFacility(facility);
        return ResponseEntity.ok(vyperEntityService.findAllVyperBuildsByComponentPartNumbers(
                material,
                facility,
                wire_partnumber,
                leadframe_partnumber,
                mount_partnumber,
                mold_partnumber));
    }

    @GetMapping("/findAllVyperBuildsByMaterial")
    public ResponseEntity<List<Map<String, Object>>> findAllVyperBuildsByMaterial(
            @RequestParam(name = "facility") @NotBlank String facility,
            @RequestParam(name = "pins") @NotBlank String pins,
            @RequestParam(name = "pkg") @NotBlank String pkg,
            @RequestParam(name = "sbe") @NotBlank String sbe,
            @RequestParam(name = "build_flow") @NotBlank String build_flow){

        log.debug("findAllVyperBuildsByMaterial (facility: {}, pins: {}, pkg: {}, sbe: {})", facility,pins,pkg,sbe);
        userUtilsService.validateUserByFacility(facility);

        return  ResponseEntity.ok(vyperEntityService.findAllVyperBuildsByMaterial(facility,pins,pkg,sbe,build_flow));
    }

    @PostMapping("/backgrind")
    public ResponseEntity<Build> changeBackgrind(
            @Valid @RequestBody ChangeBackgrindForm changeBackgrindForm
    ) {
        log.info("changeBackgrind( changeBackgrindForm: {})", changeBackgrindForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeBackgrindAction.execute(changeBackgrindForm));
    }

    @PostMapping("/operation/comment")
    public ResponseEntity<Build> changeOperationComment(
            @Valid @RequestBody ChangeOperationCommentForm changeOperationCommentForm
    ) {
        log.info("changeOperationComment(changeOperationCommentForm: {})", changeOperationCommentForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(changeOperationCommentAction.execute(changeOperationCommentForm));
    }

    @GetMapping("/pgs/pkgniche")
    public ResponseEntity<Map<String, String>> findPgsPkgNiche(
            @RequestParam(name = "facility") @NotBlank String facility,
            @RequestParam(name = "device") @NotBlank String device
    ) {
        userUtilsService.validateUserByFacility(facility);
        log.info("findAllVyperBuildsByComponentPartNumbers(facility: {}, device: {})", facility, device);
        String plantCode = vyperEntityService.findPlantCodeByFacility(facility);
        Map<String, String> pkgNiche = packageNicheService.fetchDevicePkgNicheFromPgs(device, plantCode);
        log.debug("findAllVyperBuildsByComponentPartNumbers(pkgNiche: {})", pkgNiche);

        //pkgNiche = pkgNiche.replace(':',' ');
        return ResponseEntity.ok(pkgNiche);
    }

    // return Vyper, because we create a new vyper
    @PostMapping("/createVyperAndCopyBuild")
    public ResponseEntity<Vyper> createVyperAndCopyBuild(
            @Valid @RequestBody CreateVyperAndCopyBuildForm createVyperAndCopyBuildForm
    ) {
        log.info("createVyperAndCopyBuild({})", createVyperAndCopyBuildForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(createVyperAndCopyBuildAction.execute(createVyperAndCopyBuildForm));
    }

    // return a single build, by its build number
    @GetMapping("/findBuildByBuildNumber/{buildNumber}")
    public ResponseEntity<Build> findBuildByBuildNumber(
            @PathVariable String buildNumber
    ) {
        log.debug("findBuildByBuildNumber(buildNumber:{})", buildNumber);
        userUtilsService.validateUserByBuild(buildNumber);
        return ResponseEntity.ok(buildService.findByBuildNumber(buildNumber));
    }

    // return the builds for the project, by the vyper number
    @GetMapping("/findAllBuildsByVyperNumber/{vyperNumber}")
    public ResponseEntity<List<Build>> findAllBuildsByVyperNumber(
            @PathVariable String vyperNumber
    ) {
        log.debug("findAllBuildsByVyperNumber(vyperNumber:{})", vyperNumber);
        return ResponseEntity.ok(buildService.findAllBuildsByVyperNumber(vyperNumber));
    }

    @GetMapping("/downloadVyperExcel/{vyperNumber}")
    public void downloadVyperExcel(
            @PathVariable String vyperNumber,
            HttpServletResponse response
    ) throws IOException {
        log.debug("downloadVyperExcel(VyperNumber:{})", vyperNumber);

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        downloadVyperExcelAction.execute(vyperNumber, stream);

        String contentDisposition = "attachment; filename=\"" + vyperNumber + ".xlsx\";";
        response.setHeader("Content-disposition", contentDisposition);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        ServletOutputStream servletOut = response.getOutputStream();
        servletOut.write(stream.toByteArray());
    }

    @GetMapping("/build/state/{buildNumber}")
    public ResponseEntity<Map<String, String>> getBuildState(@PathVariable String buildNumber) {
        log.debug("getBuildState(buildNumber:{})", buildNumber);
        Build build = buildService.fetchBuild(buildNumber);
        userUtilsService.validateUserByBuild(buildNumber);
        Map<String, String> ret = new HashMap<>();
        ret.put("buildNumber", build.getBuildNumber());
        ret.put("state", build.getState().name());
        return ResponseEntity.ok(ret);
    }

    @PostMapping("/updateBuild")
    public ResponseEntity<Build> updateBuild(
            @Valid @RequestBody UpdateBuildForm updateBuildForm
    ) {
        log.info("updateBuild(updateBuildForm:{})", updateBuildForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(updateBuildAction.execute(updateBuildForm));
    }

    /** Allowed for all users */
    @PostMapping("/workflow/notifyAtGroupApproved")
    public ResponseEntity<Build> atGroupApproved(
            @Valid @RequestBody NotifyAtApprovalForm notifyAtApprovalForm
    ) throws MessagingException {
        log.info("/workflow/notifyAtGroupApproved(notifyAtApprovalForm:{})", notifyAtApprovalForm);
        return ResponseEntity.ok(changeWorkflowAction.notifyAtApproval(notifyAtApprovalForm));
    }

    @GetMapping("/project/praReport")
    public ResponseEntity<Page<PraBuildReportEntity>> praReport(
            Pageable pageable,
            @RequestParam(name = "filter", required = false) List<String> filters
    ) {
        log.info("praReport(pageable:{}, filters:{})", pageable, filters);
        return ResponseEntity.ok(praBuildReportService.search(pageable, filters).getPage());
    }

    @PostMapping("/diagram/approve")
    public ResponseEntity<Build> diagramApprove(
            @Valid @RequestBody DiagramApproveForm diagramApproveForm
    ) {
        log.info("diagramApprove(diagramApproveForm:{})", diagramApproveForm);
        userUtilsService.validateUserByBuild(diagramApproveForm.getBuildNumber());
        return ResponseEntity.ok(approveDiagramAction.execute(diagramApproveForm));
    }

    @PostMapping("/checkArmarc")
    public ResponseEntity<Build> checkArmarc(
            @Valid @RequestBody CheckArmarcForm checkArmarcForm
    ) {
        log.info("checkArmarc(checkArmarcForm:{})", checkArmarcForm);
        userUtilsService.validateUserByBuild(checkArmarcForm.getBuildNumber());
        return ResponseEntity.ok(checkArmarcAction.execute(checkArmarcForm));
    }

    @PostMapping("/refreshAttributes")
    public ResponseEntity<Build> refreshAttributes(
            @Valid @RequestBody RefreshAttributesForm refreshAttributesForm
    ) {
        log.info("refreshAttributes(refreshAttributesForm:{})", refreshAttributesForm);
        userUtilsService.validateUserByBuild(refreshAttributesForm.getBuildNumber());
        return ResponseEntity.ok(refreshAttributesAction.execute(refreshAttributesForm));
    }
}
