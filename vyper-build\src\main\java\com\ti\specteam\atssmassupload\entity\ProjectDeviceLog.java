package com.ti.specteam.atssmassupload.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "MU_PROJ_DEVICE_LOG")
@Getter
@Setter
public class ProjectDeviceLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    String id;

    @Column(columnDefinition = " raw(16)")
    String deviceId;

    @Column(nullable = false)
    Date logDate;
    @Lob()
    @Column(columnDefinition = "CLOB")
    String logMessage;

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
