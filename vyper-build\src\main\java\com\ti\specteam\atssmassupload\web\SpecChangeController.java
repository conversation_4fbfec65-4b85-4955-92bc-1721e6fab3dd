package com.ti.specteam.atssmassupload.web;

import com.ti.specteam.atssmassupload.domain.*;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceRepository;
import com.ti.specteam.atssmassupload.repository.ProjectEntityRepository;
import com.ti.specteam.atssmassupload.service.ProjectHeaderService;
import com.ti.specteam.atssmassupload.service.SpecChangeService;
import com.ti.specteam.atssmassupload.utility.SpecChangeUtility;
import com.ti.specteam.vyper.security.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/v1/atssmassupload/projects")
@CrossOrigin(origins = {"*"})
@Slf4j
public class SpecChangeController {

    @Autowired
    private SpecChangeService specChangeService;

    @Autowired
    private ProjectEntityRepository projectEntityRepository;

    @Autowired
    private ProjectDeviceRepository projectDeviceRepository;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private SpecChangeUtility specChangeUtility;

    @Autowired
    private ProjectHeaderService projectHeaderService;

    @GetMapping("/{projNumber}/specchanges")
    public ResponseEntity<List<SpecChangeDataSheetGrid>> specchanges(
            @PathVariable(value = "projNumber") String projNumber) {
        log.debug("specchanges(){} called ");
        ProjectEntity projectEntity = projectEntityRepository.findByProjNumber(projNumber);
        List<SpecChangeDataSheetGrid> specChangeGridRows = new ArrayList<SpecChangeDataSheetGrid>();
        if (projectEntity != null) {
            specChangeGridRows = specChangeService.findSpecChangesByProjectId(projectEntity.getId());
        } else {

            log.error("No Project Found for ProjNumber:" + projNumber);
        }
        return ResponseEntity.ok(specChangeGridRows);

    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/{projNumber}/savespecchanges")
    public ResponseEntity<List<SpecChangeDataSheetGrid>> savespecchanges(
            @PathVariable(value = "projNumber") String projNumber,
            @Valid @RequestBody List<SpecChangeDataSheetGrid> SpecChangeDataSheetGridRows) {
        log.debug("createspecchange(){} called");
        ProjectEntity projectEntity = projectEntityRepository.findByProjNumber(projNumber);
        String userId = securityService.user().getUserid();
        if (projectEntity != null) {
            String projId = projectEntity.getId();
            Map<ProjectDevice, List<SpecChange>> projectDeviceSpecChangesMap = specChangeUtility.gridRowToMap(projId,
                    userId, SpecChangeDataSheetGridRows);
            specChangeService.saveSpecChanges(projectEntity, projectDeviceSpecChangesMap);
        } else {
            log.error("No Project Found for ProjNumber:" + projNumber);
        }

        return ResponseEntity.ok(SpecChangeDataSheetGridRows);
    }

    @GetMapping("{projNumber}/refspec")
    public ResponseEntity<Optional<ReferenceSpecEntity>> refspec(
            @PathVariable(value = "projNumber") String projNumber) {
        log.debug("refspec(){} called ");
        projectHeaderService.validateUserByProjectNumber(projNumber);
        return ResponseEntity.ok(specChangeService.findReferenceSpecEntity(projNumber));

    }

    @GetMapping("/{projId}/devices")
    public ResponseEntity<List<ProjectDevice>> getProjectDevices(@PathVariable String projId) {
        log.debug("getProjectDevices {}", projId);
        projectHeaderService.validateUserByProjectId(projId);
        return ResponseEntity.ok(projectDeviceRepository.findProjectDeviceByProjectId(projId));

    }

}