<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.dashboard.repository.VswrSecurityDao">
    <resultMap id="LdapXRefRecord" type="com.ti.specteam.vswr.dashboard.domain.security.LdapXRefRecord">
        <result property="org" column="org" />
        <result property="sitecode" column="sitecode" />
        <result property="sitetype" column="sitetype" />
        <result property="etype" column="etype" />
        <result property="plantcode" column="plantcode" />
        <result property="sapcode" column="sapcode" />
        <result property="smsfacility" column="smsfacility" />
        <result property="signoff" column="signoff" />
        <result property="signoffEmail" column="signoffEmail" />
        <result property="sitelocal" column="sitelocal" />
    </resultMap>
    <resultMap id="VswrSecurityRecord" type="com.ti.specteam.vswr.dashboard.domain.security.VswrSecurityRecord">
        <result property="empid" column="empid" />
        <result property="sitecodeOvr" column="sitecode_ovr" />
        <result property="siteTypeOvr" column="site_type_ovr" />
        <result property="siteExt" column="site_ext" />
        <result property="sitelocation" column="sitelocation" />
        <result property="adminLevel" column="admin_level" />
    </resultMap>

    <select id="getVswrSecurityRecordByEmpId" resultMap="VswrSecurityRecord">
        select *
        from security
        where empid = #{empId}
    </select>

    <select id="getLdapXRefRecordByOrg" resultMap="LdapXRefRecord">
        select *
        from ldapxref
        where org = #{org}
    </select>

    <select id="doesAtSiteExist" resultType="boolean">
        select count(1) from list_box where at_site = #{atSite}
    </select>

    <select id="doesSbeExist" resultType="boolean">
        select count(1) from list_box where sbe1 = #{sbe}
    </select>
</mapper>