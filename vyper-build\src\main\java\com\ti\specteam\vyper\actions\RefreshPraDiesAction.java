package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.pra.PraDieVerifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.ti.specteam.vyper.security.user.UserUtilsService;

import java.util.Map;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_REFRESH_DIES;

@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshPraDiesAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final PraDieVerifier praDieVerifier;
    private final RefreshPraAction refreshPraAction;
    private final AuditService auditService;
    private final UserUtilsService userUtilsService;

    public Pra execute(PraNumberForm praNumberForm) {
        log.debug("execute(praNumberForm:{})", praNumberForm);

        // grab the objects
        Vyper vyper = vyperService.fetchVyper(praNumberForm);
        Pra pra = praService.fetchPra(praNumberForm);

        // validate current user can do this
        userUtilsService.validateUserByFacility(pra.getFacility().getPdbFacility());
        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, pra);

        execute(pra);

        // refresh the verifiers
        refreshPraAction.execute(vyper, pra);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_REFRESH_DIES,
                "refresh dies");

        return praService.savePra(pra);
    }

    @SuppressWarnings("CodeBlock2Expr")
    public void execute(Pra pra) {
        log.debug("execute(praNumber:{})", pra.getPraNumber());

        pra.getComponents().stream()
                .filter(component -> StringUtils.equalsIgnoreCase(component.getName(), "Die"))
                .forEach(component -> {
                    component.getInstances().forEach(componentInstance -> {
                        componentInstance.getPriorities().forEach(componentPriority -> {
                            Map<String, Object> attributes = praDieVerifier.fetchDieAttributes(componentPriority.getValue());
                            Object thick = componentPriority.getObject().get("Incoming Wafer Thick");
                            componentPriority.getObject().clear();
                            componentPriority.getObject().putAll(attributes);
                            componentPriority.getObject().put("Incoming Wafer Thick", thick);
                        });
                    });
                });

    }

}
