package com.ti.specteam.atssmassupload.repository;

import com.ti.specteam.vyper.atss.traveler.Status;
import com.ti.specteam.vyper.atss.traveler.TravelerForm;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class ProjectDeviceTravelerRepository {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public List<TravelerForm> getProjectDeviceAtssSpecHeaders(String projId, String facility, Status status){
        String sql = "SELECT spec_device, :facility facility_at, :status status from MU_PROJ_DEVICE where proj_id = :projId ";
        return jdbcTemplate.query(
                sql,
                new MapSqlParameterSource()
                        .addValue("projId", projId)
                        .addValue("facility", facility)
                        .addValue("status", status.toString()),
                BeanPropertyRowMapper.newInstance(TravelerForm.class));
    };

}
