package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.bdw.PlantDetails;
import com.ti.specteam.vyper.bdw.BdwService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperFacilityNotFoundException;
import com.ti.specteam.vyper.performance.Performance;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.template.TemplateType;
import com.ti.specteam.vyper.topsymbol.TopSymbolLoader;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_FACILITY;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeFacilityAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final PgsLoader pgsLoader;
    private final PackageNicheLoader packageNicheLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final TemplateLoader templateLoader;
    private final FlowLoader flowLoader;
    private final AtssLoader atssLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final SecurityService securityService;
    private final ValidateOperationLoader validateOperationLoader;
    private final TopSymbolLoader topSymbolLoader;
    private final CustLoader custLoader;
    private final AuditService auditService;
    private final EcatLoader ecatLoader;
    private final DryBakeLoader dryBakeLoader;
    private final PackLoader packLoader;
    private final BdwService bdwService;
    private final FluxLoader fluxLoader;

    public Build execute(ChangeFacilityForm changeFacilityForm) {
        log.debug("execute(changeFacilityForm:{})", changeFacilityForm);

        Vyper vyper = vyperService.fetchVyper(changeFacilityForm);
        Build build = buildService.fetchBuild(changeFacilityForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        execute(vyper, build, changeFacilityForm);

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_FACILITY,
                "changed facility to: " + changeFacilityForm.getFacility()
        );

        return buildService.saveBuild(build);
    }

    public Vyper execute(Vyper vyper, Build build, ChangeFacilityForm changeFacilityForm) {
        return execute(vyper, build, changeFacilityForm.getFacility());
    }

    public Vyper execute(Vyper vyper, Build build, String facilityAt) {

        Map<String, Object> facility = build.getMaterial().getFacilities().stream()
                .filter(f -> Objects.equals(facilityAt, f.get("PDBFacility")))
                .findFirst()
                .orElse(null);

        if (facility == null) {
            PlantDetails plantDetails = bdwService.fetchPlantDetails(facilityAt);
            Map<String, Object> missingFacility = new HashMap<>();
            missingFacility.put("PDBFacility", plantDetails.getPdbFacility());
            missingFacility.put("PlantCode", plantDetails.getPlantCode());
            missingFacility.put("PlantLoc", plantDetails.getPlantLoc());
            missingFacility.put("PlantName", plantDetails.getPlantName());
            missingFacility.put("PlantType", plantDetails.getPlantType());
            facility = new HashMap<>(missingFacility);
        }

        build.resetFacility();

        if (build.getBuildFlow().getFlowName() != null && (build.getBuildFlow().getFlowName().toUpperCase().contains("TEST") || build.getBuildFlow().getFlowName().equalsIgnoreCase("TKY"))) {
            build.getTurnkey().setValue("TKY");
        }

        build.getFacility().getObject().putAll(facility);
        build.getFacility().getSource().appointUser(securityService.user());

        Performance performance = Performance.start("ChangeFacilityAction", "pgsLoader.load()");
        pgsLoader.load(vyper, build);

        performance.next("dryBakeLoader.load()");
        dryBakeLoader.load(vyper, build);

        performance.next("topSymbolLoader.load()");
        topSymbolLoader.load(vyper, build);

        performance.next("fluxLoader.load()");
        fluxLoader.load(vyper, build);

        performance.next("packageNicheLoader.load()");
        packageNicheLoader.load(vyper, build);

        performance.next("bomTemplateLoader.load()");
        templateLoader.load(vyper, build);

        performance.next("atssLoader.load()");
        atssLoader.loadAttributes(vyper, build);

        performance.next("ecatLoader.load()");
        ecatLoader.load(vyper, build);

        performance.next("custLoader.load()");
        custLoader.load(vyper, build);

        performance.next("packLoader.load()");
        packLoader.load(vyper, build);

        performance.next("flowLoader.load()");
        flowLoader.load(vyper, build);

        performance.next("requiredComponentLoader.load()");
        requiredComponentLoader.load(vyper, build);

        performance.next("selectionLoader.load()");
        selectionLoader.load(vyper, build);

        performance.next("travelerRefreshService.load()");
        travelerRefreshService.load(vyper, build);

        performance.next("validateOperationLoader.load()");
        validateOperationLoader.load(vyper, build);

        performance.done();

        build.getFillComponent().reset();

        return vyper;
    }

}
