package com.ti.specteam.vyper.vscn.model;

import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.verifier.VerifierService;
import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import com.ti.specteam.vyper.vscn.entity.VscnEntity;
import com.ti.specteam.vyper.vscn.entity.VscnEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class VscnService {

    private final VscnEntityService vscnEntityService;
    private final VerifierService verifierService;

    /**
     * Create a new VSCN.
     *
     * @param vyper {@link Vyper}
     * @param build {@link Build}
     * @param pra   {@link Pra}
     * @return {@link Vscn}
     */
    public Vscn create(Vyper vyper, Build build, Pra pra) {

        String vscnNumber = Vscn.generateVscnNumber(vyper.getVyperNumber(), getNextId(vyper.getVyperNumber()));

        List<TravelerOperation> modifiedTravelerOperationList = build.getTraveler().getOperations().stream()
                .filter(travelerOperation -> isPack(travelerOperation))
                .collect(Collectors.toList());

        Traveler modifiedTraveler = new Traveler();
        modifiedTraveler.getOperations().addAll(modifiedTravelerOperationList);

        Vscn vscn = Vscn.builder()
                .version(0)
                .vscnNumber(vscnNumber)
                .praNumber(pra.getPraNumber())
                .buildNumber(build.getBuildNumber())
                .vyperNumber(vyper.getVyperNumber())
                .state(VscnState.VSCN_DRAFT)
                .facility(pra.getFacility())
                .material(pra.getMaterial())
                .components(pra.getComponents())
                .validatedComponents(pra.getValidatedComponents())
                .symbolization(build.getSymbolization())
                .test(build.getTest())
                .packConfig(build.getPackConfig())
                .verifiers(pra.getVerifiers())
                .diagramApprovals(pra.getDiagramApprovals())
                .traveler(modifiedTraveler)
                .build();

        addSymbolComponents(build, vscn);

        // initialize the verifiers
        verifierService.initializeVerifiers(vscn);

        VscnEntity vscnEntity = new VscnEntity();
        vscnEntity.setJson(vscnEntityService.toJson(vscn));
        vscnEntity.setVscnNumber(vscn.getVscnNumber());
        vscnEntity.setPraNumber(vscn.getPraNumber());
        vscnEntity.setBuildNumber(vscn.getBuildNumber());
        vscnEntity.setVyperNumber(vscn.getVyperNumber());

        vscnEntityService.create(vscnEntity);

        return vscn;
    }

    public boolean isPack(TravelerOperation travelerOperation) {
        return StringUtils.equalsIgnoreCase(travelerOperation.getSubflowType(), "PACK");
    }

    /**
     * Return the next available suffix for the vscn.
     *
     * @param vyperNumber The parent vyper number
     * @return The available suffix
     */
    public long getNextId(String vyperNumber) {
        // pull the last Pra Number
        VscnEntity last = vscnEntityService.findFirstByVyperNumberOrderByVscnNumberDesc(vyperNumber);

        // if null then just create the id of 1
        if (last == null) {
            return 1;
        }

        // parse the number out, and add 1, then return it
        Vscn vscn = vscnEntityService.fromJson(last.getJson());
        return vscn.getSuffix() + 1;
    }

    public Vscn findByVscnNumber(String vscnNumber) {
        log.debug("findByVscnNumber(vscnNumber:{})", vscnNumber);
        VscnEntity vscnEntity = vscnEntityService.findByVscnNumber(vscnNumber);
        return vscnEntity == null ? null : vscnEntityService.fromJson(vscnEntity.getJson());
    }

    public Vscn fetchVscn(String vscnNumber) {
        log.debug("fetch(vscnNumber:{})", vscnNumber);
        VscnEntity vscnEntity = vscnEntityService.fetch(vscnNumber);
        return vscnEntityService.fromJson(vscnEntity.getJson());
    }

    public List<Vscn> findAllByPraNumber(String praNumber) {
        log.debug("findAllByPraNumber(praNumber:{})", praNumber);
        return vscnEntityService.findAllByPraNumber(praNumber).stream()
                .map(vscnEntity -> vscnEntityService.fromJson(vscnEntity.getJson()))
                .collect(Collectors.toList());
    }

    public List<Vscn> findAllByBuildNumber(String buildNumber) {
        log.debug("findAllByBuildNumber(buildNumber:{})", buildNumber);
        return vscnEntityService.findAllByBuildNumber(buildNumber).stream()
                .map(vscnEntity -> vscnEntityService.fromJson(vscnEntity.getJson()))
                .collect(Collectors.toList());
    }

    public List<Vscn> findAllByVyperNumber(String vyperNumber) {
        log.debug("findAllByVyperNumber(vyperNumber:{})", vyperNumber);
        return vscnEntityService.findAllByVyperNumber(vyperNumber).stream()
                .map(vscnEntity -> vscnEntityService.fromJson(vscnEntity.getJson()))
                .collect(Collectors.toList());
    }

    public Vscn findByVscnNumber(VscnNumberForm vscnNumberForm) {
        return findByVscnNumber(vscnNumberForm.getVscnNumber());
    }

    public Vscn saveVscn(Vscn vscn) {
        log.info("saveVscn(vscnNumber:{})", vscn.getVscnNumber());
        VscnEntity vscnEntity = vscnEntityService.fetch(vscn.getVscnNumber());
        vscnEntity.setJson(vscnEntityService.toJson(vscn));
        vscnEntityService.update(vscnEntity);
        return vscn;
    }

    public void deleteVscn(Vscn vscn) {
        log.info("deleteVscn {}", vscn);
        if (vscn.getState().equals(VscnState.VSCN_DRAFT)) {
            VscnEntity vscnEntity = vscnEntityService.fetch(vscn.getVscnNumber());
            vscnEntityService.deleteByVscnId(vscnEntity.getId());
        }
    }

    public void addSymbolComponents(Build build, Vscn vscn) {

        List<String> symbolComponents = new ArrayList<>(List.of("Topside Symbol", "ECAT", "CUST1", "CUST2", "CUST3", "CUST4", "CUST5", "CUST6", "CUST7", "CUST8", "CUST9"));

        symbolComponents.stream().forEach(symbolComponent -> {
            // find the selection
            List<Selection> selections = build.findAllSelectionsByComponentName(symbolComponent);

            //noinspection StatementWithEmptyBody
            if (selections.isEmpty()) {
                // do nothing - assuming this traveler doesn't use the component

            } else {
                // get the first operation's selection
                Selection selection = selections.get(0);

                // create the component and instance
                Component component = vscn.findOrCreateComponent(selection.getName(), selection.getSource());
                ComponentInstance ci = component.createInstance();

                // loop through the selection items
                selection.getItems().forEach(selectionItem -> {

                    // add the item's value to the component
                    ComponentPriority priority = ci.addPriority("name", selectionItem.getValue(), selectionItem.getEngineering());
                    priority.getSource().set(selectionItem.getSource());

                    // get the traveler attributes, and add them to the priority
                    build.getTraveler().getAttributesByNameAndValue(component, selectionItem.getValue())
                            .forEach(ta -> priority.getObject().put(ta.getName(), ta.getValue()));
                });
            }
        });

    }

}
