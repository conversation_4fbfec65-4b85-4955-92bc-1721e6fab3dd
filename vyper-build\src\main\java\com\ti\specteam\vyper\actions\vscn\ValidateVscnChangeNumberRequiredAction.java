package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.atss.device.AtssMaterialRepository;
import com.ti.specteam.vyper.atss.traveler.AtssRepository;
import com.ti.specteam.vyper.atss.traveler.ComponentAndAttribute;
import com.ti.specteam.vyper.bdw.BdwRepository;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ValidateVscnChangeNumberRequiredAction {
    private final AtssMaterialRepository atssMaterialRepository;
    private final AtssRepository atssRepository;
    private final BdwRepository bdwRepository;
    private final VscnService vscnService;
    private final List<String> custList = Arrays.asList("CUST1", "CUST2", "CUST3", "CUST4", "CUST5", "CUST6", "CUST7", "CUST8", "CUST9");

    public boolean execute(VscnNumberForm vscnNumberForm) {
        Vscn vscn = vscnService.fetchVscn(vscnNumberForm.getVscnNumber());
        return execute(vscn);
    }

    public boolean execute(Vscn vscn) {

        boolean specExistsInAtss = false;
        boolean specExistsInAtssForSameFacility = false;
        boolean bomDieChange = true;
        boolean bomDieChangeSameFamily = true;

        String sapMaterial = vscn.getMaterial().getMaterial();
        String oldMaterial = vscn.getMaterial().getOldMaterial();
        String facility = vscn.getFacility().getPdbFacility();

        specExistsInAtss = atssMaterialRepository.detectAtssSpec(oldMaterial, sapMaterial);

        if (specExistsInAtss) {
            specExistsInAtssForSameFacility = atssMaterialRepository.detectAtssSpecAtFacility(oldMaterial, sapMaterial, facility);
        }

        // code to check does spec device in atss extended to new facility
        if (specExistsInAtss && !specExistsInAtssForSameFacility) {
            return true;
        }

        if (specExistsInAtssForSameFacility) {
            List<ComponentAndAttribute> productionComponentsAndAttributesList = atssRepository.getProductionComponentsAndAttributes(oldMaterial, sapMaterial, facility);

            if (detectBomDieMajorRevisionChange(vscn, productionComponentsAndAttributesList)) {
                return true;
            }

            bomDieChange = detectComponentChange(vscn, productionComponentsAndAttributesList, "Die", false);
            bomDieChangeSameFamily = detectBomDieChangeSameFamily(vscn, productionComponentsAndAttributesList);

            if (bomDieChange && !bomDieChangeSameFamily) {
                return true;
            }

            if (detectComponentChange(vscn, productionComponentsAndAttributesList, "Topside Symbol", false)) {
                return true;
            }

            if (detectComponentChange(vscn, productionComponentsAndAttributesList, "Leadframe", false)) {
                return true;
            }

            if (detectComponentChange(vscn, productionComponentsAndAttributesList, "MB Diagram", false)) {
                return true;
            }

            if (detectComponentChange(vscn, productionComponentsAndAttributesList, "Mount Compound", true)) {
                return true;
            }

            if (detectComponentChange(vscn, productionComponentsAndAttributesList, "Wire", true)) {
                return true;
            }

            if (detectComponentChange(vscn, productionComponentsAndAttributesList, "Mold Compound", true)) {
                return true;
            }

            // code to check if cust value is changed for existing device
            for (String custName : custList) {
                if (vscn.componentExists(custName)) {
                    if (detectComponentChange(vscn, productionComponentsAndAttributesList, custName, false)) {
                        return true;
                    }
                }
            }

        }

        return false;
    }

    public boolean detectComponentChange(Vscn vscn, List<ComponentAndAttribute> productionComponentsAndAttributesList, String componentName, boolean checkOnly7PartNumber) {
        Component vscnComponent = vscn.findComponentByName(componentName);

        if(vscnComponent.getInstances().isEmpty()){
             return false;
        }

        List<ComponentPriority> vscnComponentPriorities = vscnComponent.getPriorities(0);

        if (!productionComponentsAndAttributesList.isEmpty()) {
            List<ComponentAndAttribute> componentsAndAttributes = productionComponentsAndAttributesList.stream()
                    .filter(componentAndAttribute -> StringUtils.equalsIgnoreCase(componentAndAttribute.getComponentName(), componentName))
                    .sorted((a, b) -> StringUtils.compare(a.getOccurrence(), b.getOccurrence()))
                    .collect(Collectors.toList());

            if (componentsAndAttributes.size() != vscnComponentPriorities.size() && !StringUtils.equalsIgnoreCase(componentName, "MB Diagram")) {
                return true;
            }

            // get the first operation's selection
            for (ComponentPriority vscnComponentPriority : vscnComponentPriorities) {
                ComponentAndAttribute matchingComponent = componentsAndAttributes.stream()
                        .filter(componentAndAttribute -> {
                            if (checkOnly7PartNumber) {
                                return StringUtils.equalsIgnoreCase(vscnComponentPriority.getValue().substring(0, 6), componentAndAttribute.getComponentValue().substring(0, 6));
                            }
                            return StringUtils.equalsIgnoreCase(vscnComponentPriority.getValue(), componentAndAttribute.getComponentValue());
                        })
                        .findFirst()
                        .orElse(null);

                if (matchingComponent == null) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean detectBomDieMajorRevisionChange(Vscn vscn, List<ComponentAndAttribute> productionComponentsAndAttributesList) {
        Component vscnDieComponent = vscn.findComponentByName("Die");
        List<ComponentPriority> vscnDiePriorities = vscnDieComponent.getPriorities(0);

        if (!productionComponentsAndAttributesList.isEmpty()) {
            List<ComponentAndAttribute> componentsAndAttributes = productionComponentsAndAttributesList.stream()
                    .filter(componentAndAttribute -> StringUtils.equalsIgnoreCase(componentAndAttribute.getComponentName(), "Die"))
                    .sorted((a, b) -> StringUtils.compare(a.getOccurrence(), b.getOccurrence()))
                    .collect(Collectors.toList());

            // get the first operation's selection
            for (ComponentPriority vscnComponentPriority : vscnDiePriorities) {
                ComponentAndAttribute matchingComponent = componentsAndAttributes.stream()
                        .filter(componentAndAttribute -> StringUtils.equalsIgnoreCase(vscnComponentPriority.getValue(), componentAndAttribute.getComponentValue()))
                        .findFirst()
                        .orElse(null);

                if (matchingComponent != null && !StringUtils.equalsIgnoreCase(vscnComponentPriority.getKeyValue("Die Revision Major"), matchingComponent.getAttributeValue())) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean detectBomDieChangeSameFamily(Vscn vscn, List<ComponentAndAttribute> productionComponentsAndAttributesList) {
        Component vscnDieComponent = vscn.findComponentByName("Die");
        List<ComponentPriority> vscnDiePriorities = vscnDieComponent.getPriorities(0);

        if (!productionComponentsAndAttributesList.isEmpty()) {
            List<String> productionDies = productionComponentsAndAttributesList.stream()
                    .filter(componentAndAttribute -> StringUtils.equalsIgnoreCase(componentAndAttribute.getComponentName(), "Die"))
                    .map(componentAndAttribute -> componentAndAttribute.getComponentValue())
                    .collect(Collectors.toList());

            if (!productionDies.isEmpty()) {
                for (ComponentPriority vscnDiePriority : vscnDiePriorities) {
                    if (!productionDies.contains(vscnDiePriority.getValue())) {
                        if (!bdwRepository.satisfyParentChildDieRelationship(productionDies, vscnDiePriority.getValue())) {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

}
