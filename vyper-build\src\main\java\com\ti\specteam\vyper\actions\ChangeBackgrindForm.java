package com.ti.specteam.vyper.actions;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ti.specteam.vyper.build.model.SelectionItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeBackgrindForm extends BuildNumberForm {

    private List<SelectionItem> backgrindSelected;

    @Size(min=1)
    @JsonProperty("backgrindVal")
    private String backgrindOption;

    public String display(){
        if(backgrindSelected!= null){
            return backgrindOption+" "+backgrindSelected.stream().map((c) -> c.getValue()).toString();
        }
        return backgrindOption;
    }
}
