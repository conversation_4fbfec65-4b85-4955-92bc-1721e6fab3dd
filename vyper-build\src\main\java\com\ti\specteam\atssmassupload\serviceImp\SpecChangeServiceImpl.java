package com.ti.specteam.atssmassupload.serviceImp;

import com.ti.specteam.atssmassupload.domain.*;
import com.ti.specteam.atssmassupload.repository.*;
import com.ti.specteam.atssmassupload.service.SpecChangeService;
import com.ti.specteam.atssmassupload.utility.UISequenceComparator;
import com.ti.specteam.vyper.security.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SpecChangeServiceImpl implements SpecChangeService {

    @Autowired
    private ProjectDeviceRepository projectDeviceRepository;

    @Autowired
    private SpecChangeTestProgramRepository specChangeTestProgramRepository;

    @Autowired
    private ProjectEntityRepository projectEntityRepository;

    @Autowired
    private ReferenceSpecEntityRepository referenceSpecEntityRepository;

    @Autowired
    private SpecChangePlanFactorRepository specChangeMaterialFacilityRepository;

    @Autowired
    private SpecChangeRuleRepository specChangeRuleRepository;

    @Autowired
    private SpecChangeRepository specChangeRepository;

    @Autowired
    private SecurityService securityService;

    @Transactional
    @Override
    public void saveSpecChanges(ProjectEntity projectEntity,
                                Map<ProjectDevice, List<SpecChange>> projectDeviceSpecChangesMap) {
        log.debug("saveSpecChanges(){}");
        Set<ProjectDevice> projectDevices = projectDeviceSpecChangesMap.keySet();
        String projId = projectEntity.getId();

		if (projectDeviceSpecChangesMap.keySet().contains(null) && projectDeviceSpecChangesMap.size()==1) {
			projectDeviceRepository.deleteProjDeviceByProjId(projId);
		}
		else {
        for (ProjectDevice projectDevice : projectDevices) {
            List<SpecChange> specChangeList = (List<SpecChange>) projectDeviceSpecChangesMap.get(projectDevice);

            deleteSpecChanges(projectDeviceSpecChangesMap, projectDevice);

            for (SpecChange specChange : specChangeList)
                specChange.setProjectDevice(projectDevice);

            projectDevice.getSpecChanges().clear();
            projectDevice.getSpecChanges().addAll(specChangeList);

            projectDeviceRepository.save(projectDevice);

        }
		}

    }

    @Override
    public List<SpecChangeDataSheetGrid> findSpecChangesByProjectId(String projId) {
        log.debug("findSpecChangesByProjectId(){}");
        List<ProjectDevice> projectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projId);
        List<SpecChange> specChanges = new ArrayList<SpecChange>();
        List<SpecChangeDataSheetGrid> specChangeDataSheetGridRows = new ArrayList<SpecChangeDataSheetGrid>();

        if (projectDevices.size() != 0) {

            for (int index = 0; index < projectDevices.size(); index++) {

                specChanges.addAll(projectDevices.get(index).getSpecChanges());

            }

            Collections.sort(specChanges, new UISequenceComparator());
            StringBuffer currentMaterial = new StringBuffer();
            for (int index = 0; index < specChanges.size(); index++) {
                SpecChangeDataSheetGrid specChangeDataSheetGridRow = new SpecChangeDataSheetGrid();
                specChangeDataSheetGridRow.setChangeId(specChanges.get(index).getId());
                specChangeDataSheetGridRow.setFlowType(specChanges.get(index).getFlowType());
                specChangeDataSheetGridRow.setComponentName(specChanges.get(index).getComponentName());
                specChangeDataSheetGridRow.setComponentValue(specChanges.get(index).getComponentValue());
                specChangeDataSheetGridRow.setAttributeName(specChanges.get(index).getAttributeName());
                specChangeDataSheetGridRow.setAttributeValue(specChanges.get(index).getAttributeValue());
                specChangeDataSheetGridRow.setOperationName(specChanges.get(index).getOperationName());
                specChangeDataSheetGridRow.setComponentOccurrance(specChanges.get(index).getComponentOccurrence());
                specChangeDataSheetGridRow.setValidationStatus(specChanges.get(index).getStatus());
                Optional<ProjectDevice> projectDeviceObj = projectDeviceRepository
                        .findById(specChanges.get(index).getProjectDeviceID());

                if (currentMaterial.length() == 0) {
                    currentMaterial = currentMaterial.append(projectDeviceObj.get().getMaterial());
                    specChangeDataSheetGridRow.setDeviceId(projectDeviceObj.get().getId());
                    specChangeDataSheetGridRow.setMaterial(projectDeviceObj.get().getMaterial());
                    specChangeDataSheetGridRow.setOldMaterial(projectDeviceObj.get().getOldMaterial());
                    specChangeDataSheetGridRow.setSpecDevice(projectDeviceObj.get().getSpecDevice());
                    specChangeDataSheetGridRow.setScnId(projectDeviceObj.get().getScnId());

                } else {

                    if (currentMaterial.toString().equals(projectDeviceObj.get().getMaterial())) {
                        specChangeDataSheetGridRow.setDeviceId(projectDeviceObj.get().getId());
                        specChangeDataSheetGridRow.setMaterial("");
                        specChangeDataSheetGridRow.setOldMaterial("");
                        specChangeDataSheetGridRow.setSpecDevice("");
                        specChangeDataSheetGridRow.setScnId(projectDeviceObj.get().getScnId());
                    } else {

                        currentMaterial.setLength(0);
                        currentMaterial = currentMaterial.append(projectDeviceObj.get().getMaterial());
                        specChangeDataSheetGridRow.setDeviceId(projectDeviceObj.get().getId());
                        specChangeDataSheetGridRow.setMaterial(projectDeviceObj.get().getMaterial());
                        specChangeDataSheetGridRow.setOldMaterial(projectDeviceObj.get().getOldMaterial());
                        specChangeDataSheetGridRow.setSpecDevice(projectDeviceObj.get().getSpecDevice());
                        specChangeDataSheetGridRow.setScnId(projectDeviceObj.get().getScnId());

                    }
                }
                specChangeDataSheetGridRows.add(specChangeDataSheetGridRow);

            }

        } else {
            log.debug("Not Found Projed Device Information for Project Id:" + projId);
        }

        return specChangeDataSheetGridRows;

    }

    @Override
    public List<SpecChangeTestProgram> findSpecChangeTestProgram(List<String> programNames) {
        log.debug("findSpecChangeTestProgram(){}");
        return specChangeTestProgramRepository.specChangeTestProgram(programNames);
    }

    @Override
    public List<SpecChangeMaterialPlanFactors> findSpecChangeMaterialsPlanFactor(List<String> materials) {
        log.debug("findSpecChangeMaterialFacility(){}");
        return specChangeMaterialFacilityRepository.specChangeMaterialFacility(materials);

    }

    @Override
    public Optional<ReferenceSpecEntity> findReferenceSpecEntity(String projNumber) {
        ProjectEntity projectEntity = projectEntityRepository.findByProjNumber(projNumber);
        if (projectEntity != null) {
            String projrefId = projectEntity.getRefSpecId();
            ReferenceSpecEntity referenceSpecEntity = referenceSpecEntityRepository.findProjectRefSpec(projrefId);
            return Optional.of(referenceSpecEntity);
        }

        return Optional.empty();
    }

    @Override
    public List<SpecChangeRule> findAllSpecChangeRule() {
        return specChangeRuleRepository.findAllSpecChangeRule();

    }

    @Override
    public void deleteSpecChanges(Map<ProjectDevice, List<SpecChange>> projectDeviceSpecChangesMap,
                                  ProjectDevice projectDevice) {
        if (projectDevice.getId() != null) {
            List<SpecChange> existingSpecChanges = projectDeviceRepository.findById(projectDevice.getId()).get()
                    .getSpecChanges();
            List<String> existingChangeIds = existingSpecChanges.stream().map(SpecChange::getId)
                    .collect(Collectors.toList());
            List<SpecChange> latestSpecChanges = projectDeviceSpecChangesMap.get(projectDevice);
            List<String> latestChangeIds = latestSpecChanges.stream().map(SpecChange::getId)
                    .collect(Collectors.toList());
            existingChangeIds.removeAll(latestChangeIds);


            specChangeRepository.deleteSpecChangeById(existingChangeIds);


        }
    }

    public boolean isActiveTestProgramAndRevision(String programName, String programRevision) {
        log.debug("activeTestProgramAndRevision(){}");
        return specChangeTestProgramRepository.activeTestProgramAndRevision(programName, programRevision);
    }

    public boolean materialSetupInSCSFacility(String material, String facility) {
        log.debug("materialSetupInSCSFacility(){}");
        return specChangeMaterialFacilityRepository.materialSetupInSCSFacility(material, facility);
    }

    public boolean materialSetupInSCSForDie(String material, String die) {
        log.debug("materialSetupForDie(){}");
        return specChangeMaterialFacilityRepository.materialSetupInSCSForDie(material, die);
    }

}

