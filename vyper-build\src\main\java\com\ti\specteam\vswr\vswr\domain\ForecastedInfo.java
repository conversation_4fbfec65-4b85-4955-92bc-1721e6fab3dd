package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForecastedInfo {
    private String swrID;
    private String title;
    private String purpose;
    private String generalComment;
    private String requestorName;
    private String currentStatus;
    private String sbe1Site;
    private String forecastedFlag;
    private String purchaseOrder;
    private String lineItem;
    private String io;
    private String leadframe;
    private String moldCompound;
    private String mountCompound;
    private String wire;
    private String solderball;
    private String chipCapacitor;
    private String lid;
    private String canister;
    private String dieBoat;
    private String frame;
    private String packFillerJewelBox;
    private String shipTube;
    private String waferVial;
    private String tray;
    private String reelTapeAndReel;
    private String tapeTapeAndReel;
    private String tapeCover;
    private String material;
}
