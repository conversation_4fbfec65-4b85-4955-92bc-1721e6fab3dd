package com.ti.specteam.vswr.dashboard.domain;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** ScswrExcelCellConfig */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScswrExcelCellConfig {
  String id;
  @NotNull int excelIndex;
  @NotBlank String excelLabel;
  @NotBlank String excelSpecialName;
  @NotBlank String dbColumnName;
  boolean isMultiselect;
  boolean isConstrainedBySqlValues;
  String sqlValueSource;
  @Valid List<ScswrExcelCellValueMap> possibleValues;
}
