package com.ti.specteam.atssmassupload.domain;

import com.ti.specteam.vyper.apitraveler.model.ApiTraveler;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ProjectDeviceVyperTraveler {
    TravelerMode travelerMode;
    ProjectHeader projectHeader;
    ApiTraveler referenceTraveler;
    List<ApiTraveler> projectDeviceTraveler = new ArrayList<>();

}
