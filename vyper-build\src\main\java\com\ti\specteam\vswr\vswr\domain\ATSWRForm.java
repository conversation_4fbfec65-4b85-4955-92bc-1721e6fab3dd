package com.ti.specteam.vswr.vswr.domain;

import java.util.List;

import java.util.HashMap;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ATSWRForm {
    GeneralInfo generalInfo;
    RequestorInfo requestorInfo;
    DeviceInfo deviceInfo;
    List<DieInfo> dieInfo;
    AssemblyInfo assemblyInfo;
    List<MaterialInfo> bomInfo;
    PackingRequirements packingRequirements;
    List<MaterialInfo> packingMaterial;
    List<ShippingInfo> shippingInfo;
    HashMap<String, List<Comment>> comments;
    Object traveler;
}
