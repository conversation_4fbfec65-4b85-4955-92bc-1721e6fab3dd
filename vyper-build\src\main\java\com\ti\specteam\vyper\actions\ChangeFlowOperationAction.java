package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.RequiredComponentLoader;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.util.AuthorizedOperationService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_FLOW_OPERATION;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeFlowOperationAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final AuthorizedOperationService authorizedOperationService;

    public Build execute(ChangeFlowOperationForm changeFlowOperationForm) {
        log.debug("execute(changeTravelerOperationForm:{})", changeFlowOperationForm);

        Vyper vyper = vyperService.fetchVyper(changeFlowOperationForm);
        Build build = buildService.fetchBuild(changeFlowOperationForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        // find the existing operation
        FlowOperation flowOperation = build.getFlow().getObject().getOperations().stream()
                .filter(fo -> StringUtils.equalsIgnoreCase(fo.getName(), changeFlowOperationForm.getOldName()))
                .findFirst()
                .orElse(null);

        if (null != flowOperation) {

            if (!authorizedOperationService.canUpdateOperation(vyper, build, changeFlowOperationForm.getOldName())) {
                throw new VyperException("Insufficient privilege rights for the user to change operation.");
            }

            // update the operation name
            String oldName = flowOperation.getName();
            flowOperation.setName(changeFlowOperationForm.getNewName());
            flowOperation.getSource().appointUser(securityService.user());

            // update the selections to the new name
            build.getSelections().stream()
                    .filter(selection -> StringUtils.equalsIgnoreCase(selection.getOperation(), changeFlowOperationForm.getOldName()))
                    .forEach(selection -> selection.setOperation(changeFlowOperationForm.getNewName()));

            // refresh
            requiredComponentLoader.load(vyper, build);
            selectionLoader.load(vyper, build);
            travelerRefreshService.load(vyper, build);
            validateOperationLoader.load(vyper, build);

            // save the audit
            auditService.createBuild(
                    vyper.getVyperNumber(),
                    build.getBuildNumber(),
                    CHANGE_FLOW_OPERATION,
                    "changed flow operation: from " + oldName + " to " + changeFlowOperationForm.getNewName()
            );

        } else {
            log.warn("flow not found {} in build {}", changeFlowOperationForm.getOldName(), build.getBuildNumber());
        }

        return buildService.saveBuild(build);
    }

}
