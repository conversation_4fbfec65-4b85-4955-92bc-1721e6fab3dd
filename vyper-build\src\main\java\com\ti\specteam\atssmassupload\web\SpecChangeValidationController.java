package com.ti.specteam.atssmassupload.web;

import com.ti.specteam.atssmassupload.domain.SpecChangeMaterialPlanFactors;
import com.ti.specteam.atssmassupload.domain.SpecChangeRule;
import com.ti.specteam.atssmassupload.domain.SpecChangeTestProgram;
import com.ti.specteam.atssmassupload.service.SpecChangeService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/v1/atssmassupload/validation")
@CrossOrigin(origins = { "*" })
@Slf4j
public class SpecChangeValidationController {

	@Autowired
	private SpecChangeService specChangeService;

	@Autowired
	private UserUtilsService userUtilsService;

	@GetMapping("/testprograms")
	public ResponseEntity<List<SpecChangeTestProgram>> findSpecChangeTestProgram(
			@RequestParam(name = "programNames") List<String> programNames) {
		log.debug("findSpecChangeTestProgram(){} called ");
		userUtilsService.validateUser();
		List<SpecChangeTestProgram> specChangeTestPrograms = specChangeService.findSpecChangeTestProgram(programNames);
		return ResponseEntity.ok(specChangeTestPrograms);

	}

	@GetMapping("/materialsplanfactor")
	public ResponseEntity<List<SpecChangeMaterialPlanFactors>> findSpecChangeMaterialsPlanFactor(
			@RequestParam(name = "materials") List<String> materials) {
		log.debug("findSpecChangeMaterialsPlanFactor(){} called ");
		userUtilsService.validateUser();
		return ResponseEntity.ok(specChangeService.findSpecChangeMaterialsPlanFactor(materials));

	}

	@GetMapping("/specchangerule")
	public ResponseEntity<List<SpecChangeRule>> findAllSpecChangeRule() {
		log.debug("findAllSpecChangeRule(){} called ");
		return ResponseEntity.ok(specChangeService.findAllSpecChangeRule());

	}

}