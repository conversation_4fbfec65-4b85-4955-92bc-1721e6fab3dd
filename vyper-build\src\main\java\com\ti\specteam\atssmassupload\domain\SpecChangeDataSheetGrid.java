package com.ti.specteam.atssmassupload.domain;

import javax.validation.constraints.NotNull;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

/**
 *
 * <AUTHOR>
 */

@Builder
@Data
@Jacksonized
public class SpecChangeDataSheetGrid {

	@NotNull
	private String material;

	@NotNull
	private String oldMaterial;

	@NotNull
	private String specDevice;

	@NotNull
	private String componentName;

	@NotNull
	private String componentValue;

	private String attributeName;

	private String attributeValue;

	@NotNull
	private String flowType;

	@NotNull
	private String operationName;

	@NotNull
	private Integer componentOccurrance;

	private String validationStatus;

	private String processingStatus;

	private String action;

	private String deviceId;

	private String changeId;

	private String scnId;

	public SpecChangeDataSheetGrid() {
		super();
	}

	public SpecChangeDataSheetGrid(@NotNull String material, @NotNull String oldMaterial, @NotNull String specDevice,
			@NotNull String componentName, String componentValue, String attributeName, String attributeValue,
			@NotNull String flowType, @NotNull String operationName, @NotNull Integer componentOccurrance,
			String validationStatus, String processingStatus, String action, String deviceId,
			String changeId, String scnId) {
		super();
		this.material = material;
		this.oldMaterial = oldMaterial;
		this.specDevice = specDevice;
		this.componentName = componentName;
		this.componentValue = componentValue;
		this.attributeName = attributeName;
		this.attributeValue = attributeValue;
		this.flowType = flowType;
		this.operationName = operationName;
		this.componentOccurrance = componentOccurrance;
		this.validationStatus = validationStatus;
		this.processingStatus = processingStatus;
		this.action = action;
		this.deviceId = deviceId;
		this.changeId = changeId;
		this.scnId = scnId;
	}

}
