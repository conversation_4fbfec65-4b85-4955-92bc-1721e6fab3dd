package com.ti.specteam.atssmassupload.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.ti.specteam.atssmassupload.domain.SpecChange;

/**
 *
 * <AUTHOR>
 */

public interface SpecChangeRepository extends JpaRepository<SpecChange, String> {

	@Query(value = "SELECT * FROM MU_PROJ_SPEC_CHANGE WHERE PROJ_DEVICE_ID = :projectDeviceId", nativeQuery = true)
	List<SpecChange> findSpecChangesByProjectDeviceId(String projectDeviceId);
	
	@Modifying
	@Transactional
	@Query(value = "DELETE FROM MU_PROJ_SPEC_CHANGE SC WHERE SC.ID  IN (:existingChangeIds)", nativeQuery = true)
	void deleteSpecChangeById(@Param("existingChangeIds") List<String> existingChangeIds);

}