package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.pra.model.PraState;
import com.ti.specteam.vyper.validate.ValidatePraService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.security.user.UserUtilsService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_CHANGE_WORKFLOW;

@Service
@Slf4j
@RequiredArgsConstructor
public class PraWorkFlowAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final ValidatePraService validatePraService;
    private final AuditService auditService;
    private final UserUtilsService userUtilsService;

    public Pra executeAtApprove(PraNumberForm praNumberForm){
        log.debug("executeAtApprove(praNumberForm:{})", praNumberForm);

        // get Vyper and PRA
        Pra pra = praService.fetchPra(praNumberForm);
        Vyper vyper = vyperService.fetchVyper(pra.getVyperNumber());

        //check if able to edit
        validateService.checkOpen(vyper);
        validateService.checkEditable(vyper, pra);

        // Change the State
        pra.setState(PraState.PRA_BU_REVIEW);

        auditService.createPra(
            pra.getVyperNumber(),
            pra.getPraNumber(),
            pra.getBuildNumber(),
            PRA_CHANGE_WORKFLOW,
            "workflow: at approve"
        );

        return praService.savePra(pra);
    }

    public Pra executeBuRework(PraNumberForm praNumberForm){
        log.debug("executeAtApprove(praNumberForm:{})", praNumberForm);

        // get Vyper and PRA
        Vyper vyper = vyperService.fetchVyper(praNumberForm);
        Pra pra = praService.fetchPra(praNumberForm);

        //check if able to edit
        validateService.checkOpen(vyper);
        validateService.checkEditable(vyper, pra);

        // Change the State
        pra.setState(PraState.PRA_DRAFT);

        auditService.createPra(
            pra.getVyperNumber(),
            pra.getPraNumber(),
            pra.getBuildNumber(),
            PRA_CHANGE_WORKFLOW,
            "workflow: bu rework"
        );

        return praService.savePra(pra);
    }

    public Pra executeApprove(PraNumberForm praNumberForm){
        log.debug("executeApprove(praNumberForm:{})", praNumberForm);

        // get Vyper and PRA
        Vyper vyper = vyperService.fetchVyper(praNumberForm);
        Pra pra = praService.fetchPra(praNumberForm);

        userUtilsService.validateUserByFacility(pra.getFacility().getPdbFacility());
        validateService.checkOpen(vyper);
        validateService.checkEditable(vyper, pra);

        // Change the State to approved
        pra.setState(PraState.PRA_APPROVED);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_CHANGE_WORKFLOW,
                "workflow: approve");

        return praService.savePra(pra);
    }

    public Pra executeDelete(PraNumberForm praNumberForm){
        log.debug("executeDelete(praNumberForm:{})", praNumberForm);

        Vyper vyper = vyperService.fetchVyper(praNumberForm);
        Pra pra = praService.fetchPra(praNumberForm);

        validateService.checkOpen(vyper);
        validatePraService.canDeletePra(pra);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_CHANGE_WORKFLOW,
                "workflow: delete");

        praService.delete(pra);

        return pra;
    }

}
