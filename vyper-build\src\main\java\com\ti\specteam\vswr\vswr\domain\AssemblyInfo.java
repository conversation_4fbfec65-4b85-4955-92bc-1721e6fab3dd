package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssemblyInfo {
    String vswrID;
    String assemblyReq;
    String header;
    String baseOutline;
    String wireDiameter;
    String bondPadMetalization;
    String mbPath;
    String mbOrArcPath;
    String forecastedFlag;
}
