package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.ChangelinkPcn;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_CHANGELINK_PCN;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeChangelinkPcnAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final AuditService auditService;

    public Build execute(ChangeChangeLinkPcnForm changeChangeLinkPcnForm) {
        log.debug("execute(changeChangeLinkPcnForm:{})", changeChangeLinkPcnForm);

        Vyper vyper = vyperService.fetchVyper(changeChangeLinkPcnForm);
        Build build = buildService.fetchBuild(changeChangeLinkPcnForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getChangelink().getPcns().clear();
        for (Map<String, Object> pcn : changeChangeLinkPcnForm.getPcns()) {
            ChangelinkPcn clp = new ChangelinkPcn();
            clp.getObject().putAll(pcn);
            clp.getSource().appointUser(securityService.user());
            build.getChangelink().getPcns().add(clp);
        }

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_CHANGELINK_PCN,
                "changed changelink pcn to: " + changeChangeLinkPcnForm.display()
        );

        return buildService.saveBuild(build);
    }

}
