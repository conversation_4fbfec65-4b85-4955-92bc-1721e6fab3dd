package com.ti.specteam.vyper.vscn.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.core.exception.VyperNumberInvalidException;
import com.ti.specteam.vyper.verifier.model.ValidatedComponent;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.verifier.model.VerifierSource;
import com.ti.specteam.vyper.verifier.model.Verifiers;
import com.ti.specteam.vyper.security.user.User;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Builder
@Data
@Jacksonized
public class Vscn {

    private long version;
    private String vscnNumber;
    private String praNumber;
    private String buildNumber;
    private String vyperNumber;
    private VscnState state;
    private String description;

    @Builder.Default
    private List<Comment> comments = new ArrayList<>();
    @Builder.Default
    private Material material = new Material();
    @Builder.Default
    private Facility facility = new Facility();
    @Builder.Default
    private PimSetup pimSetup = new PimSetup();

    @Builder.Default
    private List<AtssScn> atssScns = new ArrayList<>();
    @Builder.Default
    private List<Component> components = new ArrayList<>();
    @Builder.Default
    private HashMap<String, ValidatedComponent> validatedComponents = new HashMap<>();
    @Builder.Default
    private Symbolization symbolization = new Symbolization();
    @Builder.Default
    private Test test = new Test();
    @Builder.Default
    private PackConfig packConfig = new PackConfig();
    @Builder.Default
    private final List<Verifier> verifiers = new ArrayList<>();
    @Builder.Default
    private Traveler traveler = new Traveler();
    @Builder.Default
    private final Map<String, DiagramApproval> diagramApprovals = new HashMap<>();
    @Builder.Default
    private ChangeNumber changeNumber = new ChangeNumber();
    @Builder.Default
    private List<String> changedComponentsGroup = new ArrayList<>();

    /**
     * Return the suffix of the vscn number
     *
     * @return the suffix
     * @throws VyperException if the vscn number is invalid
     */
    @JsonIgnore
    public long getSuffix() {
        Pattern pattern = Pattern.compile("^VSCN[\\d]{7}-([\\d]{4})$");
        Matcher matcher = pattern.matcher(vscnNumber);
        if (!matcher.find()) {
            throw new VyperException("The VSCN number is invalid.");
        }
        return Long.parseLong(matcher.group(1));
    }

    /**
     * Given a vyper number and a suffix, generate the VSCN number.
     *
     * @param vyperNumber - The vyper number. The VSCN will have thevyper number's 7 digit prefix number.
     * @param suffix      - The suffix number to use.
     * @return The VSCN number, in the format VSCN1234567-1234
     */
    public static String generateVscnNumber(String vyperNumber, long suffix) {
        Pattern pattern = Pattern.compile("^VYPER([\\d]{7})$");
        Matcher matcher = pattern.matcher(vyperNumber);
        if (!matcher.find()) {
            throw new VyperNumberInvalidException();
        }

        return String.format("VSCN%s-%04d", matcher.group(1), suffix);
    }


    public boolean isEditable() {
        return state == VscnState.VSCN_DRAFT;
    }

    /**
     * add a comment
     *
     * @param user {@link User} The user's bade number
     * @param text {@link String} The comment to add
     */
    public Comment addComment(User user, String text) {
        Comment comment = new Comment();
        comment.getWho().setUserid(user.getUserid());
        comment.getWho().setUsername(user.getUsername());
        comment.setText(text);
        comments.add(comment);
        return comment;
    }

    public boolean componentExists(String name) {
        return null != findComponentByName(name);
    }

    public Component findComponentByName(String cName) {
        return components.stream()
                .filter(component -> Objects.equals(cName, component.getName()))
                .findFirst()
                .orElse(null);
    }

    public Component findOrCreateComponent(String cName, Source source) {
        Component component = findComponentByName(cName);
        if (component == null) {
            component = new Component();
            component.setName(cName);
            component.getSource().set(source);
            components.add(component);
        }
        return component;
    }

    /**
     * Returns the matching verifier, or creates a new one.
     *
     * @param component {@link Component} The component
     * @param componentPriority {@link ComponentPriority} The component priority
     * @param source {@link VerifierSource} The verifier source.
     * @return {@link Verifier} The verifier
     */
    @SuppressWarnings("UnusedReturnValue")
    public Verifier findOrCreateVerifier(Component component, ComponentPriority componentPriority, VerifierSource source) {
        return findOrCreateVerifier(component.getName(), componentPriority.getValue(), componentPriority.getEngineering(), source);
    }

    /**
     * Returns the matching verifier, or creates a new one.
     *
     * @param name The component name
     * @param value The component value
     * @param source {@link VerifierSource} The verifier source
     * @return {@link Verifier} The verifier
     */
    public Verifier findOrCreateVerifier(String name, String value, Engineering engineering, VerifierSource source) {

        // search for the verifier
        Verifier verifier = verifiers.stream()
                .filter(v -> v.match(name, value, source))
                .findFirst()
                .orElse(null);

        // if not found
        if(null == verifier) {

            // create a new verifier
            verifier = Verifier.builder()
                    .name(name)
                    .value(value)
                    .source(source)
                    .engineering(engineering)
                    .build();

            // add it to the list of verifier
            verifiers.add(verifier);
        }

        return verifier;
    }

    /**
     * Return a list of the verifier's that match the verifier source.
     *
     * @param source {@link VerifierSource} The source to match
     * @return A list of {@link Verifiers}
     */
    public List<Verifier> findVerifiers(VerifierSource source) {
        return verifiers.stream()
                .filter(verifier -> verifier.getSource() == source)
                .collect(Collectors.toList());
    }

    /**
     * Returns the verifier with the same name and source, or else null.
     *
     * @param name The component name.
     * @param source The verifier source.
     * @return The found {@link Verifier}, or null if not found.
     */
    public Verifier findVerifier(String name, VerifierSource source) {
        return verifiers.stream()
                .filter(verifier -> StringUtils.equalsIgnoreCase(name, verifier.getName()) && verifier.getSource() == source)
                .findFirst()
                .orElse(null);
    }

    public List<Verifier> findVerifiersByComponentName(String componentName){
        return  verifiers.stream()
                .filter(verifier -> verifier.getName() != null)
                .filter(verifier -> verifier.getName().equalsIgnoreCase(componentName) )
                .collect(Collectors.toList());
    }

    public TravelerComponent findTestTravelerComponentByComponentName(String componentName){
        return test.getTravelerOperations().stream()
                .flatMap(travelerOperation -> travelerOperation.getComponents().stream())
                .filter(travelerComponent -> StringUtils.equalsIgnoreCase(componentName, travelerComponent.getName()))
                .findFirst()
                .orElse(null);
    }

    public void resetMaterial() {
        material = new Material();
    }

    // opn
    // dies (order)
    // program (name, revision)
    // symbol (name, cust, ecat)
    // pim check
    // changelink
}
