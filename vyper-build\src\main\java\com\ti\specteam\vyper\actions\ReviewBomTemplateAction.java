package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.BomTemplateRequestEmailProperties;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.email.EmailContext;
import com.ti.specteam.vyper.email.EmailService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.REVIEW_BOM_TEMPLATE;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReviewBomTemplateAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final EmailService emailService;
    private final BomTemplateRequestEmailProperties bomTemplateRequestEmailProperties;
    private final AuditService auditService;

    public Build execute(ReviewBomTemplateForm reviewBomTemplateForm) throws IOException, MessagingException {
        log.debug("execute(reviewBomTemplateForm:{})", reviewBomTemplateForm);

        Vyper vyper = vyperService.fetchVyper(reviewBomTemplateForm);
        Build build = buildService.fetchBuild(reviewBomTemplateForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAt(vyper, build);
        validateService.checkEditable(vyper, build);

        com.ti.specteam.vyper.security.user.User user = securityService.user();

        // prepare email data

        String owners = vyper.getOwners().stream().map(User::getUsername).collect(Collectors.joining(", "));

        EmailContext context = new EmailContext();
        context.setTemplateName("review Bill of Process Template");
        context.getBinding().put("owners", owners);
        context.getBinding().put("material", build.getMaterial().getMaterial());
        context.getBinding().put("facility", build.getFacility().getPdbFacility());
        context.getBinding().put("buildnumber", build.getBuildNumber());
        context.getBinding().put("pin", build.getMaterial().getPin());
        context.getBinding().put("pkg", build.getMaterial().getPkg());
        context.getBinding().put("pkggroup", build.getMaterial().getPkgGroup());
        context.getBinding().put("pkgniche", build.getPackageNiche().getName());
        context.getBinding().put("templates", build.getBomTemplate().getObject().getName());
        context.getBinding().put("from", user.getUsername());
        context.getBinding().putAll(build.getBomTemplate().getObject().getContext());

        // add the merge context values

        Map<String, String> btContext = build.getBomTemplate().getObject().getContext();
        String mergeContext = btContext.keySet().stream()
                .map(key -> key + " = " + btContext.get(key))
                .collect(Collectors.joining("<br/>"));
        context.getBinding().put("mergecontext", mergeContext);

        // set subject, addresses

        context.setSubject(bomTemplateRequestEmailProperties.getSubject());
        context.getTos().add(bomTemplateRequestEmailProperties.getTo());
        context.getCcs().add(securityService.userid() + "@ti.com");

        emailService.send(context);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                REVIEW_BOM_TEMPLATE,
                "requested Bill of Process Template review"
        );

        return buildService.saveBuild(build);
    }

}
