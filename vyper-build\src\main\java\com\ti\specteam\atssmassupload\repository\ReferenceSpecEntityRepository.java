package com.ti.specteam.atssmassupload.repository;

import com.ti.specteam.atssmassupload.domain.ReferenceSpecEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ReferenceSpecEntityRepository extends JpaRepository<ReferenceSpecEntity, String> {

	@Query(nativeQuery = true, value = "SELECT * FROM MU_REF_SPEC where ID = :projrefId")
	ReferenceSpecEntity findProjectRefSpec(String projrefId);

}