package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.OperationValidationService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.SystemName;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.util.AuthorizedOperationService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.FILL_COMPONENT_CLEAR;

@Service
@Slf4j
@RequiredArgsConstructor
public class FillComponentClearAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final OperationValidationService operationValidationService;
    private final AuditService auditService;
    private final AuthorizedOperationService authorizedOperationService;

    public Build execute(FillComponentClearForm fillComponentClearForm) {
        log.debug("execute(fillComponentClearForm:{})", fillComponentClearForm);

        Vyper vyper = vyperService.fetchVyper(fillComponentClearForm);
        Build build = buildService.fetchBuild(fillComponentClearForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper,build);
        validateService.checkEditable(vyper, build);

        // remove items that were set by fill components, then remove selections that were set by fill components, and empty items list

        build.getSelections().stream()
                .filter(selection -> !operationValidationService.isOperationChecked(build, selection.getOperation()))
                .filter(selection -> authorizedOperationService.canUpdateOperation(vyper, build, selection.getOperation()))
                .forEach(selection -> selection.getItems()
                        .removeIf(selectionItem -> selectionItem.getSource().matches(SystemName.FILL_COMPONENTS)));
        build.getSelections().removeIf(selection -> selection.getItems().isEmpty() && selection.getSource().matches(SystemName.FILL_COMPONENTS));

        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // clear the fill component record

        build.getFillComponent().reset();

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                FILL_COMPONENT_CLEAR,
                "filled components cleared"
        );

        return buildService.saveBuild(build);
    }
}
