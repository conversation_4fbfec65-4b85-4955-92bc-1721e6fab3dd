package com.ti.specteam.vswr.dashboard.BatchProcessing.Repository;


import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.specteam.vswr.dashboard.domain.BpDiff;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig;
import com.ti.spring.annotations.MyBatisRepository;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 */
@MyBatisRepository
public interface SCSWRDao {

    List<Map<String, String>> getColumns();

    List<String> getATSites();

    List<String> getSBE1();

    List<String> getSwrType();

    List<String> getMarketCat();
    
    List<Map<String, Object>> getRequests(
            @Param("userId") String userId,
            @Param("siteExt") List<Map<String, String>> siteExt, @Param("swrIds") String swrIds);

    Map<String, Object> getRequest(@Param("swrId") String swrId);
    
    void insertRequests(Map scswrList);
    
    int validateSwrId(String swrId);
    
    void createRequestsHistoryRecordByRequestRecord(@Param("swrId") String swrId);
    void mergeRequestsRedbullIntoRequestsBySwrIdAndUserId(@Param("swrId") String swrId, @Param("userId") String userId);
    void deleteRequestRedbullRecordsBySwrIdAndUserId(@Param("swrId") String swrId, @Param("userId") String userId);
    
    List<Map<String, String>> requestsColumns();
    
    List<Map<String, Object>> getUserSecurity(
            @Param("userId") String userId);
    
    int confirmAtSite(String atSite);
    
    int confirmSbeSite(String sbe1);
    
    void delRedbullMax(@Param("swrId") String swrId, @Param("userId") String userId);

    void deleteRedbullRecords(@Param("swrId") String swrId, @Param("userId") String userId);
    
    List<Map<String, Object>>  getSbeView(@Param("uid") String uid, @Param("swrId") String swrId);
        
    List<String> getSwrIdUpdateList (@Param("uid") String uid);
    
    List<ScswrExcelCellConfig> getSbeExcelCfgs();
    List<ScswrExcelCellConfig> getConstrainedSbeExcelConfigs();
    ScswrExcelCellConfig getSbeExcelCfgByExcelLabel(@Param("excelLabel") String excelLabel);
    
    List<String> getAtCoordCatEmail(@Param("atSite") String atSite);
    
    List<Map<String,String>> checkSwrsNoForecast(@Param("swrId") String swrId, @Param("status") String status);
    
    void calculateDates(@Param("swrId") String swrId, @Param("status") String status);
    
    void updateTarget(@Param("targetDate") String targetDate, @Param("swrId") String swrId, @Param("status") String status);
    
    void updateForecastDates(@Param("updatedDate") String updatedDate, @Param("swrId") String swrId, @Param("status") String status);
    
    void insertForecastDates(@Param("updatedDate") String updatedDate, @Param("swrId") String swrId, 
            @Param("status") String status,@Param("targetDate") String targetDate, @Param("actualDate") String actualDate);
    
    void updateEstDate(@Param("atStartTargetDate") String atStartTargetDate, 
            @Param("swrId") String swrId, @Param("atShippedTargetDate") String atShippedTargetDate);
    
    Map<String,String> getLdapxref(@Param("org") String org);
    
    Map<String, Object> getRequestRedbull(@Param("swrId") String swrId,
            @Param("userId") String userId);
    
    String getPlantCode(@Param("atSite") String atSite);
    
    List<Map<String, Object>> swrListsByStat(@Param("tableName") String tableName,
            @Param("swrStat") String swrStat, @Param("submitSwrStat") String submitSwrStat,
            @Param("params") DataTableParams params);
    
    int countSwrListsByStat(@Param("tableName") String tableName,
            @Param("swrStat") String swrStat, @Param("submitSwrStat") String submitSwrStat,
            @Param("params") DataTableParams params);

     String getCurrentStatusBySwrId(@Param("swrId") String swrId);

     String getSignoffFlagFromLdapRefBySwrId(@Param("swrId") String swrId);

     void pkgSwrHistoryUpdateStatus(@Param("swrId") String swrId, @Param("newStatus") String newStatus, @Param("reasonCode") String reasonCode, @Param("changeReason") String changeReason, @Param("uid") String uid,
               @Param("result") Integer result);

    List<String> getDropdownValuesFromSql(@Param("sqlValueSource") String sqlValueSource);

    List<String> getBpDiffColumns();

    List<BpDiff> getBpDiffs(@Param("uploadedBy") String uploadedBy, @Param("swrId") String swrId, @Param("columns") List<String> columns);

    boolean isBpDiff(@Param("uploadedBy") String uploadedBy, @Param("swrId") String swrId, @Param("columns") List<String> columns);
}
