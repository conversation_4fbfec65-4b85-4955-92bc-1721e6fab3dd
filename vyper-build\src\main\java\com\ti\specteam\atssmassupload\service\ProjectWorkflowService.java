package com.ti.specteam.atssmassupload.service;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.ProjectEntity;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceRepository;
import com.ti.specteam.atssmassupload.repository.ProjectEntityRepository;
import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.email.EmailService;
import com.ti.specteam.vyper.email.EmailTableContext;
import com.ti.specteam.vyper.email.TaskNotificationProperties;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.buildlink.VyperLinkProperties;
import com.ti.specteam.vyper.security.user.UserService;
import com.ti.specteam.vyper.taskService.Assignment;
import com.ti.specteam.vyper.taskService.TaskServiceService;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import com.ti.specteam.vyper.vscn.model.VscnState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectWorkflowService {
    private final ProjectEntityRepository projectEntityRepository;
    private final EmailService emailService;
    private final SecurityService securityService;
    private final TaskNotificationProperties taskNotificationProperties;
    private final VyperLinkProperties vyperLinkProperties;
    private final ProjectDeviceRepository projectDeviceRepository;
    private final TaskServiceService taskServiceService;
    private final UserService userService;
    private final VyperService vyperService;
    private final VscnService vscnService;
    private final AuditService auditService;
    private final BuildService buildService;

    public ProjectEntity changeWorkFlow(String projNumber, String projectStatus) {
    	
        log.debug("changeWorkFlow(projNumber:{} projectStatus:{})", projNumber,projectStatus);
        com.ti.specteam.vyper.security.user.User user = securityService.user();
        Date now = new Date();

        if ( projectStatus.equalsIgnoreCase(VscnState.VSCN_APPROVED.toString()) ){
            projectStatus = "AT APPROVED";
        }
        ProjectEntity projectToBeUpdated = projectEntityRepository.findByProjNumber(projNumber);

        // Update the state of project device VSCNs based on project status
        String auditAction = "";
        VscnState vscnState = null;
        if(projectStatus.equalsIgnoreCase("at review")){
            auditAction = "submit";
            vscnState = VscnState.VSCN_AT_REVIEW;
        }
        else if(projectStatus.equalsIgnoreCase("at approved")){
            auditAction = "approve";
            vscnState = VscnState.VSCN_APPROVED;
        }
        else if( projectStatus.equalsIgnoreCase("ref validated")){
            auditAction = "reject";
            vscnState = VscnState.VSCN_DRAFT;
        }

        // Email the owner if the state changed
        if ( projectStatus.equalsIgnoreCase("at review") || projectStatus.equalsIgnoreCase("at approved") ||
        		(projectToBeUpdated.getStatus().equalsIgnoreCase("at review") || 
        				projectToBeUpdated.getStatus().equalsIgnoreCase("at approved") && projectStatus.equalsIgnoreCase("ref validated")) ){
            projectToBeUpdated.setStatus(projectStatus);
            sendEmailUpdate(projectToBeUpdated);
        }
        projectToBeUpdated.setStatus(projectStatus);
        projectToBeUpdated.setProjNumber(projNumber);
        projectToBeUpdated.setUpdatedBy(user.getUserid());
        projectToBeUpdated.setUpdatedDttm(now);

        for(ProjectDevice projectDevice: projectDeviceRepository.findProjectDeviceByProjectId(projectToBeUpdated.getId())){
            if ( projectDevice.getVscnNumber() != null ){
                Vscn vscn = vscnService.findByVscnNumber(projectDevice.getVscnNumber());
                Vyper vyper = vyperService.findByVyperNumber(vscn.getVyperNumber());
                if(!auditAction.isEmpty() && vscnState != null){
                    vscn.setState(vscnState);
                    auditService.createVscn(
                            vyper.getVyperNumber(),
                            vscn.getVscnNumber(),
                            AuditActivity.VSCN_CHANGE_WORKFLOW,
                            "Workflow action: " + auditAction);
                    vscnService.saveVscn(vscn);
                }
            }else{
                log.warn("changeWorkFlow >> VSCN is not created for the material "+projectDevice.getMaterial());
            }
        }

        return projectEntityRepository.save(projectToBeUpdated);
    }

    private void sendEmailUpdate(ProjectEntity projectEntity){
        // Email the Owner about the VSCN tasks created
        log.debug("sendEmailUpdate({})",projectEntity);


        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());


        EmailTableContext tableContext = new EmailTableContext();

        String message = "The ATSS MU Project ";
        if ( projectEntity.getStatus().toLowerCase().contains("review") ){
            message+= " Submitted for review";
        }else if(projectEntity.getStatus().toLowerCase().contains("approve")){
            message+= " Approved";
        }else{
            message+= " Status "+projectEntity.getStatus().toLowerCase();
        }

        // Header And Base changes
        tableContext.setHeader(getHeader(projectEntity));
        tableContext.setSubheader(message);
        tableContext.setCallToAction(getCallToAction());
        tableContext.setLink(getProjectLink(projectEntity));
        tableContext.setSubject(getSubject(projectEntity));
        tableContext.getTos().addAll(getEmailList(projectEntity));

        // Adding Rows
        tableContext.put("Action",projectEntity.getStatus().toLowerCase().contains("review") ? "AT Approval" : "Back to BU");
        tableContext.put("MU Project", projectEntity.getProjNumber());
        tableContext.put("Reference PRA Number", projectEntity.getReferenceSpec().getVyperPraNumber());
        tableContext.put("Reference Build Number", projectEntity.getReferenceSpec().getVyperBuildNumber());
        tableContext.put("Submitter", user.getUsername());
        tableContext.put("Status", projectEntity.getStatus());
        tableContext.put("Devices", getProjectSpecDevices(projectEntity));
        tableContext.put("Facility", projectEntity.getFacilityAt());

        try {
            emailService.send(tableContext);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }

    private List<String> getEmailList(ProjectEntity projectEntity){
        // Add owner and Vyper submission users
        List<String> userIdsList = new ArrayList<>();

        userIdsList.add(projectEntity.getOwnerId());
        if ( projectEntity.getStatus().equalsIgnoreCase("at review")) {
            List<Assignment> assignments = taskServiceService.getMassUploadTaskAssignments(projectEntity.getProjNumber());
            for(Assignment assignment: assignments) {
                if(!userIdsList.contains(assignment.getUserId()) ){
                    userIdsList.add(assignment.getUserId());
                }
            }
        }
        userIdsList.add(securityService.user().getUserid());

        return userService.findEmailsByUserids(userIdsList);
    }

    private String getProjectSpecDevices(ProjectEntity projectEntity){
        return projectDeviceRepository.findProjectDeviceByProjectId(projectEntity.getId())
                .stream()
                .map(ProjectDevice::getSpecDevice)
                .collect(Collectors.joining(","));
    }

    private String getSubject (ProjectEntity projectEntity){
    	Build build = buildService.fetchBuild(projectEntity.getReferenceSpec().getVyperBuildNumber());
    		return taskNotificationProperties.getSubject()
                .replaceAll("<buildnumber>", projectEntity.getProjNumber())
                .replaceAll("<device>",projectEntity.getReferenceSpec().getVyperPraNumber())
                .replaceAll("<facility>", projectEntity.getFacilityAt())
                .replaceAll("<environment>", taskNotificationProperties.getEnvironment())
                .replaceAll("<sbe>", build.getMaterial().getSbe())
                .replaceAll("<sbe-1>",build.getMaterial().getSbe1());
        
    }


    private String getProjectLink(ProjectEntity projectEntity){
        return vyperLinkProperties.getBaseUrl()+
                "/atssmassupload/projects/actions/"+ projectEntity.getId();
    }

    private String getHeader(ProjectEntity projectEntity){
        return taskNotificationProperties.getHeader()
                .replaceAll("<buildnumber>",projectEntity.getProjNumber());
    }
    private String getCallToAction(){
        return taskNotificationProperties.getCalltoaction();
    }

}