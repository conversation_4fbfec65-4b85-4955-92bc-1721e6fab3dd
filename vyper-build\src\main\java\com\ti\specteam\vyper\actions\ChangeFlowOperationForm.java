package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeFlowOperationForm extends BuildNumberForm {

    @NotNull
    @Size(min = 1)
    private String oldName;

    @NotNull
    @Size(min = 1)
    private String newName;

}
