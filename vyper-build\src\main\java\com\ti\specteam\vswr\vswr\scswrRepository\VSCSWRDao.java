package com.ti.specteam.vswr.vswr.scswrRepository;

import com.ti.specteam.vswr.vswr.domain.BomInfo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.ti.specteam.vswr.vswr.domain.VSCSWRForm;


@Repository
public interface VSCSWRDao {

    public VSCSWRForm fetchExistingScswr(@Param("swrID") String swrID);

    public List<String> getLatestLegacyID(@Param("dateID") String dateID);

    public String getAtSite(@Param("plantCode") String plantCode);

    public String getPlantCode(@Param("atSite") String atSite);
    
    public String getScswrFabCode(@Param("fabCode") String fabCode);

    public void updateExistingScswr(@Param("vscswrForm") VSCSWRForm vscswrForm);

    public void addNewScswr(@Param("vscswrForm") VSCSWRForm vscswrForm);

    public void addNewScswrHistoryRecord(
        @Param("swrID") String swrID,
        @Param("reason") String reason,
        @Param("aid") String aid
    );
    
    public void addScswrHistoryRecordForVyper(
        @Param("swrID") String swrID,
        @Param("status") String status,
        @Param("startedBy") String reason
    );

    void addBomComments(List<BomInfo> bomInfos);
}
