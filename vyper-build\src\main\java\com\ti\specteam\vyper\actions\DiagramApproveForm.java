package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.DiagramApprovalType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class DiagramApproveForm extends BuildNumberForm {

    @NotNull
    @Size(min = 1, max = 70)
    private String diagram;

    private DiagramApprovalType approvalType;
}
