package com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils;


/**
 * Helper class to represent a search parameter in DataTables filtering feature.
 */
public class DataTableSearchParam {

    String start;
    String end;

    public DataTableSearchParam(
            final String start, final String end) {
        this.start = start;
        this.end = end;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    @Override
    public String toString() {
        return "DataTableSearchParam{" + "start=" + start + ", end=" + end + '}';
    }
}
