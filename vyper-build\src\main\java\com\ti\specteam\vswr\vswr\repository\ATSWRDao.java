package com.ti.specteam.vswr.vswr.repository;

import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.ti.specteam.vswr.vswr.domain.AssemblyInfo;
import com.ti.specteam.vswr.vswr.domain.Comment;
import com.ti.specteam.vswr.vswr.domain.DeviceInfo;
import com.ti.specteam.vswr.vswr.domain.DieInfo;
import com.ti.specteam.vswr.vswr.domain.DieLotStatus;
import com.ti.specteam.vswr.vswr.domain.ForecastedInfo;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.domain.IntransitStatus;
import com.ti.specteam.vswr.vswr.domain.MaterialInfo;
import com.ti.specteam.vswr.vswr.domain.PackingRequirements;
import com.ti.specteam.vswr.vswr.domain.RequestorInfo;
import com.ti.specteam.vswr.vswr.domain.ShippingInfo;

@Repository
public interface ATSWRDao {

    public List<HashMap<String, String>> fetchSwrTypeOptions(@Param("vBuildType") String vBuildType);

    public List<HashMap<String, String>> fetchPriorityOptions();

    public String fetchPkgGroupEmail(@Param("pkg") String pkg);

    public List<HashMap<String, String>> fetchOffloadInfoOptions();
    
    public ForecastedInfo fetchForecastedInfo(@Param("forecastID") String forecastID);

    public List<String> fetchBomComponents();

    public List<String> fetchPackComponents();

    public List<HashMap<String, String>> fetchFinishedGoodOptions();

    public List<HashMap<String, String>> fetchWaferSkeletonOptions();
    
    public List<HashMap<String, String>> fetchShippToPlantOptions();

    public List<HashMap<String, String>> fetchDieLocationOptions();

    public List<HashMap<String, String>> fetchStateOfFinishOptions();
    
    public List<MaterialInfo> fetchMaterialInfo(@Param("components") List<HashMap<String, String>> components, @Param("plant") int plant);
    
    public List<MaterialInfo> fetchMaterialInfoNotInPlant(@Param("components") List<HashMap<String, String>> components, @Param("plant") int plant);

    public GeneralInfo fetchGeneralInfo(@Param("vswrID") String vswrID);

    public RequestorInfo fetchRequestorInfo(@Param("vswrID") String vswrID);

    public DeviceInfo fetchDeviceInfo(@Param("vswrID") String vswrID);

    public AssemblyInfo fetchAssemblyInfo(@Param("vswrID") String vswrID);

    public List<MaterialInfo> fetchBomInfo(@Param("vswrID") String vswrID);
    
    public PackingRequirements fetchPackingRequirements(@Param("vswrID") String vswrID);

    public List<MaterialInfo> fetchPackingMaterials(@Param("vswrID") String vswrID);

    public List<DieInfo> fetchDieInfo(@Param("vswrID") String vswrID);

    public List<Comment> fetchComments(@Param("vswrID") String vswrID);

    public List<ShippingInfo> fetchShippingInfo(@Param("vswrID") String vswrID);

    public List<DieLotStatus> fetchDieLotStatus(@Param("material") String material, @Param("plant") String plant, @Param("inPlant") boolean inPlant);

    public List<IntransitStatus> fetchDieLotIntransitStatus(@Param("material") String material, @Param("plant") String plant);
}
