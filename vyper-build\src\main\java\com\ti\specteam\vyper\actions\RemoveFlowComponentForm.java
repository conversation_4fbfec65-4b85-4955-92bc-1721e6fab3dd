package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class RemoveFlowComponentForm extends BuildNumberForm {

    @NotNull
    @Size(min=1)
    private String operationName;

    @NotNull
    private String componentName;

}
