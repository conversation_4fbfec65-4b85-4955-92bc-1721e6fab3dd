package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pgs.PgsService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.verifier.vscn.VscnMaterialSCSFacilityVerifier;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeVscnMaterialAction {

    private final VyperService vyperService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final PgsService pgsService;
    private final SecurityService securityService;
    private final VscnMaterialSCSFacilityVerifier vscnMaterialSCSFacilityVerifier;

    public Vscn execute(ChangeVscnMaterialForm changeVscnMaterialForm) {
        log.debug("execute(changeVscnMaterialForm:{})", changeVscnMaterialForm);

        Vscn vscn = vscnService.findByVscnNumber(changeVscnMaterialForm);
        Vyper vyper = vyperService.findByVyperNumber(vscn.getVyperNumber());

        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        changeMaterial(vscn, changeVscnMaterialForm);
        vscnMaterialSCSFacilityVerifier.verify(vyper,vscn);

        auditService.createVscn(
                vscn.getVyperNumber(),
                vscn.getVscnNumber(),
               AuditActivity.VSCN_CHANGE_MATERIAL,
                "Changed material to: " + changeVscnMaterialForm.getMaterial());

        return vscnService.saveVscn(vscn);
    }

    public void changeMaterial(Vscn vscn, ChangeVscnMaterialForm changeVscnMaterialForm){
        String previousMaterial = vscn.getMaterial().getMaterial();
        vscn.resetMaterial();

        vscn.getMaterial().getObject().put("Material", changeVscnMaterialForm.getMaterial());
        vscn.getMaterial().getSource().appointUser(securityService.user());
        fetchMaterial(changeVscnMaterialForm.getMaterial(), vscn);

        // replace the verifier value
        for (Verifier verifier : vscn.getVerifiers()) {
            if (verifier.match("Material", previousMaterial)) {
                verifier.setValue(changeVscnMaterialForm.getMaterial());
            }
        }
    }

    public void fetchMaterial(String material, Vscn vscn) {
        Map<String, Object> pgsData = pgsService.fetchMaterialSearchUrl(material, 100);
        loadMaterial(pgsData, vscn);
    }

    protected void loadMaterial(Map<String, Object> pgsData, Vscn vscn) {

        List<Map<String, Object>> items = (List<Map<String, Object>>) pgsData.get("items");
        if (items == null) return;

        items.stream()
                .filter(item -> "Device".equals(item.get("type")))
                .map(item -> {
                    Map<String, Object> rels = (Map<String, Object>) item.get("rels");
                    if (rels == null) return new ArrayList<Map<String, Object>>();

                    Map<String, Object> materialDevice = (Map<String, Object>) rels.get("MaterialDevice");
                    if (materialDevice == null) return new ArrayList<Map<String, Object>>();

                    List<Map<String, Object>> sources = (List<Map<String, Object>>) materialDevice.get("sources");
                    if (sources == null) return new ArrayList<Map<String, Object>>();
                    return sources;
                })
                .flatMap(Collection::stream)
                .map(source -> source == null ? null : (String) source.get("idRef"))
                .distinct()
                .map(id -> {
                    Map<String, Object> metadata = (Map<String, Object>) pgsData.get("metadata");
                    if (metadata == null) return null;

                    Map<String, Object> refObjects = (Map<String, Object>) metadata.get("refObjects");
                    if (refObjects == null) return null;

                    Map<String, Object> refObject = (Map<String, Object>) refObjects.get(id);
                    if (refObject == null) return null;

                    return (Map<String, Object>) refObject.get("attrs");
                })
                .filter(Objects::nonNull)
                .findFirst()
                .ifPresent(material -> vscn.getMaterial().getObject().putAll(material));
    }

}
