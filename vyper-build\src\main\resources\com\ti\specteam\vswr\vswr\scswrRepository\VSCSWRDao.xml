<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.vswr.scswrRepository.VSCSWRDao">
    <resultMap id="VscswrFormMap" type="com.ti.specteam.vswr.vswr.domain.VSCSWRForm">
        <result property="swrID" column="SWR_ID"/>
        <result property="currentStatus" column="CURRENT_STATUS"/>
        <result property="title" column="TITLE"/>
        <result property="atSite" column="ATSITE"/>
        <result property="sbe1" column="SBE1SITE"/>
        <result property="profitCenter" column="PROFCNTR"/>
        <result property="costCenter" column="COST_CENTER"/>
        <result property="io" column="CHARGE"/>
        <result property="itssID" column="REQUESTOR_EMPID"/>
        <result property="email" column="REQUESTOR_EMAIL"/>
        <result property="name" column="REQUESTOR_NAME"/>
        <result property="phone" column="REQUESTOR_PHONE"/>
        <result property="requestDate" column="REQUEST_DATE"/>
        <result property="sapMaterial" column="DEVICE_NAME"/>
        <result property="sapBaseMaterial" column="PARENT_DEVICE"/>
        <result property="pin" column="PIN"/>
        <result property="pkg" column="PKG"/>
        <result property="specDevice" column="PTSS_TRAVEL"/>
        <result property="purpose" column="COMMENT_PURPOSE"/>
        <result property="generalComment" column="COMMENT_REQ"/>
        <result property="pkgGroup" column="PACKCLASS"/>
        <result property="industrySector" column="MARKET_CATEGORY"/>
        <result property="lineItem" column="LINE_ITEM"/>
        <result property="apl" column="IS_APL"/>
        <result property="mcm" column="IS_MCM"/>
        <result property="iso" column="ISO_VALUE"/>

        <result property="dieLot1" column="DIE_LOT"/>
        <result property="dieName1" column="DIE_NAME"/>
        <result property="dieRev1" column="DIE_REV"/>
        <result property="dieDesignator1" column="DIE_DESIGNATOR"/>
        <result property="dieSize1" column="DIE_SIZE"/>
        <result property="fabCode1" column="FAB_CODE"/>
        <result property="fabTech1" column="FABTECH"/>
        <result property="waferDiameter1" column="WAFER_DIAMETER"/>
        <result property="waferThickness1" column="WAFER_THICKNESS"/>
        <result property="backgrind1" column="BACKGRIND"/>
        <result property="dieLot2" column="DDIE_LOT"/>
        <result property="dieName2" column="DDIENAME"/>
        <result property="dieRev2" column="DDIEREV"/>
        <result property="dieDesignator2" column="DDIEDSG"/>
        <result property="dieSize2" column="DDIESIZE"/>
        <result property="fabCode2" column="DDFABCODE"/>
        <result property="fabTech2" column="DFABTECH"/>
        <result property="waferDiameter2" column="DWAFER_DIAMETER"/>
        <result property="waferThickness2" column="DWAFER_THICKNESS"/>
        <result property="backgrind2" column="DBACKGRIND"/>
        <result property="dieLot3" column="TDIE_LOT"/>
        <result property="dieName3" column="TDIENAME"/>
        <result property="dieRev3" column="TDIEREV"/>
        <result property="dieDesignator3" column="TDIEDSG"/>
        <result property="dieSize3" column="TDIESIZE"/>
        <result property="fabCode3" column="TDFABCODE"/>
        <result property="fabTech3" column="TFABTECH"/>
        <result property="waferDiameter3" column="TWAFER_DIAMETER"/>
        <result property="waferThickness3" column="TWAFER_THICKNESS"/>
        <result property="backgrind3" column="TBACKGRIND"/>
        <result property="dieLot4" column="FODIE_LOT"/>
        <result property="dieName4" column="FODIENAME"/>
        <result property="dieRev4" column="FODIEREV"/>
        <result property="dieDesignator4" column="FODIEDSG"/>
        <result property="dieSize4" column="FODIESIZE"/>
        <result property="fabCode4" column="FODFABCODE"/>
        <result property="fabTech4" column="FOFABTECH"/>
        <result property="waferDiameter4" column="FOWAFER_DIAMETER"/>
        <result property="waferThickness4" column="FOWAFER_THICKNESS"/>
        <result property="backgrind4" column="FOBACKGRIND"/>
        <result property="dieLot5" column="FIDIE_LOT"/>
        <result property="dieName5" column="FIDIENAME"/>
        <result property="dieRev5" column="FIDIEREV"/>
        <result property="dieDesignator5" column="FIDIEDSG"/>
        <result property="dieSize5" column="FIDIESIZE"/>
        <result property="fabCode5" column="FIDFABCODE"/>
        <result property="fabTech5" column="FIFABTECH"/>
        <result property="waferDiameter5" column="FIWAFER_DIAMETER"/>
        <result property="waferThickness5" column="FIWAFER_THICKNESS"/>
        <result property="backgrind5" column="FIBACKGRIND"/>
        <result property="dualDie" column="DUALDIE"/>
        <result property="tripleDie" column="TRIPLEDIE"/>
        <result property="quadDie" column="QUADDIE"/>
        <result property="quintDie" column="QUINTDIE"/>

        <result property="travelerStatus" column="TRAVEL_STAT"/>
        <result property="assemblyReq" column="ASYREQ"/>
        <result property="shipType" column="SHIP_TYPE"/>
        <result property="deliveryNote" column="SBEDELNOTE"/>
        <result property="dateShipped" column="DATESHIP"/>
        <result property="invoice" column="SBEINVOICE"/>
        <result property="qtyToShip" column="GEC_QTY"/>
        <result property="sapWaybill" column="SBEWAYBILL"/>

        <result property="header" column="HEADER1"/>
        <result property="leadFrame" column="LEADFRAMES1"/>
        <result property="moldCompound" column="MOLD_COMPOUND1"/>
        <result property="mountCompound" column="MOUNT_COMPOUND1"/>
        <result property="wire" column="WIRE"/>
        <result property="solder" column="SOLDER"/>
        <result property="lid" column="LID1"/>
        <result property="chipCap" column="CHIPCAP"/>

        <result property="bumpReq" column="BUMPREQ"/>
        <result property="wireDiameter" column="WIRE_DIAMETER"/>
    </resultMap>


    <select id="getLatestLegacyID" resultType="String">
        SELECT SWR_ID
        FROM REQUESTS
        WHERE SWR_ID LIKE #{dateID}
        ORDER BY SWR_ID DESC
    </select>
    
    <select id="getAtSite" resultType="String">
        SELECT AT_SITE
        FROM LIST_BOX
        WHERE PLANTCODE = #{plantCode}
    </select>
    
    <select id="getPlantCode" resultType="String">
        SELECT PLANTCODE
        FROM LIST_BOX
        where AT_SITE = #{atSite}
    </select>

    <select id="getScswrFabCode" resultType="String">
        SELECT FABCODE FROM SCSWR.LIST_BOX
        WHERE FABCODE LIKE #{fabCode} AND ROWNUM &lt;=1
    </select>

    <select id="fetchExistingScswr" resultMap="VscswrFormMap">
        SELECT 
            SWR_ID,
            CURRENT_STATUS,
            TITLE,
            ATSITE,
            SBE1SITE,
            PROFCNTR,
            COST_CENTER,
            CHARGE,
            REQUESTOR_EMPID,
            REQUESTOR_EMAIL,
            REQUESTOR_NAME,
            REQUESTOR_PHONE,
            REQUEST_DATE,
            DEVICE_NAME,
            PARENT_DEVICE,
            PIN,
            PKG,
            PTSS_TRAVEL,
            COMMENT_PURPOSE,
            COMMENT_REQ,

            PACKCLASS,
            MARKET_CATEGORY,
            LINE_ITEM,
            IS_APL,
            IS_MCM,
            ISO_VALUE,

            DIE_LOT,
            DIE_NAME,
            DIE_REV,
            DIE_DESIGNATOR,
            DIE_SIZE,
            SCRIBEWIDTH,
            FAB_CODE,
            FABTECH,
            WAFER_DIAMETER,
            WAFER_THICKNESS,
            BACKGRIND,
            DDIE_LOT,
            DDIENAME,
            DDIEREV,
            DDIEDSG,
            DDIESIZE,
            DSCRIBEWIDTH,
            DDFABCODE,
            DFABTECH,
            DWAFER_DIAMETER,
            DWAFER_THICKNESS,
            DBACKGRIND,
            TDIE_LOT,
            TDIENAME,
            TDIEREV,
            TDIEDSG,
            TDIESIZE,
            TSCRIBEWIDTH,
            TDFABCODE,
            TFABTECH,
            TWAFER_DIAMETER,
            TWAFER_THICKNESS,
            TBACKGRIND,
            FODIE_LOT,
            FODIENAME,
            FODIEREV,
            FODIEDSG,
            FODIESIZE,
            FOSCRIBEWIDTH,
            FODFABCODE,
            FOFABTECH,
            FOWAFER_DIAMETER,
            FOWAFER_THICKNESS,
            FOBACKGRIND,
            FIDIE_LOT,
            FIDIENAME,
            FIDIEREV,
            FIDIEDSG,
            FIDIESIZE,
            FISCRIBEWIDTH,
            FIDFABCODE,
            FIFABTECH,
            FIWAFER_DIAMETER,
            FIWAFER_THICKNESS,
            FIBACKGRIND,
            DUALDIE,
            TRIPLEDIE,
            QUADDIE,
            QUINTDIE,

            TRAVEL_STAT,
            ASYREQ,
            SHIP_TYPE,
            SBEDELNOTE,
            DATESHIP,
            SBEINVOICE,
            GEC_QTY,
            SBEWAYBILL,

            HEADER1,
            LEADFRAMES1,
            MOLD_COMPOUND1,
            MOUNT_COMPOUND1,
            WIRE,
            SOLDER,
            LID1,
            CHIPCAP,

            BUMPREQ,
            WIRE_DIAMETER
        FROM REQUESTS
        WHERE SWR_ID = #{swrID}
    </select>

    <insert id="addNewScswrHistoryRecord">
        INSERT INTO HISTORY_NEW (
            RECORD_ID,
            SWR_ID,
            STATUS,
            REASON,
            START_DTTM,
            STARTED_BY,
            END_DTTM,
            ENDED_BY
        )
        VALUES (
            RECORD_SEQ.nextVal,
            #{swrID},
            'Saved',
            #{reason},
            SYSDATE,
            #{aid},
            SYSDATE,
            #{aid}
        )
    </insert>

    <insert id="addScswrHistoryRecordForVyper">
        INSERT INTO HISTORY_NEW (
            RECORD_ID,
            SWR_ID,
            STATUS,
            START_DTTM,
            STARTED_BY
        )
        VALUES (
            RECORD_SEQ.nextVal,
            #{swrID},
            #{status},
            SYSDATE,
            #{startedBy}
        )
    </insert>

    <insert id="addNewScswr">
        INSERT INTO REQUESTS (
            SWR_ID,
            CURRENT_STATUS,
            TITLE,
            ATSITE,
            SBE1SITE,
            PROFCNTR,
            COST_CENTER,
            CHARGE,
            REQUESTOR_EMPID,
            REQUESTOR_EMAIL,
            REQUESTOR_NAME,
            REQUESTOR_PHONE,
            REQUEST_DATE,
            DEVICE_NAME,
            PARENT_DEVICE,
            PIN,
            PKG,
            PTSS_TRAVEL,
            COMMENT_PURPOSE,
            COMMENT_REQ,
            PACKCLASS,
            MARKET_CATEGORY,
            LINE_ITEM,
            IS_MCM,
            ISO_VALUE,

            DIE_LOT,
            DIE_NAME,
            DIE_REV,
            DIE_DESIGNATOR,
            DIE_SIZE,
            SCRIBEWIDTH,
            FAB_CODE,
            FABTECH,
            WAFER_DIAMETER,
            WAFER_THICKNESS,
            BACKGRIND,
            DDIE_LOT,
            DDIENAME,
            DDIEREV,
            DDIEDSG,
            DDIESIZE,
            DSCRIBEWIDTH,
            DDFABCODE,
            DFABTECH,
            DWAFER_DIAMETER,
            DWAFER_THICKNESS,
            DBACKGRIND,
            TDIE_LOT,
            TDIENAME,
            TDIEREV,
            TDIEDSG,
            TDIESIZE,
            TSCRIBEWIDTH,
            TDFABCODE,
            TFABTECH,
            TWAFER_DIAMETER,
            TWAFER_THICKNESS,
            TBACKGRIND,
            FODIE_LOT,
            FODIENAME,
            FODIEREV,
            FODIEDSG,
            FODIESIZE,
            FOSCRIBEWIDTH,
            FODFABCODE,
            FOFABTECH,
            FOWAFER_DIAMETER,
            FOWAFER_THICKNESS,
            FOBACKGRIND,
            FIDIE_LOT,
            FIDIENAME,
            FIDIEREV,
            FIDIEDSG,
            FIDIESIZE,
            FISCRIBEWIDTH,
            FIDFABCODE,
            FIFABTECH,
            FIWAFER_DIAMETER,
            FIWAFER_THICKNESS,
            FIBACKGRIND,
            DUALDIE,
            TRIPLEDIE,
            QUADDIE,
            QUINTDIE,

            TRAVEL_STAT,
            ASYREQ,
            SHIP_TYPE,
            SBEDELNOTE,
            DATESHIP,
            SBEINVOICE,
            GEC_QTY,
            SBEWAYBILL,

            HEADER1,
            LEADFRAMES1,
            MOLD_COMPOUND1,
            MOUNT_COMPOUND1,
            WIRE,
            SOLDER,
            LID1,
            CHIPCAP,
            BUMPREQ,
            WIRE_DIAMETER
        )
        VALUES (
            #{vscswrForm.swrID, jdbcType=VARCHAR},
            #{vscswrForm.currentStatus, jdbcType=VARCHAR},
            #{vscswrForm.title, jdbcType=VARCHAR},
            #{vscswrForm.atSite, jdbcType=VARCHAR},
            #{vscswrForm.sbe1, jdbcType=VARCHAR},
            #{vscswrForm.profitCenter, jdbcType=VARCHAR},
            #{vscswrForm.costCenter, jdbcType=VARCHAR},
            #{vscswrForm.io, jdbcType=VARCHAR},
            #{vscswrForm.itssID, jdbcType=VARCHAR},
            #{vscswrForm.email, jdbcType=VARCHAR},
            #{vscswrForm.name, jdbcType=VARCHAR},
            #{vscswrForm.phone, jdbcType=VARCHAR},
            TO_DATE(#{vscswrForm.requestDate, jdbcType=VARCHAR}, 'yyyy-mm-dd hh24:mi:ss'),
            #{vscswrForm.sapMaterial, jdbcType=VARCHAR},
            #{vscswrForm.sapBaseMaterial, jdbcType=VARCHAR},
            #{vscswrForm.pin, jdbcType=VARCHAR},
            #{vscswrForm.pkg, jdbcType=VARCHAR},
            #{vscswrForm.specDevice, jdbcType=VARCHAR},
            #{vscswrForm.purpose, jdbcType=VARCHAR},
            #{vscswrForm.generalComment, jdbcType=VARCHAR},
            #{vscswrForm.pkgGroup, jdbcType=VARCHAR},
            #{vscswrForm.industrySector, jdbcType=VARCHAR},
            #{vscswrForm.lineItem, jdbcType=VARCHAR},
            #{vscswrForm.mcm, jdbcType=VARCHAR},
            #{vscswrForm.iso, jdbcType=VARCHAR},

            #{vscswrForm.dieLot1, jdbcType=VARCHAR},
            #{vscswrForm.dieName1, jdbcType=VARCHAR},
            #{vscswrForm.dieRev1, jdbcType=VARCHAR},
            #{vscswrForm.dieDesignator1, jdbcType=VARCHAR},
            #{vscswrForm.dieSize1, jdbcType=VARCHAR},
            #{vscswrForm.scribeWidth1, jdbcType=VARCHAR},
            #{vscswrForm.fabCode1, jdbcType=VARCHAR},
            #{vscswrForm.fabTech1, jdbcType=VARCHAR},
            #{vscswrForm.waferDiameter1, jdbcType=VARCHAR},
            #{vscswrForm.waferThickness1, jdbcType=VARCHAR},
            #{vscswrForm.backgrind1, jdbcType=VARCHAR},
            #{vscswrForm.dieLot2, jdbcType=VARCHAR},
            #{vscswrForm.dieName2, jdbcType=VARCHAR},
            #{vscswrForm.dieRev2, jdbcType=VARCHAR},
            #{vscswrForm.dieDesignator2, jdbcType=VARCHAR},
            #{vscswrForm.dieSize2, jdbcType=VARCHAR},
            #{vscswrForm.scribeWidth2, jdbcType=VARCHAR},
            #{vscswrForm.fabCode2, jdbcType=VARCHAR},
            #{vscswrForm.fabTech2, jdbcType=VARCHAR},
            #{vscswrForm.waferDiameter2, jdbcType=VARCHAR},
            #{vscswrForm.waferThickness2, jdbcType=VARCHAR},
            #{vscswrForm.backgrind2, jdbcType=VARCHAR},
            #{vscswrForm.dieLot3, jdbcType=VARCHAR},
            #{vscswrForm.dieName3, jdbcType=VARCHAR},
            #{vscswrForm.dieRev3, jdbcType=VARCHAR},
            #{vscswrForm.dieDesignator3, jdbcType=VARCHAR},
            #{vscswrForm.dieSize3, jdbcType=VARCHAR},
            #{vscswrForm.scribeWidth3, jdbcType=VARCHAR},
            #{vscswrForm.fabCode3, jdbcType=VARCHAR},
            #{vscswrForm.fabTech3, jdbcType=VARCHAR},
            #{vscswrForm.waferDiameter3, jdbcType=VARCHAR},
            #{vscswrForm.waferThickness3, jdbcType=VARCHAR},
            #{vscswrForm.backgrind3, jdbcType=VARCHAR},
            #{vscswrForm.dieLot4, jdbcType=VARCHAR},
            #{vscswrForm.dieName4, jdbcType=VARCHAR},
            #{vscswrForm.dieRev4, jdbcType=VARCHAR},
            #{vscswrForm.dieDesignator4, jdbcType=VARCHAR},
            #{vscswrForm.dieSize4, jdbcType=VARCHAR},
            #{vscswrForm.scribeWidth4, jdbcType=VARCHAR},
            #{vscswrForm.fabCode4, jdbcType=VARCHAR},
            #{vscswrForm.fabTech4, jdbcType=VARCHAR},
            #{vscswrForm.waferDiameter4, jdbcType=VARCHAR},
            #{vscswrForm.waferThickness4, jdbcType=VARCHAR},
            #{vscswrForm.backgrind4, jdbcType=VARCHAR},
            #{vscswrForm.dieLot5, jdbcType=VARCHAR},
            #{vscswrForm.dieName5, jdbcType=VARCHAR},
            #{vscswrForm.dieRev5, jdbcType=VARCHAR},
            #{vscswrForm.dieDesignator5, jdbcType=VARCHAR},
            #{vscswrForm.dieSize5, jdbcType=VARCHAR},
            #{vscswrForm.scribeWidth5, jdbcType=VARCHAR},
            #{vscswrForm.fabCode5, jdbcType=VARCHAR},
            #{vscswrForm.fabTech5, jdbcType=VARCHAR},
            #{vscswrForm.waferDiameter5, jdbcType=VARCHAR},
            #{vscswrForm.waferThickness5, jdbcType=VARCHAR},
            #{vscswrForm.backgrind5, jdbcType=VARCHAR},
            #{vscswrForm.dualDie, jdbcType=VARCHAR},
            #{vscswrForm.tripleDie, jdbcType=VARCHAR},
            #{vscswrForm.quadDie, jdbcType=VARCHAR},
            #{vscswrForm.quintDie, jdbcType=VARCHAR},

            #{vscswrForm.travelerStatus, jdbcType=VARCHAR},
            #{vscswrForm.assemblyReq, jdbcType=VARCHAR},
            #{vscswrForm.shipType, jdbcType=VARCHAR},
            #{vscswrForm.deliveryNote, jdbcType=VARCHAR},
            TO_DATE(#{vscswrForm.dateShipped, jdbcType=VARCHAR}, 'yyyy-mm-dd'),
            #{vscswrForm.invoice, jdbcType=VARCHAR},
            #{vscswrForm.qtyToShip, jdbcType=VARCHAR},
            #{vscswrForm.sapWaybill, jdbcType=VARCHAR},

            #{vscswrForm.header, jdbcType=VARCHAR},
            #{vscswrForm.leadFrame, jdbcType=VARCHAR},
            #{vscswrForm.moldCompound, jdbcType=VARCHAR},
            #{vscswrForm.mountCompound, jdbcType=VARCHAR},
            #{vscswrForm.wire, jdbcType=VARCHAR},
            #{vscswrForm.solder, jdbcType=VARCHAR},
            #{vscswrForm.lid, jdbcType=VARCHAR},
            #{vscswrForm.chipCap, jdbcType=VARCHAR},
            #{vscswrForm.bumpReq, jdbcType=VARCHAR},
            #{vscswrForm.wireDiameter, jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateExistingScswr">
        UPDATE REQUESTS
        SET
            CURRENT_STATUS      = #{vscswrForm.currentStatus},
            TITLE               = #{vscswrForm.title},
            ATSITE              = #{vscswrForm.atSite},
            <if test="vscswrForm.sbe1 != null">
                SBE1SITE        = #{vscswrForm.sbe1},
            </if>
            PROFCNTR            = #{vscswrForm.profitCenter, jdbcType=VARCHAR},
            COST_CENTER         = #{vscswrForm.costCenter, jdbcType=VARCHAR},
            CHARGE              = #{vscswrForm.io, jdbcType=VARCHAR},
            REQUESTOR_EMPID     = #{vscswrForm.itssID, jdbcType=VARCHAR},
            REQUESTOR_EMAIL     = #{vscswrForm.email, jdbcType=VARCHAR},
            REQUESTOR_NAME      = #{vscswrForm.name, jdbcType=VARCHAR},
            REQUESTOR_PHONE     = #{vscswrForm.phone, jdbcType=VARCHAR},
            REQUEST_DATE        = TO_DATE(#{vscswrForm.requestDate, jdbcType=VARCHAR}, 'yyyy-mm-dd hh24:mi:ss'),
            DEVICE_NAME         = #{vscswrForm.sapMaterial, jdbcType=VARCHAR},
            PARENT_DEVICE       = #{vscswrForm.sapBaseMaterial, jdbcType=VARCHAR},
            PIN                 = #{vscswrForm.pin, jdbcType=VARCHAR},
            PKG                 = #{vscswrForm.pkg, jdbcType=VARCHAR},
            PTSS_TRAVEL         = #{vscswrForm.specDevice, jdbcType=VARCHAR},
            COMMENT_PURPOSE     = #{vscswrForm.purpose, jdbcType=VARCHAR},
            COMMENT_REQ         = #{vscswrForm.generalComment, jdbcType=VARCHAR},
            PACKCLASS           = #{vscswrForm.pkgGroup, jdbcType=VARCHAR},
            MARKET_CATEGORY     = #{vscswrForm.industrySector, jdbcType=VARCHAR},
            LINE_ITEM           = #{vscswrForm.lineItem, jdbcType=VARCHAR},
            IS_MCM              = #{vscswrForm.mcm, jdbcType=VARCHAR},
            ISO_VALUE           = #{vscswrForm.iso, jdbcType=VARCHAR},

            DIE_LOT             = #{vscswrForm.dieLot1, jdbcType=VARCHAR},
            DIE_NAME            = #{vscswrForm.dieName1, jdbcType=VARCHAR},
            DIE_REV             = #{vscswrForm.dieRev1, jdbcType=VARCHAR},
            DIE_DESIGNATOR      = #{vscswrForm.dieDesignator1, jdbcType=VARCHAR},
            DIE_SIZE            = #{vscswrForm.dieSize1, jdbcType=VARCHAR},
            SCRIBEWIDTH         = #{vscswrForm.scribeWidth1, jdbcType=VARCHAR},
            FAB_CODE            = #{vscswrForm.fabCode1, jdbcType=VARCHAR},
            FABTECH             = #{vscswrForm.fabTech1, jdbcType=VARCHAR},
            WAFER_DIAMETER      = #{vscswrForm.waferDiameter1, jdbcType=VARCHAR},
            WAFER_THICKNESS     = #{vscswrForm.waferThickness1, jdbcType=VARCHAR},
            BACKGRIND           = #{vscswrForm.backgrind1, jdbcType=VARCHAR},
            DDIE_LOT            = #{vscswrForm.dieLot2, jdbcType=VARCHAR},
            DDIENAME            = #{vscswrForm.dieName2, jdbcType=VARCHAR},
            DDIEREV             = #{vscswrForm.dieRev2, jdbcType=VARCHAR},
            DDIEDSG             = #{vscswrForm.dieDesignator2, jdbcType=VARCHAR},
            DDIESIZE            = #{vscswrForm.dieSize2, jdbcType=VARCHAR},
            DSCRIBEWIDTH        = #{vscswrForm.scribeWidth2, jdbcType=VARCHAR},
            DDFABCODE           = #{vscswrForm.fabCode2, jdbcType=VARCHAR},
            DFABTECH            = #{vscswrForm.fabTech2, jdbcType=VARCHAR},
            DWAFER_DIAMETER     = #{vscswrForm.waferDiameter2, jdbcType=VARCHAR},
            DWAFER_THICKNESS    = #{vscswrForm.waferThickness2, jdbcType=VARCHAR},
            DBACKGRIND          = #{vscswrForm.backgrind2, jdbcType=VARCHAR},
            TDIE_LOT            = #{vscswrForm.dieLot3, jdbcType=VARCHAR},
            TDIENAME            = #{vscswrForm.dieName3, jdbcType=VARCHAR},
            TDIEREV             = #{vscswrForm.dieRev3, jdbcType=VARCHAR},
            TDIEDSG             = #{vscswrForm.dieDesignator3, jdbcType=VARCHAR},
            TDIESIZE            = #{vscswrForm.dieSize3, jdbcType=VARCHAR},
            TSCRIBEWIDTH        = #{vscswrForm.scribeWidth3, jdbcType=VARCHAR},
            TDFABCODE           = #{vscswrForm.fabCode3, jdbcType=VARCHAR},
            TFABTECH            = #{vscswrForm.fabTech3, jdbcType=VARCHAR},
            TWAFER_DIAMETER     = #{vscswrForm.waferDiameter3, jdbcType=VARCHAR},
            TWAFER_THICKNESS    = #{vscswrForm.waferThickness3, jdbcType=VARCHAR},
            TBACKGRIND          = #{vscswrForm.backgrind3, jdbcType=VARCHAR},
            FODIE_LOT           = #{vscswrForm.dieLot4, jdbcType=VARCHAR},
            FODIENAME           = #{vscswrForm.dieName4, jdbcType=VARCHAR},
            FODIEREV            = #{vscswrForm.dieRev4, jdbcType=VARCHAR},
            FODIEDSG            = #{vscswrForm.dieDesignator4, jdbcType=VARCHAR},
            FODIESIZE           = #{vscswrForm.dieSize4, jdbcType=VARCHAR},
            FOSCRIBEWIDTH       = #{vscswrForm.scribeWidth4, jdbcType=VARCHAR},
            FODFABCODE          = #{vscswrForm.fabCode4, jdbcType=VARCHAR},
            FOFABTECH           = #{vscswrForm.fabTech4, jdbcType=VARCHAR},
            FOWAFER_DIAMETER    = #{vscswrForm.waferDiameter4, jdbcType=VARCHAR},
            FOWAFER_THICKNESS   = #{vscswrForm.waferThickness4, jdbcType=VARCHAR},
            FOBACKGRIND         = #{vscswrForm.backgrind4, jdbcType=VARCHAR},
            FIDIE_LOT           = #{vscswrForm.dieLot5, jdbcType=VARCHAR},
            FIDIENAME           = #{vscswrForm.dieName5, jdbcType=VARCHAR},
            FIDIEREV            = #{vscswrForm.dieRev5, jdbcType=VARCHAR},
            FIDIEDSG            = #{vscswrForm.dieDesignator5, jdbcType=VARCHAR},
            FIDIESIZE           = #{vscswrForm.dieSize5, jdbcType=VARCHAR},
            FISCRIBEWIDTH       = #{vscswrForm.scribeWidth5, jdbcType=VARCHAR},
            FIDFABCODE          = #{vscswrForm.fabCode5, jdbcType=VARCHAR},
            FIFABTECH           = #{vscswrForm.fabTech5, jdbcType=VARCHAR},
            FIWAFER_DIAMETER    = #{vscswrForm.waferDiameter5, jdbcType=VARCHAR},
            FIWAFER_THICKNESS   = #{vscswrForm.waferThickness5, jdbcType=VARCHAR},
            FIBACKGRIND         = #{vscswrForm.backgrind5, jdbcType=VARCHAR},
            DUALDIE             = #{vscswrForm.dualDie, jdbcType=VARCHAR},
            TRIPLEDIE           = #{vscswrForm.tripleDie, jdbcType=VARCHAR},
            QUADDIE             = #{vscswrForm.quadDie, jdbcType=VARCHAR},
            QUINTDIE            = #{vscswrForm.quintDie, jdbcType=VARCHAR},

            TRAVEL_STAT         = #{vscswrForm.travelerStatus, jdbcType=VARCHAR},
            ASYREQ              = #{vscswrForm.assemblyReq, jdbcType=VARCHAR},
            SHIP_TYPE           = #{vscswrForm.shipType, jdbcType=VARCHAR},
            SBEDELNOTE          = #{vscswrForm.deliveryNote, jdbcType=VARCHAR},
            DATESHIP            = TO_DATE(#{vscswrForm.dateShipped, jdbcType=VARCHAR}, 'yyyy-mm-dd hh24:mi:ss'),
            SBEINVOICE          = #{vscswrForm.invoice, jdbcType=VARCHAR},
            GEC_QTY             = #{vscswrForm.qtyToShip, jdbcType=VARCHAR},
            SBEWAYBILL          = #{vscswrForm.sapWaybill, jdbcType=VARCHAR},

            HEADER1             = #{vscswrForm.header, jdbcType=VARCHAR},
            LEADFRAMES1         = #{vscswrForm.leadFrame, jdbcType=VARCHAR},
            MOLD_COMPOUND1      = #{vscswrForm.moldCompound, jdbcType=VARCHAR},
            MOUNT_COMPOUND1     = #{vscswrForm.mountCompound, jdbcType=VARCHAR},
            WIRE                = #{vscswrForm.wire, jdbcType=VARCHAR},
            SOLDER              = #{vscswrForm.solder, jdbcType=VARCHAR},
            LID1                = #{vscswrForm.lid, jdbcType=VARCHAR},
            CHIPCAP             = #{vscswrForm.chipCap, jdbcType=VARCHAR},
            BUMPREQ             = #{vscswrForm.bumpReq, jdbcType=VARCHAR},
            WIRE_DIAMETER       = #{vscswrForm.wireDiameter, jdbcType=VARCHAR}
        WHERE SWR_ID = #{vscswrForm.swrID}
    </update>

    <insert id="addBomComments" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="bomInfo">
            INTO BOM_INFO
            (SWR_ID, FIELD, BOM_VALUES, BOM_COMMENT, DTTM)
            VALUES
            (#{bomInfo.swrID}, #{bomInfo.bomField}, #{bomInfo.bomValues}, #{bomInfo.bomComments}, #{bomInfo.createDate})
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

</mapper>
