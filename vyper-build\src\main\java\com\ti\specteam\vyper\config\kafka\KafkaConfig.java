package com.ti.specteam.vyper.config.kafka;

import com.ti.specteam.vyper.vscn.model.KafkaAtssTopicResponse;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

@ConditionalOnProperty(value = "app.kafka.enabled")
@Configuration
public class KafkaConfig {
    @Value(value = "${app.kafka.bootstrap-servers}")
    private String bootstrapAddress;

    @Value(value = "${app.kafka.consumer.group-id}")
    private String groupId;

    @Value(value = "${app.kafka.security.protocol}")
    private String securityProtocol;

    @Value(value = "${app.kafka.security.sasl-mechanism}")
    private String securitySaslMechanism;

    @Value(value = "${app.kafka.security.sasl-jaas-config}")
    private String securitySaslJaasConfig;

    @Value(value = "${app.kafka.notification.message}")
    private String notificationMessage;

    private void addSecurityConfigProperties(Map<String, Object> config) {
        config.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
        config.put(SaslConfigs.SASL_MECHANISM, securitySaslMechanism);
        config.put(SaslConfigs.SASL_JAAS_CONFIG, securitySaslJaasConfig);
    }

    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
    @Bean
    public ProducerFactory<String, Object> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                bootstrapAddress);
        configProps.put(
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                StringSerializer.class);
        configProps.put(
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                JsonSerializer.class);
        addSecurityConfigProperties(configProps);
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    @Bean
    public ConsumerFactory<String, KafkaAtssTopicResponse> consumerFactory() {

        JsonDeserializer<KafkaAtssTopicResponse> deserializer = new JsonDeserializer<>(KafkaAtssTopicResponse.class, false);
        deserializer.addTrustedPackages("*");

        Map<String, Object> props = new HashMap<>();
        props.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                bootstrapAddress);
        props.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                groupId);
        addSecurityConfigProperties(props);
        return new DefaultKafkaConsumerFactory<>(props,
                new ErrorHandlingDeserializer<>(new StringDeserializer()),
                new ErrorHandlingDeserializer<>(deserializer)
        );
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, KafkaAtssTopicResponse>
    kafkaListenerContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, KafkaAtssTopicResponse> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        return factory;
    }

    public String getNotificationMessage(){
        return  notificationMessage;
    }
}
