package com.ti.specteam.atssmassupload.exception;

import com.ti.specteam.vyper.vscn.model.AtssFieldError;

import java.util.List;

public class CamsMassUploadException extends RuntimeException{

    private List<AtssFieldError> fieldErrors;
    public CamsMassUploadException(String message) {
        super(message);
    }

    public CamsMassUploadException(String message, List<AtssFieldError> apiErrors){
        super(message);
        fieldErrors = apiErrors;
    }

    public List<AtssFieldError> getFieldErrors(){
        return fieldErrors;
    }
}
