package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.pra.IPraVerifier;
import com.ti.specteam.vyper.verifier.VerifierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ti.specteam.vyper.security.user.UserUtilsService;

import java.util.List;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_REFRESH;

@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshPraAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final VerifierService verifierService;
    private final AuditService auditService;
    private final UserUtilsService userUtilsService;

    // get all the beans that implement the IPraVerifier interface
    private final List<IPraVerifier> praVerifiers;

    public Pra execute(PraNumberForm praNumberForm) {
        log.debug("execute(praNumberForm:{})", praNumberForm);

        // grab the objects
        Vyper vyper = vyperService.fetchVyper(praNumberForm);
        Pra pra = praService.fetchPra(praNumberForm);

        // validate current user can do this
        userUtilsService.validateUserByFacility(pra.getFacility().getPdbFacility());
        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, pra);

        execute(vyper, pra);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_REFRESH,
                "refresh pra");

        return praService.savePra(pra);
    }

    public void execute(Vyper vyper, Pra pra) {

        // initialize the verifiers
        verifierService.initializeVerifiers(pra);

        // run the verifiers
        praVerifiers.parallelStream().forEach(iPraVerifier -> iPraVerifier.verify(vyper, pra));
    }

}
