package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.flow.FlowService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.SubFlow;
import com.ti.specteam.vyper.build.model.SystemName;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfig;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfigService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_PACK_CONFIG;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangePackConfigAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final BomTemplateLoader bomTemplateLoader;
    private final FlowLoader flowLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final FlowService flowService;
    private final FlowPackConfigService flowPackConfigService;
    private final PackLoader packLoader;

    public Build execute(ChangePackConfigForm changePackConfigForm) {
        log.debug("execute(changePackConfigForm:{})", changePackConfigForm);

        Vyper vyper = vyperService.fetchVyper(changePackConfigForm);
        Build build = buildService.fetchBuild(changePackConfigForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        if(build.getBuildFlow().getFlowId() == null || build.getBuildFlow().getFlowName().equalsIgnoreCase("TKY")){
            build.getPackConfig().getObject().put("value", changePackConfigForm.getValue());
            build.getPackConfig().getSource().appointUser(securityService.user());
        }else{
            List<FlowPackConfig> flowPackConfigs = flowPackConfigService.getAllPackConfigByFlowId(build.getBuildFlow().getFlowId());
            List<String> packConfigList =  flowPackConfigs.stream().map(flowPackConfig -> flowPackConfig.getPackConfig().getValue().toString()).collect(Collectors.toList());
            if(packConfigList.indexOf(changePackConfigForm.getValue()) > -1){
                build.getPackConfig().getObject().put("value", changePackConfigForm.getValue());
                build.getPackConfig().getSource().appointSystem(SystemName.PGS);
            }else{
                throw new VyperException("Pack Config is invalid for the current flow");
            }
        }

        // clear the existing pack
        flowService.removeOperationsBySubFlow(build, SubFlow.PACK);

        bomTemplateLoader.load(vyper, build);
        packLoader.load(vyper, build);
        flowLoader.load(vyper, build);
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_PACK_CONFIG,
                "changed pack config to: " + changePackConfigForm.getValue()
        );

        return buildService.saveBuild(build);
    }

}
