package com.ti.specteam.atssmassupload.web;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.ProjectEntity;
import com.ti.specteam.atssmassupload.domain.ProjectHeader;
import com.ti.specteam.atssmassupload.domain.ProjectHeaderForm;
import com.ti.specteam.atssmassupload.service.ProjectDeviceService;
import com.ti.specteam.atssmassupload.service.ProjectHeaderService;
import com.ti.specteam.vyper.atss.facility.AtssFacilityService;
import com.ti.specteam.vyper.atss.facility.FacilityAndPlantCode;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.vscn.actions.CreateVscnForm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("unused")
@RestController
@RequestMapping("/v1/atssmassupload")
@CrossOrigin(origins = { "*" })
@Slf4j
@RequiredArgsConstructor
public class ProjectHeaderController {

    private final ProjectHeaderService projectHeaderService;
    private final AtssFacilityService atssFacilityService;
    private final UserUtilsService userUtilsService;
    private final ProjectDeviceService projectDeviceService;

    @GetMapping("/projects")
    public ResponseEntity<Page<ProjectHeader>> massTravelerProjectHeaders(
            Pageable pageable,
            @RequestParam(name = "filter", required = false) List<String> filters) {

        log.info("atssmassuploadProject(pageable:{}, filters:{})", pageable, filters);
        userUtilsService.validateUser();
        return ResponseEntity.ok(projectHeaderService.search(pageable, filters).getPage());

    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/projects")
    public ResponseEntity<ProjectEntity> createProject(@Valid @RequestBody ProjectHeaderForm projectHeaderForm) {
        log.info("createProject(projectHeaderForm:{})", projectHeaderForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(projectHeaderService.create(projectHeaderForm));
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/projects/{projNumber}")
    public ResponseEntity<ProjectEntity> editProject(@PathVariable("projNumber") String projNumber,
            @Valid @RequestBody ProjectHeaderForm projectHeaderForm) {
        log.info("editProject(projectHeaderForm:{})", projectHeaderForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(projectHeaderService.update(projNumber, projectHeaderForm));
    }

    @GetMapping("/facilities")
    public ResponseEntity<List<FacilityAndPlantCode>> allFacilities() {
        log.info("allFacilities()");
        userUtilsService.validateUser();
        return ResponseEntity.ok(atssFacilityService.plantcodes());
    }

    @GetMapping("/project/{projId}")
    public ResponseEntity<ProjectHeader> getProjectHeaderById(@PathVariable @NotNull String projId) {
        log.info("getProjectHeaderById()");
        userUtilsService.validateUser();
        return ResponseEntity.ok(projectHeaderService.getProjectHeaderById(projId));
    }

    @GetMapping("/project/projNumber/{projNumber}")
    public ResponseEntity<ProjectHeader> getProjectHeaderbyProjNumber(@PathVariable @NotNull String projNumber){
        log.info("getProjectHeaderByProjNumber()");
        userUtilsService.validateUser();
        return ResponseEntity.ok(projectHeaderService.getProjectHeaderByProjNumber(projNumber));
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/project/createPra")
    public ResponseEntity<ProjectHeader> createPraForRefTraveler( @Valid @RequestBody ProjectHeader projectHeader){
        log.info("createPraForRefTraveler(projectHeader:{})",projectHeader);
        return ResponseEntity.ok(projectHeaderService.createPraForRefTraveler(projectHeader));
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/project/projNumber/{projNumber}/workflow")
    public ResponseEntity<ProjectEntity> changeWorkFlow(@PathVariable @NotNull String projNumber, @RequestBody(required = false) HashMap<String, Object> bodyJson) {
        log.info("changeWorkFlow(json:{})", bodyJson);

        String projectStatus = (String) bodyJson.get("status");

        return ResponseEntity.ok(projectHeaderService.changeWorkFlow(projNumber, projectStatus));
    }
    @PostMapping("/atApproveCallBack")
    public void atApproveCallBack(@RequestBody(required = false) HashMap<String, Object> taskJson) {
        log.info("atApproveCallBack(json:{})", taskJson);

        String projectNumber = (String) taskJson.get("taskName");
        String taskState = (String) taskJson.get("stateName");

        projectHeaderService.changeWorkFlow(projectNumber, taskState);
    }

    @PreAuthorize("@externalAuthCheck.validateUser()")
    @PostMapping("/project/{projNumber}/createVscns")
    public ResponseEntity<List<String>> createProjectVscns(@PathVariable @NotNull String projNumber, @Valid @RequestBody CreateVscnForm vscnForm){
        log.info("createProjectVscns(projNumber:{},vscnForm:{})",projNumber,vscnForm);
        userUtilsService.validateUser();
        return ResponseEntity.ok(projectHeaderService.createProjectVscns(projNumber, vscnForm));
    }

    @PostMapping("project/{projNumber}/devices/refreshScnStatus")
    public ResponseEntity<List<ProjectDevice>> refreshDeviceScnStatusByProject(@RequestBody @NotNull Map<String,Object> request){
        return ResponseEntity.ok(projectDeviceService.refreshDeviceStatusByProject(request.get("projId").toString()));
    }
}
