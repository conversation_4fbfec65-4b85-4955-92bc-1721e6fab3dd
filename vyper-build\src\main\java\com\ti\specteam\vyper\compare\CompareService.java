package com.ti.specteam.vyper.compare;

import difflib.Delta;
import difflib.DiffUtils;
import difflib.Patch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CompareService {
    List<String> traveler1Lines;
    List<String> traveler2Lines;

    class MatchIndex {
        int idx1;
        int idx2;
        MatchIndex() {}
        MatchIndex(int i, int j) {
            idx1 = i;
            idx2 = j;
        }
    }

    public List<DifferenceLines> compare(String traveler1, String traveler2) {
        log.debug("compare(traveler1:{} chars, traveler2:{} chars)", traveler1.length(), traveler2.length());

        return compareTravelerByTraveler(traveler1, traveler2);

//        Patch<String> patch = DiffUtils.diff(traveler1Lines, traveler2Lines);
//
//        int originalPos = 0;
//        int revisedPos = 0;
//
//        List<Difference> differences = new ArrayList<>();
//
//        for(Delta<String> delta : patch.getDeltas()) {
//
//            while (originalPos < delta.getOriginal().getPosition()) {
//                differences.add(new Difference(
//                        originalPos,
//                        revisedPos,
//                        DifferenceType.NONE,
//                        traveler1Lines.get(originalPos)));
//
//                originalPos++;
//                revisedPos++;
//            }
//
//            for(String it : delta.getOriginal().getLines()) {
//                differences.add(new Difference(
//                        originalPos++,
//                        null,
//                        DifferenceType.REMOVE,
//                        it));
//            }
//
//            for(String it : delta.getRevised().getLines()) {
//                differences.add(new Difference(
//                        null,
//                        revisedPos++,
//                        DifferenceType.ADD,
//                        it));
//            }
//
//        }
//
//        while (originalPos < traveler1Lines.size()) {
//            differences.add(new Difference(
//                    originalPos,
//                    revisedPos,
//                    DifferenceType.NONE,
//                    traveler1Lines.get(originalPos)));
//
//            originalPos++;
//            revisedPos++;
//        }
//
//        return differences;
    }

    public List<DifferenceLines> compareTravelerByTraveler(String traveler1, String traveler2) {
        this.traveler1Lines = Arrays.asList(traveler1.split("\\r?\\n"));
        this.traveler2Lines = Arrays.asList(traveler2.split("\\r?\\n"));

        List<DifferenceLines> differenceLines = new ArrayList<DifferenceLines>();

        matchLinesByOperationFirst(differenceLines);


        return differenceLines;
    }

    private void matchLinesByOperationFirst(List<DifferenceLines> differenceLines) {

        List<Integer> operationsIdxTrav1 = getOperationIndexInTraveler(traveler1Lines);
        List<Integer> operationsIdxTrav2 = getOperationIndexInTraveler(traveler2Lines);

        addLinesAsNoDifferenceBeforeFirstOperation(traveler1Lines, traveler2Lines, differenceLines, operationsIdxTrav1.get(0), operationsIdxTrav2.get(0));
        int i, j;
        for (i = 0, j = 0; i < operationsIdxTrav1.size() - 1 && j < operationsIdxTrav2.size() - 1; ) {
            int opIdx1 = operationsIdxTrav1.get(i);
            int opIdx2 = operationsIdxTrav2.get(j);
            int nextOp1Idx = operationsIdxTrav1.get(i + 1);
            int nextOp2Idx = operationsIdxTrav2.get(j + 1);
            String operation1 = traveler1Lines.get(opIdx1).replaceAll("\\s+$", "");
            String operation2 = traveler2Lines.get(opIdx2).replaceAll("\\s+$", "");
            if (operation1.equalsIgnoreCase(operation2)) {
                // Case 1: operation matches
                // add the matched line
                differenceLines.add(new DifferenceLines(operation1, operation2, DifferenceType.NONE, differenceLines.size()));
                // Check if components matches if not find the next match

                List<Integer> comp1 = loadComponentIdxFromOperation(traveler1Lines, opIdx1, nextOp1Idx);
                List<Integer> comp2 = loadComponentIdxFromOperation(traveler2Lines, opIdx2, nextOp2Idx);
                matchLinesByComponentFirst(comp1, comp2, differenceLines);
                i += 1;j += 1;
            } else {
                MatchIndex matchIndex = findNextMatchInList(operationsIdxTrav1.subList(0, operationsIdxTrav1.size() - 1).stream().map(traveler1Lines::get).collect(Collectors.toList()), operationsIdxTrav2.subList(0, operationsIdxTrav2.size() - 1).stream().map(traveler2Lines::get).collect(Collectors.toList()), i, j);
                if(matchIndex == null) {
                    matchIndex = new MatchIndex(operationsIdxTrav1.size() - 1, operationsIdxTrav2.size() - 1);
                }
                i = matchIndex.idx1;
                j = matchIndex.idx2;
                nextOp1Idx = operationsIdxTrav1.get(i);
                nextOp2Idx = operationsIdxTrav2.get(j);
                traveler1Lines.subList(opIdx1, nextOp1Idx).forEach(line -> differenceLines.add(new DifferenceLines(line, "", DifferenceType.REMOVE, differenceLines.size())));
                traveler2Lines.subList(opIdx2, nextOp2Idx).forEach(line -> differenceLines.add(new DifferenceLines("", line, DifferenceType.ADD, differenceLines.size())));
            }
        }
        if(i < operationsIdxTrav1.size() - 1) {
            traveler1Lines.subList(operationsIdxTrav1.get(i), operationsIdxTrav1.get(operationsIdxTrav1.size() - 1)).forEach(line -> differenceLines.add(new DifferenceLines(line, "", DifferenceType.REMOVE, differenceLines.size())));
        }
        if(j < operationsIdxTrav2.size() - 1) {
            traveler2Lines.subList(operationsIdxTrav2.get(j), operationsIdxTrav2.get(operationsIdxTrav2.size() - 1)).forEach(line -> differenceLines.add(new DifferenceLines("", line, DifferenceType.ADD, differenceLines.size())));
        }
    }

    private void matchLinesByComponentFirst(List<Integer> comp1, List<Integer> comp2, List<DifferenceLines> differenceLines) {
        int i, j;
        for (i = 0, j = 0; i < comp1.size() - 1 && j < comp2.size() - 1; ) {
            int compIdx1 = comp1.get(i);
            int compIdx2 = comp2.get(j);
            int nextComp1Idx = comp1.get(i + 1);
            int nextComp2Idx = comp2.get(j + 1);

            String component1 = traveler1Lines.get(compIdx1).replaceAll("\\s+$", "");
            String component2 = traveler2Lines.get(compIdx2).replaceAll("\\s+$", "");
            if (component2.equals(component1)) {
                // add matches line
                differenceLines.add(new DifferenceLines(traveler1Lines.get(compIdx1), traveler2Lines.get(compIdx2), DifferenceType.NONE, differenceLines.size()));
                // Match Attributes
                List<Integer> attr1 = loadAttributeIdxFromComponent(traveler1Lines, compIdx1, nextComp1Idx);
                List<Integer> attr2 = loadAttributeIdxFromComponent(traveler2Lines, compIdx2, nextComp2Idx);
                matchLinesByAttributeFirst(attr1, attr2, differenceLines);
                i++;j++;
            }else if (checkIfComponentValueChanged(component1, component2)) {
                differenceLines.add(new DifferenceLines(component1, component2, DifferenceType.MODIFIED, differenceLines.size()));
                // Match Attributes
                List<Integer> attr1 = loadAttributeIdxFromComponent(traveler1Lines, compIdx1 + 1, nextComp1Idx);
                List<Integer> attr2 = loadAttributeIdxFromComponent(traveler2Lines, compIdx2 + 1, nextComp2Idx);
                matchLinesByAttributeFirst(attr1, attr2, differenceLines);
                i++;j++;
            }else {
                // find next match in components
                MatchIndex matchIndex = findNextMatchInComponentsList(comp1.subList(0, comp1.size() - 1).stream().map(traveler1Lines::get).collect(Collectors.toList()), comp2.subList(0, comp2.size() - 1).stream().map(traveler2Lines::get).collect(Collectors.toList()), i, j);
                if(matchIndex == null) {
                    matchIndex = new MatchIndex(comp1.size() - 1, comp2.size() - 1);
                }
                i = matchIndex.idx1;
                j = matchIndex.idx2;
                nextComp1Idx = comp1.get(i);
                nextComp2Idx = comp2.get(j);
                traveler1Lines.subList(compIdx1, nextComp1Idx).forEach(line -> differenceLines.add(new DifferenceLines(line, "", DifferenceType.REMOVE, differenceLines.size())));
                traveler2Lines.subList(compIdx2, nextComp2Idx).forEach(line -> differenceLines.add(new DifferenceLines("", line, DifferenceType.ADD, differenceLines.size())));
            }
        }
        if(i < comp1.size() - 1) {
            traveler1Lines.subList(comp1.get(i), comp1.get(comp1.size() - 1)).forEach(line -> differenceLines.add(new DifferenceLines(line, "", DifferenceType.REMOVE, differenceLines.size())));
        }
        if(j < comp2.size() - 1) {
            traveler2Lines.subList(comp2.get(j), comp2.get(comp2.size() - 1)).forEach(line -> differenceLines.add(new DifferenceLines("", line, DifferenceType.ADD, differenceLines.size())));
        }
    }

    private void matchLinesByAttributeFirst(List<Integer> attr1, List<Integer> attr2, List<DifferenceLines> differenceLines) {
        int i,j;
        for(i = 0, j = 0; i < attr1.size() - 1  && j < attr2.size() - 1; ){
            int attr1Idx = attr1.get(i);
            int attr2Idx = attr2.get(j);

            String attrLine1 = traveler1Lines.get(attr1Idx).replaceAll("\\s+$", "");
            String attrLine2 = traveler2Lines.get(attr2Idx).replaceAll("\\s+$", "");

            if(attrLine1.equals(attrLine2)) {
                // Matched Attributes
                // add line
                differenceLines.add(new DifferenceLines(attrLine1, attrLine2, DifferenceType.NONE, differenceLines.size()));
                i++;j++;
            }else if (checkIfAttributeValueChanged(attrLine1, attrLine2)){
                differenceLines.add(new DifferenceLines(attrLine1, attrLine2, DifferenceType.MODIFIED, differenceLines.size()));
                i++;j++;
            }
            else {
                // find next match
                MatchIndex matchIndex = findNextMatchInList(attr1.subList(0, attr1.size() - 1).stream().map(traveler1Lines::get).collect(Collectors.toList()), attr2.subList(0, attr2.size() - 1).stream().map(traveler2Lines::get).collect(Collectors.toList()), i, j);
                if(matchIndex == null) {
                    matchIndex = new MatchIndex(attr1.size() - 1, attr2.size() - 1);
                }
                i = matchIndex.idx1;
                j = matchIndex.idx2;
                int nextAttr1Idx = attr1.get(i);
                int nextAttr2Idx = attr2.get(j);
                traveler1Lines.subList(attr1Idx, nextAttr1Idx).forEach(line -> differenceLines.add(new DifferenceLines(line, "", DifferenceType.REMOVE, differenceLines.size())));
                traveler2Lines.subList(attr2Idx, nextAttr2Idx).forEach(line -> differenceLines.add(new DifferenceLines("", line, DifferenceType.ADD, differenceLines.size())));
            }
        }
        if(i < attr1.size() - 1) {
            traveler1Lines.subList(attr1.get(i), attr1.get(attr1.size() - 1)).forEach(line -> differenceLines.add(new DifferenceLines(line, "", DifferenceType.REMOVE, differenceLines.size())));
        }
        if(j < attr2.size() - 1) {
            traveler2Lines.subList(attr2.get(j), attr2.get(attr2.size() - 1)).forEach(line -> differenceLines.add(new DifferenceLines("", line, DifferenceType.ADD, differenceLines.size())));
        }
    }

    private void addLinesAsNoDifferenceBeforeFirstOperation(List<String> traveler1Lines, List<String> traveler2Lines, List<DifferenceLines> differenceLines, Integer idx1,
            Integer idx2) {
        int i = 0, j = 0;
        while (i < idx1 && j < idx2) {
            differenceLines.add(new DifferenceLines(traveler1Lines.get(i), traveler2Lines.get(j), DifferenceType.NONE, differenceLines.size()));
            i++; j++;
        }
        while(i < idx1) {
            differenceLines.add(new DifferenceLines(traveler1Lines.get(i), "", DifferenceType.NONE, differenceLines.size()));
            i++;
        }
        while(j < idx2) {
            differenceLines.add(new DifferenceLines("", traveler2Lines.get(j), DifferenceType.NONE, differenceLines.size()));
            j++;
        }
    }

    private List<Integer> getOperationIndexInTraveler(List<String> traveler1Lines) {
        List<Integer> operationIndex = new ArrayList<>();
        for (int i = 0; i < traveler1Lines.size(); ++i) {
            String line = traveler1Lines.get(i);
            if (checkIfLineIsOperation(line)) {
                operationIndex.add(i);
            }
        }
        operationIndex.add(traveler1Lines.size());
        return operationIndex;
    }

    private MatchIndex findNextMatchInList(List<String> list1, List<String> list2, int idx1, int idx2) {
        // returns the index at which two elements matches
        Map<String, Integer> map2 = new HashMap<String, Integer>();
        MatchIndex matchIndex = null;
        for(int i = idx2; i < list2.size(); ++i) {
            String key = list2.get(i).split(":")[0].trim();
            map2.put(key, i);
        }

        for(int i = idx1; i < list1.size(); ++i) {
            String key = list1.get(i).split(":")[0].trim();
            if(map2.containsKey(key)) {
                matchIndex = new MatchIndex(i, map2.get(key));
                break;
            }
        }
        return matchIndex;
    }
    private MatchIndex findNextMatchInComponentsList(List<String> list1, List<String> list2, int idx1, int idx2) {
        // returns the index at which two elements matches
        Map<String, Integer> map2 = new HashMap<String, Integer>();
        MatchIndex matchIndex = null;
        for(int i = idx2; i < list2.size(); ++i) {
            String[] line = list2.get(i).trim().split(":");
            String priority = line[1].substring(line[1].length() - 2);
            String key = line[0].trim();
            key += priority;
            map2.put(key, i);
        }

        for(int i = idx1; i < list1.size(); ++i) {
            String[] line = list1.get(i).trim().split(":");
            String priority = line[1].substring(line[1].length() - 2);
            String key = line[0].trim();
            key += priority;
            if(map2.containsKey(key)) {
                matchIndex = new MatchIndex(i, map2.get(key));
                break;
            }
        }
        return matchIndex;
    }
    private boolean checkIfLineIsOperation(String line ) {
        return Pattern.matches("^(?! )[A-Za-z-%/0-9 ]+(?<! )$", line.replaceAll("\\s+$", ""));
    }
    private boolean checkIfLineIsComponent(String line) {
        Pattern pattern1 = Pattern.compile("^[\\s]{3}");
        Pattern pattern2 = Pattern.compile("^[\\s]{4}");
        Matcher matcher1 = pattern1.matcher(line);
        Matcher matcher2 = pattern2.matcher(line);

        return matcher1.find() && !matcher2.find() && line.contains(":");
    }
    private List<Integer> loadComponentIdxFromOperation(List<String> travelerLines, int start, int end) {
        List<Integer> list = new ArrayList<>();
        int i = start;
        while(i < end) {
            String line = travelerLines.get(i);
            if(checkIfLineIsComponent(line)) {
                list.add(i);
            }
            i++;
        }
        list.add(end);
        return list;
    }
    private List<Integer> loadAttributeIdxFromComponent(List<String> travelerLines, int start, int end) {
        List<Integer> list = new ArrayList<>();
        int i = start;
        while(i < end) {
            list.add(i);
            i++;
        }
        return list;
    }

    private boolean checkIfComponentValueChanged(String line1, String line2) {
        String[] keyValue1 = line1.split(":");
        String[] keyValue2 = line2.split(":");
        if(keyValue1.length == 2 && keyValue2.length == 2) {
            String priority1 = keyValue1[1].substring(keyValue1[1].length() - 2);
            String priority2 = keyValue2[1].substring(keyValue2[1].length() - 2);
            return keyValue1[0].trim().equals(keyValue2[0].trim()) && priority1.equals(priority2);
        }else {
            return keyValue1[0].equals(keyValue2[0]);
        }
    }
    private boolean checkIfAttributeValueChanged(String line1, String line2) {
        String[] keyValue1 = line1.split(":");
        String[] keyValue2 = line2.split(":");
        if(keyValue1.length == 2 && keyValue2.length == 2) {
            return keyValue1[0].trim().equals(keyValue2[0].trim());
        }else {
            return keyValue1[0].equals(keyValue2[0]);
        }
    }
    private boolean checkIfLineIsAttribute(String line) {
        Pattern pattern = Pattern.compile("^[\\s]{6}");
        Matcher matcher = pattern.matcher(line);

        return matcher.find() || line.isEmpty();
    }

}
