package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_CREATE;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreatePraAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Pra execute(CreatePraForm createPraForm) {
        log.debug("execute(createPraForm:{})", createPraForm);
        Vyper vyper = vyperService.fetchVyper(createPraForm);

        // Check if owner
        validateService.checkOpen(vyper);

        // fetch the build
        Build build = buildService.fetchBuild(createPraForm.getBuildNumber());

        // create, save and return a pra object
        Pra pra = praService.create(vyper, build);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_CREATE,
                "created: " + pra.getPraNumber());

        return pra;
    }

}
