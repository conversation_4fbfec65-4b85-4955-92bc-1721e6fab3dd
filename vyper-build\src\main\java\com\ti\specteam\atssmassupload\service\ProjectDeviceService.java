package com.ti.specteam.atssmassupload.service;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.ProjectDeviceForm;
import com.ti.specteam.atssmassupload.domain.ProjectHeader;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadException;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceRepository;
import com.ti.specteam.vyper.apitraveler.model.ApiTraveler;
import com.ti.specteam.vyper.apitraveler.model.ApiTravelerHeader;
import com.ti.specteam.vyper.bdw.BdwService;
import com.ti.specteam.vyper.build.model.Facility;
import com.ti.specteam.vyper.build.model.Material;
import com.ti.specteam.vyper.build.model.MaterialExtras;
import com.ti.specteam.vyper.config.kafka.KafkaConfig;
import com.ti.specteam.vyper.email.EmailService;
import com.ti.specteam.vyper.email.EmailTableContext;
import com.ti.specteam.vyper.pgs.PgsService;
import com.ti.specteam.vyper.security.buildlink.VyperLinkProperties;
import com.ti.specteam.vyper.security.user.UserService;
import com.ti.specteam.vyper.vscn.model.AtssScnStatus;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import com.ti.specteam.vyper.vscn.model.VscnState;
import com.ti.specteam.vyper.vscn.repository.VscnValidationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.util.*;

@SuppressWarnings("ALL")
@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectDeviceService {

    private final PgsService pgsService;
    private final BdwService bdwService;
    private final ProjectDeviceRepository projectDeviceRepository;
    private final ProjectHeaderService projectHeaderService;
    private final VscnValidationRepository vscnValidationRepository;
    private final VscnService vscnService;
    private final KafkaConfig kafkaConfig;
    private final VyperLinkProperties vyperLinkProperties;
    private final EmailService emailService;
    private final UserService userService;

    public ApiTraveler fillHeaderInformation(ApiTraveler apiTraveler, ProjectDevice projectDevice, String facility){
        apiTraveler.getFacility().clear();
        apiTraveler.getMaterial().clear();
        Material material = getMaterialData(projectDevice.getMaterial(), projectDevice.getSpecDevice());
        apiTraveler.getMaterial().putAll(material.getObject());
        apiTraveler.getFacility().put("PDBFacility",facility);
        apiTraveler.getFacility().put("PlantLoc",facility);
        apiTraveler.setVyperScnNumber(projectDevice.getScnId()== null ? projectDevice.getId(): projectDevice.getVscnNumber());
        apiTraveler.setScnNumber(projectDevice.getScnId());
        apiTraveler.setVyperBuildNumber(projectDevice.getProjectId());// get project Id
        getTravelerHeader(apiTraveler, projectDevice, material, facility);
        return apiTraveler;
    }

    /**
     * To save project device information
     * @param projectDeviceForm
     * @return
     */
    public ProjectDevice saveProjectDevice(ProjectDeviceForm projectDeviceForm){

        ProjectDevice existingProjectDevice = projectDeviceRepository.findById(projectDeviceForm.getId()).orElse(null);
        if ( existingProjectDevice == null){
            throw new AtssMassUploadException("Device ID not found");
        }

        existingProjectDevice.setIsMultiBuild(projectDeviceForm.getIsMultiBuild());
        existingProjectDevice.setCmsNumber(projectDeviceForm.getCmsNumber());
        
        return projectDeviceRepository.save(existingProjectDevice);
    }
    protected Material getMaterialData(String material, String specDevice){

        Material newMaterial = new Material();
        newMaterial.getObject().put("Material", material);
        newMaterial.getObject().put("SpecDevice", specDevice);

        // Load from PGS
        Map<String, Object> pgsData = pgsService.fetchMaterialSearchUrl(material, 100);

        List<Map<String, Object>> items = (List<Map<String, Object>>) pgsData.get("items");
        if (items == null) return newMaterial;

        items.stream()
                .filter(item -> "Device".equals(item.get("type")))
                .map(item -> {
                    Map<String, Object> rels = (Map<String, Object>) item.get("rels");
                    if (rels == null) return new ArrayList<Map<String, Object>>();

                    Map<String, Object> materialDevice = (Map<String, Object>) rels.get("MaterialDevice");
                    if (materialDevice == null) return new ArrayList<Map<String, Object>>();

                    List<Map<String, Object>> sources = (List<Map<String, Object>>) materialDevice.get("sources");
                    if (sources == null) return new ArrayList<Map<String, Object>>();
                    return sources;
                })
                .flatMap(Collection::stream)
                .map(source -> source == null ? null : (String) source.get("idRef"))
                .distinct()
                .map(id -> {
                    Map<String, Object> metadata = (Map<String, Object>) pgsData.get("metadata");
                    if (metadata == null) return null;

                    Map<String, Object> refObjects = (Map<String, Object>) metadata.get("refObjects");
                    if (refObjects == null) return null;

                    Map<String, Object> refObject = (Map<String, Object>) refObjects.get(id);
                    if (refObject == null) return null;

                    return (Map<String, Object>) refObject.get("attrs");
                })
                .filter(Objects::nonNull)
                .findFirst()
                .ifPresent(materialattr -> newMaterial.getObject().putAll(materialattr));

        // Load from BDW
        MaterialExtras me = bdwService.fetchMaterialExtras(material);

        newMaterial.getObject().put("basicMaterial", me.getBasicMaterial());
        newMaterial.getObject().put("niche", me.getNiche());
        newMaterial.getObject().put("presp", me.getPresp());
        newMaterial.getObject().put("industrySector", me.getIndustrySector());

        return newMaterial;
    }

    private Facility getFacilityObjectFromBdw(String facilityAt){
        Facility facility = new Facility();
        facility.getObject().put("PDBFacility", facility);
        return facility;
    }

    private void getTravelerHeader(ApiTraveler apiTraveler, ProjectDevice projectDevice,
                                          Material material, String facilityAt){
        ApiTravelerHeader header = apiTraveler.getHeader();
        header.setId(projectDevice.getId());
        header.setCurrentDateTime(new Date());
        header.setOldMaterial(projectDevice.getOldMaterial());
        header.setSpecDevice(projectDevice.getSpecDevice());
        header.setFacility(facilityAt);
        header.setRevision("WORKING");
        header.setSbe((String) material.getObject().get("SBE"));
        header.setSbe1((String) material.getObject().get("SBE1"));
        header.setSbe2((String) material.getObject().get("SBE2"));
        header.setSapMaterial((String) material.getObject().get("Material"));
        header.setSapBaseName((String) material.getObject().get("basicMaterial"));
        header.setNiche((String)material.getObject().get("niche"));
        header.setPresp((String)material.getObject().get("presp"));
        header.setPin(handleNullInts(material.getObject().get("PackagePin")));
        header.setPkg((String) material.getObject().get("PackageDesignator"));
        header.setPackageGroup((String) material.getObject().get("PackageGroup"));
        header.setSpq(handleNullInts(material.getObject().get("SPQ")));
        header.setMoq(handleNullInts(material.getObject().get("MOQ")));
        header.setIndustrySector((String)material.getObject().get("industrySector"));
        header.setCustomer(null);
        header.setCustPartName(null);
        header.setCustPrintName(null);
        header.setCustPrintRevision(null);
        header.setType4(null);

    }

    private int handleNullInts(Object input) {
        if(null == input) {
            return 0;
        } else {
            return (int)input;
        }
    }

    public List<ProjectDevice> refreshDeviceStatusByProject(String projId){
        projectDeviceRepository.findProjectDeviceByProjectId(projId)
            .stream()
            .filter(device -> device.getScnId() != null &&
                    (device.getStatus().equalsIgnoreCase("ATSS_SUBMITTED") || device.getStatus().equalsIgnoreCase("ATSS_IN_SIGNOFF"))
            )
            .forEach(device -> {
                ProjectDevice updatedDevice = refreshDeviceStatus(device);
                projectDeviceRepository.save(updatedDevice);
            });

        return projectDeviceRepository.findProjectDeviceByProjectId(projId);
    }

    public ProjectDevice refreshDeviceStatus(ProjectDevice projectDevice){
        if ( projectDevice.getScnId() != null){
            AtssScnStatus latestScnStatus = vscnValidationRepository.getScnStatus(Long.valueOf(projectDevice.getScnId()));
            VscnState newState = null;
            if ( latestScnStatus != null){
                if ( latestScnStatus.getScnStatus().startsWith("W") ){
                    newState = VscnState.ATSS_WORKING;
                }else if (latestScnStatus.getScnStatus().startsWith("A") ) {
                    newState = VscnState.ATSS_ACTIVE;
                }else if (latestScnStatus.getScnStatus().startsWith("S")){
                    newState = VscnState.ATSS_IN_SIGNOFF;
                } else{
                    newState = VscnState.ATSS_SUBMITTED;
                }
            }else{
                newState = VscnState.ATSS_SUBMITTED;
            }

            if(newState != null){
                projectDevice.setStatus(newState.name());
            }

            // Get Vscn and update the status
            if ( newState != null && projectDevice.getVscnNumber() != null ){
                Vscn deviceVscn = vscnService.fetchVscn(projectDevice.getVscnNumber());
                deviceVscn.setState(newState);
                vscnService.saveVscn(deviceVscn);
            }

        }

        return projectDevice;
    }

    public void sendScnUpdateEmail(ProjectDevice projectDevice){
        log.debug("sendScnUpdateEmail(specdevice {} scnId {})",projectDevice.getSpecDevice() , projectDevice.getScnId());

        ProjectHeader projectHeader = projectHeaderService.getProjectHeaderByIdForInternal(projectDevice.getProjectId());
        EmailTableContext tableContext = new EmailTableContext();

        // Header And Base changes
        tableContext.setHeader(projectHeader.getProjNumber() + " ATSS Traveler creation request status ");
        tableContext.setSubheader(getSubHeader(projectDevice));
        tableContext.setCallToAction("To view Mass upload project ");
        tableContext.setLink(getProjectLink(projectHeader.getProjNumber(), projectDevice));
        tableContext.setSubject(getSubject(projectHeader.getProjNumber(),projectDevice));
        tableContext.getTos().addAll(getEmailList(projectHeader));

        // Adding Rows
        tableContext.put("Submitted by", projectHeader.getOwnerId());
        tableContext.put("Spec Device", projectDevice.getSpecDevice());
        tableContext.put("Old Material", projectDevice.getOldMaterial());
        tableContext.put("Material", projectDevice.getMaterial());
        tableContext.put("Multi Build", projectDevice.getIsMultiBuild() ? "Y": "N");
        tableContext.put("Status", projectDevice.getStatus());


        try {
            emailService.send(tableContext);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }

    private String getSubject (String muProjectNumber, ProjectDevice projectDevice){
        String message = muProjectNumber +" Notification";
        return kafkaConfig.getNotificationMessage().replaceAll("<message>", message);
    }

    private String getSubHeader ( ProjectDevice projectDevice){
        return "SCN "+projectDevice.getScnId() + " has been created ";
    }

    private String getProjectLink(String muProjectNumber, ProjectDevice projectDevice){
        return vyperLinkProperties.getBaseUrl()+"/atssmassupload/projects/review/"+projectDevice.getProjectId();
    }

    private List<String> getEmailList(ProjectHeader projectHeader){
        List<String> userIdsList = new ArrayList<>();
        userIdsList.add(projectHeader.getOwnerId());

        return userService.findEmailsByUserids(userIdsList);


    }
}
