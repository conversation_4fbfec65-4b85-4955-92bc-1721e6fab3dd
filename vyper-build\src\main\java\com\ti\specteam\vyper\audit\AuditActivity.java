package com.ti.specteam.vyper.audit;

public enum AuditActivity {
    CREATE_VYPER,
    CHANGE_TITLE,
    CHANGE_DESCRIPTION,
    CHANGE_SCSWR_CONTROL_NUMBER,
    <PERSON><PERSON><PERSON>_BUILDTYPE,
    CHANGE_PROJECT_TYPE,
    ADD_OWNER,
    R<PERSON>OVE_OWNER,
    CREATE_BUILD,
    ADD_COMMENT,
    CH<PERSON>GE_DEVICE,
    CH<PERSON>GE_FACILITY,
    CHANGE_ARMARC,
    <PERSON><PERSON><PERSON>_AT<PERSON>,
    <PERSON><PERSON><PERSON>_VYPER,
    CH<PERSON>GE_DIES,
    <PERSON><PERSON><PERSON>_COMPONENT,
    <PERSON>ANGE_MSL,
    CH<PERSON><PERSON>_SYMBOLIZATION,
    <PERSON><PERSON><PERSON>_E<PERSON>,
    <PERSON>AN<PERSON>_<PERSON>CK_CONFIG,
    <PERSON><PERSON><PERSON>_WAFER_SAW_METHOD,

    <PERSON>D_FLOW_OPERATION,
    ADD_TRAVELER_OPERATION,         // depreciated
    REMOVE_FLOW_OPERATION,
    REMOVE_TRAVELER_OPERATION,      // depreciated
    CHANGE_FLOW_OPERATION,
    CHAN<PERSON>_TRAVELER_OPERATION,      // depreciated

    ADD_FLOW_COMPONENT,
    ADD_TRAVELER_COMPONENT,         // depreciated
    REMOVE_FLOW_COMPONENT,
    REMOVE_TRAVELER_COMPONENT,      // depreciated
    CHANGE_FLOW_COMPONENT,
    CHANGE_TRAVELER_COMPONENT,      // depreciated

    CHANGE_SELECTION,
    CHANGE_TURNKEY,
    CHANGE_CHANGELINK_CHANGE,
    CHANGE_CHANGELINK_PCN,
    CHANGE_PKG_NICHE,
    START_SCN,
    UPLOAD_PROGRAM,
    TOGGLE_OPERATION_VALIDATION,
    AUTO_VALIDATE_OPERATIONS,
    CHANGE_DRY_BAKE,
    REFRESH_PGS,
    REFRESH_BOM_TEMPLATE,
    REFRESH_FLOW,

    REVIEW_BOM_TEMPLATE,
    FILL_COMPONENT_VYPER,
    FILL_COMPONENT_ATSS,
    CHANGE_BACKGRIND,
    FILL_COMPONENT_CLEAR,
    CHANGE_OPERATION_COMMENT,
    CHANGE_WORKFLOW,
    AUTO_FINAL_APPROVE,
    ADD_BUILD_COPY,

    RESTORE_FLOW_OPERATION,
    UPDATE_BUILD_ACTION,
    APPROVE_MB_DIAGRAM,

    OVERRIDE_BUILD_STATE,

    // pra activities
    PRA_CREATE,
    PRA_CHANGE_WORKFLOW,
    PRA_CHANGE_DESCRIPTION,
    PRA_ADD_COMMENT,
    PRA_REFRESH,
    PRA_REFRESH_DIES,
    PRA_REFRESH_MB_DIAGRAMS,
    PRA_CHANGE_COMPONENT,
    PRA_TOGGLE_CHECKED,
    PRA_AUTO_CHECK_COMPONENTS,

    // vscn activities
    VSCN_CREATE,
    VSCN_CHANGE_WORKFLOW,
    VSCN_CHANGE_DESCRIPTION,
    VSCN_ADD_COMMENT,
    VSCN_CHANGE_PIM_SETUP_NEEDED_STATE,
    VSCN_CHANGE_PIM_SETUP_VALIDATED_STATE,
    VSCN_CHANGE_COMPONENT,
    VSCN_CHANGE_PACK_CONFIG,
    VSCN_UPLOAD_PROGRAM,
    VSCN_CHANGE_CHANGELINK_CHANGE,
    VSCN_CHANGE_MATERIAL,
    VSCN_CHANGE_SELECTION,
    VSCN_CHANGE_SYMBOLIZATION,
    MASS_UPLOAD_VSCN_CREATE
}
