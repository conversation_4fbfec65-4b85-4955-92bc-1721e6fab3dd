package com.ti.specteam.vyper.actions;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_DESCRIPTION;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.validate.ValidateService;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_SCSWR_CONTROL_NUMBER;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeScswrControlNumberAction {
    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Build execute(ChangeScswrControlNumberForm changeScswrControlNumberForm) {
        log.debug("execute(changeScswrControlNumberForm:{})", changeScswrControlNumberForm);

        Vyper vyper = vyperService.fetchVyper(changeScswrControlNumberForm);
        Build build = buildService.fetchBuild(changeScswrControlNumberForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        execute(vyper, build, changeScswrControlNumberForm);

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_SCSWR_CONTROL_NUMBER,
                "changed SCSWR Control Number to: " + build.getScswrcontrolnumber()
        );

        return buildService.saveBuild(build);
    }

    public Vyper execute(Vyper vyper, Build build, ChangeScswrControlNumberForm changeScswrControlNumberForm) {
        return execute(vyper, build, changeScswrControlNumberForm.getScswrcontrolnumber().trim());
    }

    public Vyper execute(Vyper vyper, Build build, String scswrControlNumber) {
        if (StringUtils.isBlank(scswrControlNumber)) throw new VyperException("The SCSWR Control Number is invalid.");

        build.setScswrcontrolnumber(scswrControlNumber);
        return vyper;
    }    
}
