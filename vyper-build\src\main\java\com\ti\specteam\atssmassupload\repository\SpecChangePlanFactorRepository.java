package com.ti.specteam.atssmassupload.repository;

import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import com.ti.specteam.atssmassupload.domain.SpecChangeMaterialPlanFactors;

import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class SpecChangePlanFactorRepository {

	private final NamedParameterJdbcTemplate jdbcTemplate;

	public List<SpecChangeMaterialPlanFactors> specChangeMaterialFacility(List<String> materials) {
		String sql = "select material,LOCATION from  <EMAIL>\r\n"
				+ "where material IN (:materials)\r\n" + "and location_type = 'A/T'\r\n"
				+ "and stop_build_type is null\r\n"
				+ "order by bld_seq, bld_detail";

		return jdbcTemplate.query(sql, new MapSqlParameterSource("materials", materials),
				BeanPropertyRowMapper.newInstance(SpecChangeMaterialPlanFactors.class));

	};

	public boolean materialSetupInSCSFacility(String material, String facility) {
		String sql = "select 1 from  <EMAIL>\n" +
				"where material = :material\n" +
				"and location = :facility\n" +
				"and location_type = 'A/T'\n" +
				"and stop_build_type is null\n" +
				"and rownum < 2";

		List<String> cValues = jdbcTemplate.query(
				sql,
				new MapSqlParameterSource()
						.addValue("material", material)
						.addValue("facility",facility),
				new SingleColumnRowMapper<>());

		return !cValues.isEmpty();

	};

	public boolean materialSetupInSCSForDie(String material, String die){
		String sql = "Select 1\n" +
				"from <EMAIL>\n" +
				"where plan_item = :plan_item\n" +
				"and Material = :material\n" +
				"and oprtyp <> 'INTRANSIT'\n" +
				"and non_rtp_chip_flag = 'N'";

		List<String> cValues = jdbcTemplate.query(
				sql,
				new MapSqlParameterSource()
						.addValue("material", material)
						.addValue("plan_item",die),
				new SingleColumnRowMapper<>());

		return !cValues.isEmpty();
	}

}