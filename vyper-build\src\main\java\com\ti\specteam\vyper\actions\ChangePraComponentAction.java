package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.atss.attribute.AttributeService;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.ComponentInstance;
import com.ti.specteam.vyper.build.model.ComponentPriority;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.verifier.model.ValidatedComponent;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.ti.specteam.vyper.security.user.UserUtilsService;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_CHANGE_COMPONENT;

import java.util.HashMap;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangePraComponentAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final RefreshPraAction refreshPraAction;
    private final AuditService auditService;
    private final AttributeService attributeService;
    private final UserUtilsService userUtilsService;

    public Pra execute(ChangePraComponentForm changePraComponentsForm) {
        log.debug("execute(changePraComponentsForm:{})", changePraComponentsForm);

        Vyper vyper = vyperService.fetchVyper(changePraComponentsForm);
        Pra pra = praService.fetchPra(changePraComponentsForm);
        String facility = pra.getFacility().getPdbFacility();
        HashMap<String, ValidatedComponent> validatedComponents = pra.getValidatedComponents();
        String componentFormName = changePraComponentsForm.getName();

        userUtilsService.validateUserByFacility(facility);
        validateService.checkOpen(vyper);
        validateService.checkEditable(vyper, pra);

        if(validatedComponents.containsKey(componentFormName)){
            String operation = validatedComponents.get(componentFormName).getParentOperation();
            validateService.checkInGroup(facility, operation);
        }

        // replace the component value
        for (Component component : pra.getComponents()) {
            if (StringUtils.equalsIgnoreCase(component.getName(), changePraComponentsForm.getName())) {
                for (ComponentInstance instance : component.getInstances()) {
                    for (ComponentPriority priority : instance.getPriorities()) {
                        if (StringUtils.equalsIgnoreCase(priority.getValue(), changePraComponentsForm.getOldValue())) {

                            priority.getObject().clear();
                            priority.getObject().put("name", changePraComponentsForm.getNewValue());

                            // add the attributes
                            attributeService.retrieve(pra.getFacility().getPdbFacility(), component, instance, priority);
                        }
                    }
                }
            }
        }

        // replace the verifier value
        for (Verifier verifier : pra.getVerifiers()) {
            if (verifier.match(changePraComponentsForm.getName(), changePraComponentsForm.getOldValue())) {
                verifier.setValue(changePraComponentsForm.getNewValue());
            }
        }

        refreshPraAction.execute(vyper, pra);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_CHANGE_COMPONENT,
                "change component " + changePraComponentsForm.getName() + " from " + changePraComponentsForm.getOldValue() + " to " + changePraComponentsForm.getNewValue());

        return praService.savePra(pra);
    }

}
