package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfig;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfigRepository;
import com.ti.specteam.vyper.packconfig.PackConfig;
import com.ti.specteam.vyper.packconfig.PackConfigRepository;
import com.ti.specteam.vyper.packconfig.PackConfigSelectorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.packconfig.PackSelection.NORMAL;

@Service
@Slf4j
@RequiredArgsConstructor
public class ListPackConfigAction {

    private final BuildService buildService;
    private final PackConfigSelectorService packConfigSelectorService;
    private final PackConfigRepository packConfigRepository;
    private final FlowPackConfigRepository flowPackConfigRepository;

    public List<String> execute(BuildNumberForm buildNumberForm) {
        log.debug("execute(buildNumberForm:{})", buildNumberForm);

        Build build = buildService.fetchBuild(buildNumberForm);

        if(packConfigSelectorService.determine(build) == NORMAL) {
            return normalPackValues();
        }

        return mffPackValues(build);
    }

    /**
     * Return the list of pack config values for turnkey, and final-packed MFF devices.
     *
     * @return The list of values
     */
    public List<String> normalPackValues() {

        return packConfigRepository.findAll()
                .stream()
                .map(PackConfig::getValue)
                .sorted()
                .collect(Collectors.toList());
    }

    /**
     * Return the list of pack config values for non-final MFF packed devices.
     *
     * @param build {@link Build} The build
     * @return The list of values
     */
    public List<String> mffPackValues(Build build) {

        if (build == null || build.getBuildFlow() == null) {
            return Collections.emptyList();
        }

        return mffPackValues(build.getBuildFlow().getFlowId());
    }

    /**
     * Return the list of pack config values for non-final MFF packed devices.
     *
     * @param flowId The id of the pack config flow
     * @return The list of values
     */
    public List<String> mffPackValues(Long flowId) {

        return flowPackConfigRepository.findAllByIdFlowId(flowId)
                .stream()
                .map(FlowPackConfig::getPackConfig)
                .map(PackConfig::getValue)
                .sorted()
                .collect(Collectors.toList());
    }

}
