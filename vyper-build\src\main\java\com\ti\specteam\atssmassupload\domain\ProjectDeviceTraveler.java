package com.ti.specteam.atssmassupload.domain;

import com.ti.specteam.vyper.apitraveler.TravelerVariant;
import com.ti.specteam.vyper.atss.traveler.Traveler;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ProjectDeviceTraveler {
    TravelerMode travelerMode;
    ProjectHeader projectHeader;
    Traveler referenceTraveler;
    List<Traveler> projectDeviceTraveler = new ArrayList<>();
}
