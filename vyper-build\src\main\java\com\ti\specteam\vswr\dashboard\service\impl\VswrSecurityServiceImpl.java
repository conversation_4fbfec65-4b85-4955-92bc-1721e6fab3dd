package com.ti.specteam.vswr.dashboard.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ti.specteam.vswr.dashboard.domain.security.LdapXRefRecord;
import com.ti.specteam.vswr.dashboard.domain.security.VswrRolesContainer;
import com.ti.specteam.vswr.dashboard.domain.security.VswrSecurityRecord;
import com.ti.specteam.vswr.dashboard.repository.VswrSecurityDao;
import com.ti.specteam.vswr.dashboard.service.VswrSecurityService;
import com.ti.specteam.vyper.security.user.UserService;
import com.ti.util.directoryservices.EmployeeDirectory;
import com.ti.util.directoryservices.EmployeeInfo;

@Service
public class VswrSecurityServiceImpl implements VswrSecurityService {
    @Autowired
    VswrSecurityDao vswrSecurityDao;
    @Autowired
    UserService userService;
    @Autowired
    EmployeeDirectory employeeDirectory;

    public VswrRolesContainer getVswrRolesByAid(String aid) {
        VswrSecurityRecord vswrSecurityRecord = vswrSecurityDao.getVswrSecurityRecordByEmpId(aid);
        if (vswrSecurityRecord == null) {
            return this.generateVswrRolesByLdapInformation(aid);
        }
        return this.convertVswrSecurityRecordToVswrRolesContainer(vswrSecurityRecord);
    }

    public VswrRolesContainer convertVswrSecurityRecordToVswrRolesContainer(VswrSecurityRecord vswrSecurityRecord) {
        VswrRolesContainer vswrRolesContainer = new VswrRolesContainer();
        if (vswrSecurityRecord == null) {
            return vswrRolesContainer;
        }
        String siteType = vswrSecurityRecord.getSiteTypeOvr();
        String siteCode = vswrSecurityRecord.getSitecodeOvr();
        String siteExt = vswrSecurityRecord.getSiteExt();
        String administrativeLevel = vswrSecurityRecord.getAdminLevel();

        if (StringUtils.equals(VswrSecurityService.RoleTypes.SBE, siteType)) {
            vswrRolesContainer.setSbeRoles(this.transformSiteCodeAndSiteExtToRolesList(siteType, siteCode, siteExt));
        } else if (StringUtils.equals(VswrSecurityService.RoleTypes.AT, siteType)) {
            vswrRolesContainer.setAtRoles(this.transformSiteCodeAndSiteExtToRolesList(siteType, siteCode, siteExt));
        } else if (StringUtils.equals(VswrSecurityService.RoleTypes.BTH, siteType)) {
            vswrRolesContainer.setSbeRoles(
                    this.transformSiteCodeAndSiteExtToRolesList(VswrSecurityService.RoleTypes.SBE, siteCode, siteExt));
            vswrRolesContainer.setAtRoles(
                    this.transformSiteCodeAndSiteExtToRolesList(VswrSecurityService.RoleTypes.AT, siteCode, siteExt));
        }
        vswrRolesContainer.setRoleCategory(siteType);
        vswrRolesContainer.setAdministrativeLevel(administrativeLevel);
        return vswrRolesContainer;
    }

    private boolean doesSiteExist(String siteType, String site) {
        if (StringUtils.equals(VswrSecurityService.RoleTypes.SBE, siteType)) {
            return vswrSecurityDao.doesSbeExist(site);
        } else if (StringUtils.equals(VswrSecurityService.RoleTypes.AT, siteType)) {
            return vswrSecurityDao.doesAtSiteExist(site);
        } else
            return false;
    }

    private List<String> transformSiteCodeAndSiteExtToRolesList(String siteType, String siteCode, String siteExt) {
        List<String> roles = new ArrayList<String>();
        if (doesSiteExist(siteType, siteCode)) {
            roles.add(siteCode);
        }
        if (siteExt == null) {
            return roles;
        }
        String[] siteExtList = siteExt.split(SITE_EXTENSION_DELIM);
        for (int i = 0; i < siteExtList.length; i++) {
            String siteExtItem = siteExtList[i];
            if (doesSiteExist(siteType, siteExtItem)) {
                roles.add(siteExtItem);
            }
        }
        return roles;
    }

    public VswrRolesContainer generateVswrRolesByLdapInformation(String aid) {
        try {
            String org = employeeDirectory.getEmployeeById(aid).get(EmployeeInfo.ORGANIZATION);
            LdapXRefRecord ldapXRefRecord = vswrSecurityDao.getLdapXRefRecordByOrg(org);
            VswrRolesContainer vswrRolesContainer = new VswrRolesContainer();
            if (ldapXRefRecord == null) {
                return vswrRolesContainer;
            }
            String siteType = ldapXRefRecord.getSitetype();
            vswrRolesContainer.setRoleCategory(siteType);
            if (StringUtils.equals(VswrSecurityService.RoleTypes.SBE, siteType)) {
                vswrRolesContainer.setSbeRoles(List.of(ldapXRefRecord.getSitecode()));
            } else if (StringUtils.equals(VswrSecurityService.RoleTypes.AT, siteType)) {
                vswrRolesContainer.setAtRoles(List.of(ldapXRefRecord.getSitecode()));
            }
            vswrRolesContainer.setRoleCategory(siteType);
            return vswrRolesContainer;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(VswrSecurityService.ErrorTypes.GENERATE_LDAP_ROLES_ERROR, e);
        }
    }
}
