package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Engineering;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class RemoveFlowOperationForm extends BuildNumberForm {

    @NotNull
    @Size(min = 1)
    private String name;

    private int index;

    @NotNull
    private Engineering engineering;
}
