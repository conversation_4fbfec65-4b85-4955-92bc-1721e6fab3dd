package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.util.AuthorizedOperationService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.FILL_COMPONENT_VYPER;

@Service
@Slf4j
@RequiredArgsConstructor
public class FillComponentVyperAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuthorizedOperationService authorizedOperationService;
    private final AuditService auditService;

    public Build execute(FillComponentVyperForm fillComponentVyperForm) {
        log.debug("execute(fillComponentVyperForm:{})", fillComponentVyperForm);

        Vyper vyper = vyperService.fetchVyper(fillComponentVyperForm);
        Build build = buildService.fetchBuild(fillComponentVyperForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        // read the vyper build

        String otherBuildNumber = fillComponentVyperForm.getNumber();
        String otherVyperNumber = Build.convertBuildNumbertoVyperNumber(otherBuildNumber);

        Vyper otherVyper = vyperService.fetchVyper(otherVyperNumber);
        Build otherBuild = buildService.fetchBuild(otherBuildNumber);

        process(vyper, build, otherBuild);

        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // store the fill component record
        build.getFillComponent().reset();
        build.getFillComponent().setMode(FillComponentMode.VYPER);
        build.getFillComponent().setBuildNumber(fillComponentVyperForm.getNumber());
        build.getFillComponent().getSource().appointUser(securityService.user());

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                FILL_COMPONENT_VYPER,
                "filled components via vyper copy: " + fillComponentVyperForm.getNumber()
        );

        return buildService.saveBuild(build);
    }

    /**
     * If the build's selections have empty items, then copy the other builds matching selection items.
     *
     * @param build      {@link Build} The build
     * @param otherBuild {@link Build} The other build
     */
    public void process(Vyper vyper, Build build, Build otherBuild) {
        build.getSelections().stream()
                .filter(Selection::isEmpty)
                .filter(selection -> authorizedOperationService.canUpdateOperation(vyper, build, selection.getOperation()))
                .forEach(selection -> {
                    Selection otherSelection = otherBuild.findSelection(selection.getOperation(), selection.getName());
                    if (null == otherSelection || otherSelection.isEmpty()) return;
                    selection.getItems().addAll(otherSelection.getItems());

                    // code to sync selection screen and build screen for backgrind when user uses fill unselected option
                    if (selection.getName().equals("Backgrind")) {
                        Backgrind backgrind = build.getBackgrind();
                        backgrind.setBackgrindSelected(selection.getItems());
                        backgrind.getSource().getUser().setUserid(securityService.user().getUserid());
                        backgrind.getSource().getUser().setUsername(securityService.user().getUsername());
                    }

                    if (selection.getName().equals("Topside Symbol")) {
                        Symbolization symbolization = build.getSymbolization();
                        Symbol symbol = new Symbol();
                        if (!selection.getItems().isEmpty() && selection.getItems().get(0) != null) {
                            symbol.getObject().setName(selection.getItems().get(0).getValue());
                            symbolization.getSymbols().add(symbol);
                            symbolization.getSymbols().get(0).setSource(Source.ATSS);
                        }
                    }

                    if (build.getDies().getDieInstances().isEmpty() && selection.getName().equals("Die")) {
                        Dies dies = build.getDies();
                        DieInstance dieInstance = new DieInstance();
                        dieInstance.setType("Die");
                        dies.getDieInstances().add(dieInstance);
                        dies.getSource().appointSystem(SystemName.FILL_COMPONENTS);
                        build.setDies(dies);
                        for (SelectionItem selectionItem : selection.getItems()) {
                            Die die = new Die();
                            die.setName(selectionItem.getValue());
                            die.setEngineering(selectionItem.getEngineering());
                            dieInstance.getDies().add(die);
                        }
                    }
                });
    }

}
