package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

import com.ti.specteam.vyper.build.model.Build;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class CompareAtssForm {

    @NotNull
    private String material;

    @NotNull
    private String facility;

    @NotNull
    private String status;

    @NotNull
    private String buildNumber;

    public String getVyperNumber() {
        return (buildNumber == null) ? null : Build.convertBuildNumbertoVyperNumber(buildNumber);
    }

}
