package com.ti.specteam.atssmassupload.repository;

import com.ti.specteam.atssmassupload.domain.ProjectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ProjectEntityRepository extends JpaRepository<ProjectEntity, String> {

    ProjectEntity findByProjNumber(String projNumber);

    @Query(nativeQuery = true, value = "SELECT generate_mu_proj_number() FROM DUAL")
    String generateProjectNumber();

}
