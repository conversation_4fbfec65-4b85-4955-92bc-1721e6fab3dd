package com.ti.specteam.vswr.vswr.service.impl;

import java.util.*;

import com.ti.specteam.vswr.vswr.domain.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ti.specteam.vswr.dashboard.BatchProcessing.SCSWRService.SCSWRService;
import com.ti.specteam.vswr.vswr.repository.ATSWRPostDao;
import com.ti.specteam.vswr.vswr.scswrRepository.VSCSWRDao;
import com.ti.specteam.vswr.vswr.service.VSCSWRService;

import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.sql.Date;

@RequiredArgsConstructor
@Slf4j
@Service
public class VSCSWRServiceImpl implements VSCSWRService{
    final ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    
    private final VSCSWRDao vscswrDao;
    private final ATSWRPostDao atswrPostDao;
    private final SCSWRService scswrService;

    @Value("${scswr.bom.Fields}")
    public List<String> bomFields;

    @Value("${scswr.bom.comments.enable}")
    public boolean commentsEnabled;

    public VSCSWRForm fetchExistingScswr(String swrID){
        VSCSWRForm vf = vscswrDao.fetchExistingScswr(swrID);
        if(vf == null){
            return new VSCSWRForm();
        }
        String atSite = vf.getAtSite();
        vf.setPlantCode(vscswrDao.getPlantCode(atSite));
        return vf;
    }

    private void setMatInfo(ATSWRForm atswrForm, VSCSWRForm vscswrForm){
        log.info("Called setMatInfo from VSCSWRServiceImpl");

        List<DieInfo> dieInfos = atswrForm.getDieInfo();

        if (dieInfos == null || dieInfos.isEmpty()) {
            return;
        }

        DieInfo firstDie = dieInfos.get(0);
        if(firstDie.getDieLots() ==null || firstDie.getDieLots().isEmpty()) {
            return;
        }

        DieLotInfo firstLot = firstDie.getDieLots().get(0);
        vscswrForm.addDieLotInfo(firstLot);
    }

    private void convertDieDiameter(DieInfo dieInfo){
        String scswrWaferDiameter = "";

        if(dieInfo.getWaferDiameter() == null){
            dieInfo.setWaferDiameter(scswrWaferDiameter);
            return;
        }

        switch(dieInfo.getWaferDiameter()){
            case "100":
                scswrWaferDiameter = "100mm(4in)";
                break;
            case "125":
                scswrWaferDiameter = "125mm(5in)";
                break;
            case "150":
                scswrWaferDiameter = "150mm(6in)";
                break;
            case "200":
                scswrWaferDiameter = "200mm(8in)";
                break;
            case "300":
                scswrWaferDiameter = "300mm(12in)";
                break;
        }
        dieInfo.setWaferDiameter(scswrWaferDiameter);
    }

    private void setDieInfo(ATSWRForm atswrForm, VSCSWRForm vscswrForm){
        log.info("Called setDieInfo from VSCSWRServiceImpl");

        if (atswrForm.getDieInfo().isEmpty()){
            return;
        }

        // Find the priority Die with DieLot size
        List<DieInfo> priorityDies = atswrForm.getDieInfo().stream()
                .sorted(Comparator.comparingInt(DieInfo::getPriority))
                .collect(Collectors.toList());

        if (priorityDies.isEmpty()) {
            return;
        }

        int instance = 1;
        for(DieInfo dieInfo : priorityDies){
            //MCM is not yet supported
            if (instance > 1 ){
                continue;
            }

            String dieLotStr = "";
            if (dieInfo.getDieLots() != null && !dieInfo.getDieLots().isEmpty()){
                StringJoiner dieLots = new StringJoiner(";");
                for (DieLotInfo dieLotInfo : dieInfo.getDieLots()) {
                    dieLots.add(dieLotInfo.getDieLot());
                }
                dieLotStr = dieLots.toString();
            }

            String scswrFabCode = vscswrDao.getScswrFabCode(dieInfo.getFabCode()+" %");
            dieInfo.setFabCode(scswrFabCode);
            convertDieDiameter(dieInfo);
            vscswrForm.addDieInfo(instance, dieInfo, dieLotStr);
            instance++;
        }
    }

    private String toTitleCase(String text){
        if (text == null || text.isEmpty()) {
            return text;
        }
    
        StringBuilder sb = new StringBuilder();
        sb.append(text.substring(0, 1).toUpperCase());
        sb.append(text.substring(1).toLowerCase());

        return sb.toString();
    }

    public VSCSWRForm mapVswrToScswr(ATSWRForm atswrForm){
        log.info("Called mapVswrToScswr from VSCSWRServiceImpl");
        GeneralInfo generalInfo = atswrForm.getGeneralInfo();
        String vbuildID = generalInfo.getVbuildID();

        VSCSWRForm vscswrForm1 = mapper.convertValue(generalInfo, VSCSWRForm.class);
        VSCSWRForm vscswrForm2 = mapper.convertValue(atswrForm.getRequestorInfo(), VSCSWRForm.class);
        VSCSWRForm vscswrForm3 = mapper.convertValue(atswrForm.getDeviceInfo(), VSCSWRForm.class);
        String atSite = vscswrDao.getAtSite(generalInfo.getPlant());

        vscswrForm1.setPlantCode(vscswrDao.getPlantCode(atSite));
        vscswrForm1.setAtSite(atSite);
        vscswrForm1.merge(vscswrForm2);
        vscswrForm1.merge(vscswrForm3);
        vscswrForm1.addAssyInfo(atswrForm.getBomInfo());


        // vscswrForm1.addAssyInfo(atswrForm.getPackingMaterial());
        setDieInfo(atswrForm, vscswrForm1);
        setMatInfo(atswrForm, vscswrForm1);
        vscswrForm1.setGeneralComment("[" + vbuildID + "]" + " and of Vyper Build Type :" +  "[" + atswrForm.getGeneralInfo().getBuildType()  + "]" + "\n" +
                "Rules for selecting SWR type based on the Vyper BuildType \n" +
                "1.\tVYPER Type Experimental, can be used for Samples and Process Development only.\n" +
                "2.\tVYPER Type New, can be used for any SWR Type.\n" +
                "3.\tVYPER Type Minor Change, can be used for any SWR Type.\n " );

        if("N".equals(vscswrForm1.getMcm())){
            vscswrForm1.setMcm("");
        }
        vscswrForm1.setIndustrySector(toTitleCase(vscswrForm1.getIndustrySector()));
        vscswrForm1.setWireDiameter(getWireDiameter(atswrForm.getTraveler()));
        return vscswrForm1;
    }

    private String createLegacyID(){
        log.info("Called createLegacyID from VSCSWRServiceImpl");
        //creates first 8 digits of legacy swr id year-month-day -> yearmonthday
        String dateID = java.time.LocalDate.now().toString().replaceAll("-", "");
        List<String> latestIDs = vscswrDao.getLatestLegacyID(dateID+"%");

        if(latestIDs.isEmpty()){
            return dateID + "001";
        }
        //strips date portion of id
        String latest = latestIDs.get(0).substring(8);
        int instance = Integer.parseInt(latest);
        return dateID + String.format("%03d", instance+1);
    }
    
    private boolean isUpdateBlocked(String currentStatus){
        log.info("Called isUpdateBlocked from VSCSWRServiceImpl");

        return (
            currentStatus == null 
            || "Void_Forecast".equals(currentStatus)
            || "Void".equals(currentStatus)
            || "Reject".equals(currentStatus)
            || "Disapprove".equals(currentStatus)
            || "Cost Charge".equals(currentStatus)
            || "SBE_Action_Required".equals(currentStatus)
            || "AT_Action_Required".equals(currentStatus)
            || "SBE_Shipped_Paperwork".equals(currentStatus)
            || "AT_Paperwork_Approved".equals(currentStatus)
            || "AT_Accepted".equals(currentStatus)
            || "AT_Started".equals(currentStatus)
            || "AT_Finished".equals(currentStatus)
            || "AT_Shipped".equals(currentStatus)
            || "MQ_Wait".equals(currentStatus)
            || "SBE_Recvd".equals(currentStatus)
            || "Complete".equals(currentStatus)
        );
    }

    public String isScswrStatusBlockedReason(String swrID){
        log.info("Called isScswrStatusBlockedReason from VSCSWRServiceImpl");
        VSCSWRForm vf = fetchExistingScswr(swrID);
        if(isUpdateBlocked(vf.getCurrentStatus())){
            if (StringUtils.isBlank(vf.getCurrentStatus()))
                return "Error. SCSWR " + swrID + " cannot be processed because it is not a valid SWR that exists in production.";
            else 
                return "Error. SCSWR " + swrID + " cannot be updated because it is currently in " + vf.getCurrentStatus() + " status";
        }
        return "";
    }

    private void addNewScswr(VSCSWRForm vscswrForm){
        log.info("Called addNewScswr from VSCSWRServiceImpl");
        String aid = vscswrForm.getItssID();
        String legacyID = createLegacyID();
        vscswrForm.setSwrID(legacyID);
        vscswrForm.setCurrentStatus("Saved");

        vscswrDao.addNewScswr(vscswrForm);
        vscswrDao.addNewScswrHistoryRecord(legacyID, "Created from VSWR", aid);
        // scswrService.handleSwrStatusUpdate(legacyID, "Created from VSWR", aid, SCSWRService.SWR_UPDATE_ACTION.SAVE_SWR);
    }

    private String updateExistingScswr(VSCSWRForm vscswrForm){
        log.info("Called updateExistingScswr from VSCSWRServiceImpl");
        String swrID = vscswrForm.getSwrID();
        VSCSWRForm existingScswr = vscswrDao.fetchExistingScswr(swrID);
        String generalComment = vscswrForm.getGeneralComment();
        String existingGenComment = existingScswr.getGeneralComment();

        if(existingGenComment!=null){
            if(!existingGenComment.contains("VBUILD")){
                vscswrForm.setGeneralComment(generalComment + existingGenComment);
            }
            else{
                vscswrForm.setGeneralComment(existingGenComment);
            }
        }
        //check status first
        if(isUpdateBlocked(existingScswr.getCurrentStatus())){
            if (StringUtils.isBlank(existingScswr.getCurrentStatus()))
                return "Error. SCSWR " + swrID + " cannot be processed because it is not a valid SWR that exists in production.";
            else 
                return "Error. SCSWR " + swrID + " cannot be updated because it is currently in " + existingScswr.getCurrentStatus() + " status";
        }
        if("WWMAKE".equals(existingScswr.getSbe1())){
            vscswrForm.setSbe1("WWMAKE");
        }
        if(!"Saved".equals(vscswrForm.getCurrentStatus())){
            scswrService.createRequestsHistoryRecordByRequestRecord(swrID);
        }
        vscswrForm.setCurrentStatus(existingScswr.getCurrentStatus());
        vscswrDao.updateExistingScswr(vscswrForm);
        scswrService.handleSwrStatusUpdate(swrID, "Updated from VSWR", vscswrForm.getItssID(), SCSWRService.SWR_UPDATE_ACTION.SAVE_SWR, false);
        if ("AT_Approved_Forecast".equals(vscswrForm.getCurrentStatus()) ||
                "SBE_Forecast_Submitted".equals(vscswrForm.getCurrentStatus()) ||
                "AT_Rejected_Forecast".equals(vscswrForm.getCurrentStatus())) {
            vscswrDao.addScswrHistoryRecordForVyper(swrID, vscswrForm.getCurrentStatus(), "VYPER");            
        }
        return "SCSWR " + swrID + " update successful.";
    }


    public String pushExistingToScswr(ATSWRForm atswrForm){
        log.info("Called pushExistingToScswr from VSCSWRServiceImpl");
        GeneralInfo generalInfo = atswrForm.getGeneralInfo();
        VSCSWRForm vscswrForm = mapVswrToScswr(atswrForm);
        String existingScswrID = generalInfo.getExistingScswrID();

        vscswrForm.setSwrID(existingScswrID);

        return updateExistingScswr(vscswrForm);
    }

    public GeneralInfo pushNewToScswr(ATSWRForm atswrForm){
        log.info("Called pushToScswr from VSCSWRServiceImpl");
        GeneralInfo generalInfo = atswrForm.getGeneralInfo();
        VSCSWRForm vscswrForm = mapVswrToScswr(atswrForm);


        addNewScswr(vscswrForm);
        if(commentsEnabled) {
            addBomComments(vscswrForm);
        }
        //updates new scswr id and status then saves
        generalInfo.setExistingScswrID(vscswrForm.getSwrID());
        generalInfo.setCurrentStatus(vscswrForm.getCurrentStatus());
        generalInfo.setCurrentRequestor(vscswrForm.getName());
        generalInfo.setScswrMaterial(generalInfo.getVbuildMaterial());
        generalInfo.setScswrPlant(generalInfo.getPlant());
        generalInfo.setScswrFacility(vscswrDao.getAtSite(generalInfo.getPlant()));
        generalInfo.setScswrSpecDevice(generalInfo.getSpecDevice());

        atswrPostDao.updateGeneral(atswrForm.getGeneralInfo(), atswrForm.getRequestorInfo());
            return generalInfo;
    }

    private void addBomComments(VSCSWRForm vscswrForm) {
        List<BomInfo> bomInfos = new ArrayList<>();
        String swrID = vscswrForm.getSwrID();
        Date now = new java.sql.Date(System.currentTimeMillis());

        if (vscswrForm.getLeadFrame() != null && !vscswrForm.getLeadFrame().isEmpty()) {
            bomInfos.add(BomInfo.builder()
                    .swrID(swrID)
                    .bomField("Leadframe")
                    .bomValues(vscswrForm.getLeadFrame())
                    .bomComments("For Complete List of Assembly BOM, please refer to the Vyper")
                    .createDate(now)
                    .build());
        }

        if (vscswrForm.getMountCompound() != null && !vscswrForm.getMountCompound().isEmpty()) {
            bomInfos.add(BomInfo.builder()
                    .swrID(swrID)
                    .bomField("MountComp")
                    .bomValues(vscswrForm.getMountCompound())
                    .bomComments("For Complete List of Assembly BOM, please refer to the Vyper")
                    .createDate(now)
                    .build());
        }

        if (vscswrForm.getMoldCompound() != null && !vscswrForm.getMoldCompound().isEmpty()) {
            bomInfos.add(BomInfo.builder()
                    .swrID(swrID)
                    .bomField("MoldComp")
                    .bomValues(vscswrForm.getMoldCompound())
                    .bomComments("For Complete List of Assembly BOM, please refer to the Vyper")
                    .createDate(now)
                    .build());
        }

        if (vscswrForm.getWire() != null && !vscswrForm.getWire().isEmpty()) {
            bomInfos.add(BomInfo.builder()
                    .swrID(swrID)
                    .bomField("Wire")
                    .bomValues(vscswrForm.getWire())
                    .bomComments("For Complete List of Assembly BOM, please refer to the Vyper")
                    .createDate(now)
                    .build());
        }

        if (!bomInfos.isEmpty()) {
            vscswrDao.addBomComments(bomInfos);
        }

    }

    public String getWireDiameter(Object oTraveler) {
        String sWireDiameter = "";  
        ArrayList alSubflows = (ArrayList)((HashMap)oTraveler).get("operations");
        for (int i=0;i<alSubflows.size();i++) {
            String sSubFlowType = (String)((HashMap)alSubflows.get(i)).get("subflowType");
            String sSubFlowName = (String)((HashMap)alSubflows.get(i)).get("name");
            if ("ASSEMBLY".equalsIgnoreCase(sSubFlowType) && "Bond".equalsIgnoreCase(sSubFlowName)) {
                ArrayList alComponents = (ArrayList)((HashMap)alSubflows.get(i)).get("components");
                for(Object oComponent :alComponents) {
                    String sCompName = (String)((HashMap)oComponent).get("name");
                    if ("Wire".equals(sCompName)) {
                        ArrayList alAttributes = (ArrayList)((HashMap)oComponent).get("attributes");
                        if (alAttributes.isEmpty()) {
                            String sCompValue = (String)((HashMap)oComponent).get("value");
                            String[] saCompValue = sCompValue.split(":");
                            sWireDiameter = saCompValue[2];
                        } else {
                            for(Object oAttribute :alAttributes) {
                                if("Wire diameter".equalsIgnoreCase((String)((HashMap)oAttribute).get("name"))) {
                                    sWireDiameter = (String)((HashMap)oAttribute).get("value");
                                }                            
                            }
                        }                        
                    }
                }
            }
        }
        return sWireDiameter;
    }

}