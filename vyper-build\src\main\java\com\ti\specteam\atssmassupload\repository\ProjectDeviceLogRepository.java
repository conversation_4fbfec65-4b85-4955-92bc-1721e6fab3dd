package com.ti.specteam.atssmassupload.repository;

import com.ti.specteam.atssmassupload.entity.ProjectDeviceLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectDeviceLogRepository extends JpaRepository<ProjectDeviceLog, String> {
    List<ProjectDeviceLog> findAllByDeviceIdOrderByLogDateDesc(String deviceId);
}
