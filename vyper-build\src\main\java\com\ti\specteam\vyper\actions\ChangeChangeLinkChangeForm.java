package com.ti.specteam.vyper.actions;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeChangeLinkChangeForm extends BuildNumberForm {

    @NotNull
    private List<Map<String, Object>> changes;

    @JsonIgnore
    public String display() {
        return changes.stream().map(c -> (String) c.get("changeNumber")).collect(Collectors.joining(", "));
    }

}
