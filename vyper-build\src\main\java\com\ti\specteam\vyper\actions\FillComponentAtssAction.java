package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.atss.traveler.Component;
import com.ti.specteam.vyper.atss.traveler.Traveler;
import com.ti.specteam.vyper.atss.traveler.*;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.basename.BaseNameService;
import com.ti.specteam.vyper.build.OperationValidationService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.componentmap.util.ComponentUniqueness;
import com.ti.specteam.vyper.build.dataloader.AtssLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.template.TemplateType;
import com.ti.specteam.vyper.util.AuthorizedOperationService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.FILL_COMPONENT_ATSS;

/**
 * This class will fill any missing selections with data from an ATSS traveler.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FillComponentAtssAction { // aka FillUnselectedAtssAction

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final TravelerService travelerService;
    private final TravelerRefreshService travelerRefreshService;
    private final ComponentMapService componentMapService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuthorizedOperationService authorizedOperationService;
    private final List<String> opnTxtNames;
    private final OperationValidationService operationValidationService;
    private final AuditService auditService;
    private final AtssLoader atssLoader;
    public final BaseNameService baseNameService;

    public Build execute(FillComponentAtssForm fillComponentAtssForm) {
        log.debug("execute(fillComponentAtssForm:{})", fillComponentAtssForm);

        Vyper vyper = vyperService.fetchVyper(fillComponentAtssForm);
        Build build = buildService.fetchBuild(fillComponentAtssForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        // read the atss traveler
        TravelerForm travelerForm = new TravelerForm();
        travelerForm.setSpecDevice(fillComponentAtssForm.getMaterial());
        travelerForm.setFacilityAt(fillComponentAtssForm.getFacility());
        travelerForm.setStatus(Status.valueOf(fillComponentAtssForm.getStatus()));
        Traveler traveler = travelerService.build(travelerForm);

        // fill in the missing selections
        process(vyper, build, traveler, fillComponentAtssForm);

        // special handling for opn txt components
        addOpnTxt(vyper, build, traveler);

        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // store the fill component record
        build.getFillComponent().reset();
        build.getFillComponent().setMode(FillComponentMode.ATSS);
        build.getFillComponent().setAtssFacility(fillComponentAtssForm.getFacility());
        build.getFillComponent().setAtssMaterial(fillComponentAtssForm.getMaterial());
        build.getFillComponent().setAtssStatus(fillComponentAtssForm.getStatus());
        build.getFillComponent().getSource().appointUser(securityService.user());

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                FILL_COMPONENT_ATSS,
                "filled components via atss copy: " + fillComponentAtssForm.getDisplay()
        );

        return buildService.saveBuild(build);
    }

    /**
     * Copy unselected data from ATSS.
     *
     * @param build    {@link Build} The build object
     * @param traveler {@link Traveler} The traveler object
     */

    public void process(Vyper vyper, Build build, Traveler traveler, FillComponentAtssForm fillComponentAtssForm) {

        // load ATSS data into a copy of this build for comparison
        final Build buildCopy = buildService.fetchBuild(build.getBuildNumber());
        buildCopy.getAtss().getObject().setMaterial(fillComponentAtssForm.getMaterial());
        buildCopy.getAtss().getObject().setFacilityAt(fillComponentAtssForm.getFacility());
        buildCopy.getAtss().getObject().setStatus(fillComponentAtssForm.getStatus());

        atssLoader.load(vyper, buildCopy);

        // setting build test information from atss traveler in case of atss full copy
        if(build.isAtssFullCopy() || build.isAtssCopy()){
            build.setTest(buildCopy.getTest());
            if(build.isAtssFullCopy()){
                build.clearSelections("Die");
            }
        }

        // loop through the selections
        List<Selection> selections = build.getSelections().stream()

                // only choose the empty selections
                .filter(Selection::isEmpty)
                .map(selection -> {
                    // get the component map
                    ComponentMap componentMap = componentMapService.findByName(selection.getName());

                    // get the atss component name
                    String atssName;
                    if (componentMap != null) {
                        atssName = componentMap.getAtssComponentName();
                    } else {
                        atssName = selection.getName();
                    }

                    // fill based on component uniqueness
                    boolean addedItem;
                    if (null == componentMap || componentMap.getComponentUniqueness() == ComponentUniqueness.MULTIPLE_VALUE) {
                        addedItem = byOperation(vyper, build, atssName, selection, traveler);
                    } else {
                        addedItem = byGlobal(vyper, build, atssName, selection, traveler);
                    }

                    // code to sync selection screen and build screen for below components when user uses fill unselected option
                    if (selection.getName().equals("Backgrind")) {
                        Backgrind backgrind = build.getBackgrind();
                        backgrind.setBackgrindSelected(selection.getItems());
                        backgrind.getSource().getUser().setUserid(securityService.user().getUserid());
                        backgrind.getSource().getUser().setUsername(securityService.user().getUsername());
                    }

                    if (selection.getName().equals("Topside Symbol")) {
                        Symbolization symbolization = build.getSymbolization();
                        Symbol symbol = new Symbol();
                        if (!selection.getItems().isEmpty() && selection.getItems().get(0) != null) {
                            symbol.getObject().setName(selection.getItems().get(0).getValue());
                            symbolization.getSymbols().add(symbol);
                            symbolization.getSymbols().get(0).setSource(Source.ATSS);
                        }
                    }

                    if (build.getDies().getDieInstances().isEmpty() && selection.getName().equals("Die")) {
                        Dies dies = build.getDies();
                        DieInstance dieInstance = new DieInstance();
                        dieInstance.setType("Die");
                        dies.getDieInstances().add(dieInstance);
                        dies.getSource().appointSystem(SystemName.FILL_COMPONENTS);
                        build.setDies(dies);
                        for (SelectionItem selectionItem : selection.getItems()) {
                            Die die = new Die();
                            die.setName(selectionItem.getValue());
                            die.setEngineering(selectionItem.getEngineering());
                            if(build.isAtssFullCopy() || build.isAtssCopy()){
                                Component dieComponent = traveler.fetchComponentWithValue("Die", selectionItem.getValue());
                                Attribute incomingWaferThickAttribute = dieComponent.fetchAttribute("Incoming Wafer Thick");
                                if(incomingWaferThickAttribute != null){
                                         die.setIncomingWaferThick(Integer.valueOf(incomingWaferThickAttribute.getValue()));
                                }
                            }
                            dieInstance.getDies().add(die);
                        }
                    }

                    // add to component instance if empty
                    com.ti.specteam.vyper.build.model.Component buildComponent = getBuildComponent(build, atssName);
                    if (buildComponent != null && missingCompInstancePriority(buildComponent) && addedItem) {
                        copyCompInstancePriority(buildCopy, build, atssName);
                    }

                    return selection;
                })
                .filter(Selection::isEmpty)
                .map(selection -> {

                    // get the component map
                    ComponentMap componentMap = componentMapService.findByName(selection.getName());

                    // for empty selections, try the alternativeFillSource
                    if (componentMap == null || componentMap.getAlternativeFillSource() == null) {
                        return selection;
                    }

                    // change from csv to list<string>
                    String[] alts = componentMap.getAlternativeFillSource().split(",");

                    // loop through all of the alternative names
                    for (String atssName : alts) {
                        if (selection.isEmpty()) {
                            boolean addedItem;
                            if (componentMap.getComponentUniqueness() == ComponentUniqueness.MULTIPLE_VALUE) {
                                addedItem = byOperation(vyper, build, atssName, selection, traveler);
                            } else {
                                addedItem = byGlobal(vyper, build, atssName, selection, traveler);
                            }

                            // add to component instance if empty
                            com.ti.specteam.vyper.build.model.Component buildComponent = getBuildComponent(build, atssName);
                            if (buildComponent != null && missingCompInstancePriority(buildComponent) && addedItem) {
                                copyCompInstancePriority(buildCopy, build, atssName);
                            }
                        }
                    }

                    return selection;
                })
                .collect(Collectors.toList());

    }

    // copy data from atss into selection.
    // we respect operation so that the traveler's operation matches the selection.operation
    // return whether any selection item has been added
    public boolean byOperation(Vyper vyper, Build build, String atssName, Selection selection, Traveler traveler) {

        // loop through the atss traveler subflows
        List<SelectionItem> items = traveler.getSubFlows().stream()

                // get the atss traveler operations
                .flatMap(subFlow -> subFlow.getOperations().stream())

                // get the unvalidated operations
                .filter(operation -> !operationValidationService.isOperationChecked(build, operation.getName()))

                // get the operations that the current user can update
                .filter(operation -> authorizedOperationService.canUpdateOperation(vyper, build, operation.getName()))

                // get the operation that matches the current selection
                .filter(operation -> StringUtils.equalsIgnoreCase(selection.getOperation(), operation.getName()))

                // loop through the atss traveler components
                .flatMap(operation -> operation.getComponents().stream())

                // get the atss components
                .filter(component -> matchComponentName(atssName, component))

                // add distinct values to the selection items
                .map(Component::getValue)
                .distinct()
                .map(this::valueToSelectionItem)
                .collect(Collectors.toList());

        return selection.getItems().addAll(items);
    }

    // copy data from atss into selection.
    // we don't respect operation - all selection.operations will get the exact same selection items.
    // return whether any selection item has been added
    public boolean byGlobal(Vyper vyper, Build build, String atssName, Selection selection, Traveler traveler) {

        // loop through the atss traveler flows, return a list of selection items
        List<SelectionItem> items = traveler.getSubFlows().stream()

                // loop through the atss traveler operations
                .flatMap(subFlow -> subFlow.getOperations().stream())

                // filter out operations that are validated
                .filter(operation -> !operationValidationService.isOperationChecked(build, operation.getName()))

                // filter out operations the current user can't edit
                .filter(operation -> authorizedOperationService.canUpdateOperation(vyper, build, operation.getName()))

                // loop through the atss components
                .flatMap(operation -> operation.getComponents().stream())

                // match the atss component to the vyper component
                .filter(component -> matchComponentName(atssName, component))

                // get the component values
                .map(Component::getValue)

                // remove any duplicates
                .distinct()

                // add the value to the items, return the items
                .map(this::valueToSelectionItem)

                // build the list of items
                .collect(Collectors.toList());

        // add the items to the selection
        return selection.getItems().addAll(items);
    }

    // convert a value into a selection item
    public SelectionItem valueToSelectionItem(String value) {
        SelectionItem item = new SelectionItem();
        item.setValue(value);
        item.setEngineering(Engineering.N);
        item.getSource().set(Source.FILL_COMPONENT);
        return item;
    }

    public boolean matchComponentName(String atssName, Component component) {

        return matchComponentName(atssName, component.getName());
    }

    public boolean matchComponentName(String atssName, String componentName) {

        // match to the atssName
        if (StringUtils.equalsIgnoreCase(atssName, componentName)) {
            return true;
        }

        // match to the base name
        List<String> componentNames = baseNameService.findAllComponentNamesByComponentName(atssName);
        return componentNames.stream().anyMatch(name -> StringUtils.equalsIgnoreCase(name, componentName));
    }

    private com.ti.specteam.vyper.build.model.Component getBuildComponent(Build build, String atssName) {
        return build.getComponents().stream()
                .filter(comp -> matchComponentName(atssName, comp.getName()))
                .findFirst()
                .orElse(null);
    }

    private boolean missingCompInstancePriority(com.ti.specteam.vyper.build.model.Component component) {
        return component.getInstances().stream()
                .flatMap(inst -> inst.getPriorities().stream())
                .findAny()
                .isEmpty();
    }

    private void copyCompInstancePriority(Build srcBuild, Build tgtBuild, String atssName) {
        com.ti.specteam.vyper.build.model.Component srcComp = getBuildComponent(srcBuild, atssName);
        com.ti.specteam.vyper.build.model.Component tgtComp = getBuildComponent(tgtBuild, atssName);
        if (srcComp != null && tgtComp != null) {
            srcComp.copy(tgtComp);
        }
    }

    /**
     * Loop through the traveler, and find the opn-txt assy/test/pack components.
     * if the operation matches, make sure they are inserted into the build
     *
     * @param build    {@link Build} The build
     * @param traveler {@link Traveler} The ATSS traveler
     */
    public void addOpnTxt(Vyper vyper, Build build, Traveler traveler) {

        traveler.getSubFlows().forEach(subFlow -> subFlow.getOperations().stream()
                .filter(operation -> !operationValidationService.isOperationChecked(build, operation.getName()))
                .filter(operation -> authorizedOperationService.canUpdateOperation(vyper, build, operation.getName()))
                .forEach(operation -> operation.getComponents().stream()
                        .filter(component -> opnTxtNames.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, component.getName())))
                        .forEach(component -> addSelectionValue(build, operation, component))));
    }

    private void addSelectionValue(Build build, Operation operation, Component component) {
        Selection selection = build.findSelection(operation.getName(), component.getName());
        if (selection == null) {
            return;
        }

        boolean exists = selection.valueExists(component.getValue());
        if (exists) {
            return;
        }

        // add the selection
        selection.addItem(component.getValue(), Engineering.N, Source.FILL_COMPONENT);
    }

}
