package com.ti.specteam.atssmassupload.service;

import com.ti.specteam.atssmassupload.domain.*;
import com.ti.specteam.atssmassupload.repository.AtssComponentAttributeRepository;
import com.ti.specteam.atssmassupload.repository.SpecChangeTestProgramRepository;
import com.ti.specteam.vyper.apitraveler.model.ApiTravelerAttribute;
import com.ti.specteam.vyper.apitraveler.model.ApiTravelerComponent;
import com.ti.specteam.vyper.atss.cams.AtssCamsService;
import com.ti.specteam.vyper.pgs.PgsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("ALL")
@Service
@RequiredArgsConstructor
@Slf4j
public class AtssComponentValidatorService {
    private final PgsService pgsService;
    private final AtssCamsService atssCamsService;
    private final AtssComponentAttributeRepository atssComponentAttributeRepository;
    private final SpecChangeTestProgramRepository specChangeTestProgramRepository;

    static final String ATTRIBUTE_NAME_VALUE_SEPAROTOR = ":";
    final List<String> TEST_PGM_ATTRIBUTES =
                List.of("Revision", "Tester", "Lot Control Value", "Library Location", "Program Extension", "Continuity Failure", "Parametric Failure",
                        "Final Test Yield", "Functional Failure");

    public DeviceComponent getDieAttributes(DeviceComponent deviceComponent){
        //Get ATSS Die Attributes from SAP View
        Map<String, Object> dieAttributesFromSap = atssComponentAttributeRepository.getSapDieAttributes(deviceComponent.getValue());
        Set<DeviceComponentAttribute> givenAttributes = deviceComponent.getAttributes();
        deviceComponent.getAttributes().addAll(
                dieAttributesFromSap.entrySet()
                        .stream()
                        .filter( attributeKeyValue -> (attributeKeyValue.getValue() != null && !attributeKeyValue.getKey().equals("DIENAME")))
                        .filter( attributeKeyValue ->
                                        !givenAttributes.stream().anyMatch( attr -> attr.getName().equalsIgnoreCase(attributeKeyValue.getKey())))
                        .map( attributeKeyValue ->
                                DeviceComponentAttribute.builder()
                                        .name(attributeKeyValue.getKey())
                                        .value(String.valueOf(attributeKeyValue.getValue()))
                                        .isQualified("N")
                                        .build()
                        ).collect(Collectors.toSet())
        );
        return deviceComponent;
    }

    //sap.attributes
    @Cacheable(cacheManager="atssCacheManager",
            value="sap.die.attributes",
            key="#die")
    public List<ApiTravelerAttribute> getDieAttributes(String die){
        //Get ATSS Die Attributes from SAP View
        Map<String, Object> dieAttributesFromSap = atssComponentAttributeRepository.getSapDieAttributes(die);

        return dieAttributesFromSap.entrySet()
                        .stream()
                        .filter( attributeKeyValue -> (attributeKeyValue.getValue() != null && !attributeKeyValue.getKey().equals("DIENAME")))
                        .map( attributeKeyValue ->{
                                    ApiTravelerAttribute newAttribute = new ApiTravelerAttribute();
                                    newAttribute.setName(attributeKeyValue.getKey());
                                    newAttribute.setValue(String.valueOf(attributeKeyValue.getValue()));
                                    return newAttribute;
                                }).collect(Collectors.toList());
    }

    /**
     * DECIDE AGAIN LATER FOR CACHE REQUIREMENT
    @Cacheable(cacheManager="atssCacheManager",
            value="atss.component.attributes",
            key="#facilityAt.concat('-').concat(#componentName).concat(#componentValue)")
    **/
    public Map<String, Object> getAtssComponentData(String facilityAt, String componentName, String componentValue){

        log.debug("getAtssComponentData {} {} {}",facilityAt, componentName,componentValue);
        List<SpecCompAttribute> atssCompAttrData =
                atssComponentAttributeRepository.getAtssComponentData(facilityAt, componentName, componentValue);

        // No valid component name for given AT
        if (atssCompAttrData == null || atssCompAttrData.isEmpty()) return Collections.emptyMap();

        SpecCompAttribute specCompMap = atssCompAttrData.get(0);
        // Component Type , name
        Map<String , Object> specCompData = new HashMap<>();
        specCompData.put("componentType", specCompMap.getComponentType());
        specCompData.put("componentName", specCompMap.getComponentName());
        specCompData.put("componentValue", componentValue);
        specCompData.put("componentExists", specCompMap.getComponentExists());

        if (! specCompMap.getComponentType().equals(ComponentType.PARAGRAPH.name())){
            atssCompAttrData.stream().skip(1).forEach( specComp -> {
                Map<String, Boolean> compAttrs = null;
                if ( !specCompData.containsKey("componentAttributes")){
                    compAttrs = new HashMap<String , Boolean>();
                }else{
                    compAttrs = (Map<String, Boolean>) specCompData.get("componentAttributes");
                }
                compAttrs.put(specComp.getComponentAttributeName()+ ATTRIBUTE_NAME_VALUE_SEPAROTOR +specComp.getAttributeValue(), specComp.getAttributeExists());
                specCompData.put("componentAttributes", compAttrs);
            });
        }
        log.debug("{}",specCompData);

        return specCompData;

    }

    public DeviceComponent manageTestPrograms( DeviceComponent deviceComponent ){
        Optional<String> revision =
                deviceComponent.getAttributes().stream()
                        .filter(attr -> attr.getName().equals("Revision"))
                        .map(DeviceComponentAttribute::getValue).findFirst();
        SpecChangeTestProgram testProgramData = specChangeTestProgramRepository.getTestProgramLatestRevision(deviceComponent.getValue());;
        String sRevision = "";
        if ( revision.isPresent()){
            testProgramData.setProgramRev(revision.get());
        }

        String revisionFixed = testProgramData.getProgramRev().length() == 1 ?
                "0"+testProgramData.getProgramRev() : testProgramData.getProgramRev();

        //Manage the other attributes
        final List<String> TEST_PGM_ATTRIBUTES =
                List.of("Revision", "Tester", "Lot Control Value", "Library Location", "Program Extension", "Continuity Failure", "Parametric Failure",
                        "Final Test Yield", "Functional Failure");

        final SpecChangeTestProgram finalTestProgramData = testProgramData;
        Set<DeviceComponentAttribute> testProgramAttributes =
                TEST_PGM_ATTRIBUTES.stream().map(
                attriName -> {
                    DeviceComponentAttribute givenAttribute =
                            deviceComponent.getAttributes()
                                    .stream()
                                    .filter(attr -> attr.getName().equals(attriName))
                                    .findFirst().orElse(null);
                    if ( givenAttribute == null){
                        givenAttribute = new DeviceComponentAttribute(attriName);
                        switch (attriName){
                            case "Revision":
                                givenAttribute.setValue(revisionFixed);
                                break;
                            case "Lot Control Value":
                                givenAttribute.setValue(deviceComponent.getValue()+ revisionFixed+ "." +testProgramData.getProgramExtension());
                                break;
                            case "Program Extension":
                                givenAttribute.setValue(testProgramData.getProgramExtension().toUpperCase());
                                break;
                            case "Tester":
                                givenAttribute.setValue(finalTestProgramData.getTesterPlatform());
                                break;
                            case "Library Location":
                                givenAttribute.setValue(finalTestProgramData.getSrcDb());
                                break;
                            case "Continuity Failure":
                            case "Parametric Failure":
                            case "Final Test Yield":
                            case "Functional Failure":
                                givenAttribute.setValue(finalTestProgramData.getDataLine());
                                break;
                            default:
                                break;
                        }
                    }
                    return givenAttribute;
                }
        ).collect(Collectors.toSet());

        deviceComponent.setAttributes( testProgramAttributes );

        return deviceComponent;
    }

    public List<ApiTravelerAttribute> manageTestProgramsByComponentValue( String componentValue ) {
        SpecChangeTestProgram testProgramData = specChangeTestProgramRepository.getTestProgramLatestRevision(componentValue);

        //Manage the other attributes
        final SpecChangeTestProgram finalTestProgramData = testProgramData;
        List<ApiTravelerAttribute> testProgramAttributes =
                TEST_PGM_ATTRIBUTES.stream().map(
                attriName -> {
                    ApiTravelerAttribute givenAttribute =
                             new ApiTravelerAttribute(attriName, null);
                        switch (attriName){
                            case "Revision":
                                givenAttribute.setValue(testProgramData.getProgramRev());
                                break;
                            case "Lot Control Value":
                                givenAttribute.setValue(componentValue+ testProgramData.getProgramRev()+ "." +testProgramData.getProgramExtension());
                                break;
                            case "Program Extension":
                                givenAttribute.setValue(testProgramData.getProgramExtension() == null ? ""  : testProgramData.getProgramExtension().toUpperCase());
                                break;
                            case "Tester":
                                givenAttribute.setValue(finalTestProgramData.getTesterPlatform());
                                break;
                            case "Library Location":
                                givenAttribute.setValue(finalTestProgramData.getSrcDb());
                                break;
                            case "Continuity Failure":
                            case "Parametric Failure":
                            case "Final Test Yield":
                            case "Functional Failure":
                                givenAttribute.setValue(finalTestProgramData.getDataLine());
                                break;
                            default:
                                break;
                    }
                    return givenAttribute;
                }
        ).collect(Collectors.toList());

        return testProgramAttributes;
    }

}
