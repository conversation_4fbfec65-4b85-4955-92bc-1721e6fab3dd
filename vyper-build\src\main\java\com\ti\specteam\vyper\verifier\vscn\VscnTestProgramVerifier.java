package com.ti.specteam.vyper.verifier.vscn;

import com.ti.specteam.atssmassupload.serviceImp.SpecChangeServiceImpl;
import com.ti.specteam.vyper.build.model.TravelerAttribute;
import com.ti.specteam.vyper.build.model.TravelerComponent;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.verifier.model.VerifierStatus;
import com.ti.specteam.vyper.vscn.model.Vscn;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.verifier.model.VerifierSource.SOURCE_TSM;

@Service
@Slf4j
@RequiredArgsConstructor
public class VscnTestProgramVerifier implements IVscnVerifier {

    private final SpecChangeServiceImpl specChangeService;

    public void verify(Vyper vyper, Vscn vscn) {
        log.debug("verify(vyper:{}, vscn:{})", vyper.getVyperNumber(), vscn.getVscnNumber());
        vscn.findVerifiers(SOURCE_TSM).forEach(verifier -> verify(vscn, verifier));
    }

    private void verify(Vscn vscn, Verifier verifier){
        verifier.clearMessages();
        TravelerComponent component = vscn.findTestTravelerComponentByComponentName("Test Program 1");
        if(component != null){
            String testProgram = component.getValue();
            String testRevision = null;
            TravelerAttribute attribute = component.findByName("Revision");
            if(attribute != null){
                testRevision = attribute.getValue();
            }

            boolean result = specChangeService.isActiveTestProgramAndRevision(testProgram,testRevision);

            verifier.addMessage("Active Test Program and Revision in TSM ", result, "TSM", result ? "Yes" : "No", null, null);
            verifier.setStatus(result ? VerifierStatus.FULLY_VERIFIED : VerifierStatus.NOT_VERIFIED);
        }
    }

}
