package com.ti.specteam.vyper.warning

import com.ti.specteam.vyper.armarc.ArmArcLookupService
import com.ti.specteam.vyper.armarc.Armarc
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.Engineering
import com.ti.specteam.vyper.build.model.Source
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.componentlistener.context.ChangeComponentListenerContext
import com.ti.specteam.vyper.componentlistener.processor.LeadframeProcessor
import com.ti.specteam.vyper.componentlistener.processor.MoldCompoundProcessor
import com.ti.specteam.vyper.componentlistener.processor.MountCompoundProcessor
import com.ti.specteam.vyper.componentlistener.processor.WireProcessor
import com.ti.specteam.vyper.pra.model.Pra
import spock.lang.Specification

class PavvWarningServiceSpec extends Specification {

    ArmArcLookupService armArcLookupService = Mock(ArmArcLookupService)
    LeadframeProcessor leadframeProcessor = Mock(LeadframeProcessor)
    MoldCompoundProcessor moldCompoundProcessor = Mock(MoldCompoundProcessor)
    MountCompoundProcessor mountCompoundProcessor = Mock(MountCompoundProcessor)
    WireProcessor wireProcessor = Mock(WireProcessor)

    PavvWarningService service = new PavvWarningService(
            armArcLookupService,
            leadframeProcessor,
            moldCompoundProcessor,
            mountCompoundProcessor,
            wireProcessor
    )

    def vyper1 = new Vyper()
    def build1 = new Build()
    def pra1 = new Pra()

    def setup() {
        0 * _
    }

    def "onChangeComponent does nothing if not a pavv component"() {

        ChangeComponentListenerContext context = ChangeComponentListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .componentName("Die")
                .build()

        when:
        service.onChangeComponent(context)

        then:
        notThrown RuntimeException
        // nothing happens
    }

    def "onChangeComponent does nothing if there is no mb diagram component"() {

        ChangeComponentListenerContext context = ChangeComponentListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .componentName("Leadframe")
                .build()

        when:
        service.onChangeComponent(context)

        then:
        0 * _
    }

    def "onChangeComponent creates pra, runs the pavv validator, and translates the messages to warnings"() {

        def c1 = build1.findOrCreateComponent("MB Diagram", Source.PGS)
        c1.setValue(0, 0, "MB_VALUE1", Engineering.N, Source.PGS)
        def priorityMBDiagram = c1.getPriority(0, 0)

        def c2 = build1.findOrCreateComponent("Leadframe", Source.PGS)
        c2.setValue(0, 0, "LEADFRAME_VALUE1", Engineering.N, Source.PGS)
        c2.getPriority(0, 0)
        def priorityLeadframe = c2.getPriority(0, 0)

        def c3 = build1.findOrCreateComponent("Mount Compound", Source.PGS)
        c3.setValue(0, 0, "MOUNT_VALUE1", Engineering.N, Source.PGS)
        def priorityMount = c3.getPriority(0, 0)

        def c4 = build1.findOrCreateComponent("Mold Compound", Source.PGS)
        c4.setValue(0, 0, "MOLD_VALUE1", Engineering.N, Source.PGS)
        def priorityMold = c4.getPriority(0, 0)

        def c5 = build1.findOrCreateComponent("Wire", Source.PGS)
        c5.setValue(0, 0, "WIRE_VALUE1", Engineering.N, Source.PGS)
        def priorityWire = c5.getPriority(0, 0)

        ChangeComponentListenerContext context = ChangeComponentListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .componentName("Leadframe")
                .build()

        when:
        service.onChangeComponent(context)

        then:
        1 * armArcLookupService.findArmarc(build1) >> new Armarc(
                leadframe: "ARMARC_LF_VALUE1",
                mountCompound: "ARMARC_MOUNT_VALUE1",
                moldCompound: "ARMARC_MOLD_VALUE1",
                wireType: "ARMARC_TYPE1",
                wireDiameter: "ARMARC_DIAMETER1"
        )

        1 * mountCompoundProcessor.matchValue(priorityMount, "ARMARC_MOUNT_VALUE1", _) >> false
        1 * moldCompoundProcessor.matchValue(priorityMold, "ARMARC_MOLD_VALUE1", _) >> false
        1 * leadframeProcessor.matchValue(priorityLeadframe, "ARMARC_LF_VALUE1", _) >> false
        1 * wireProcessor.matchValue(priorityWire, "ARMARC_DIAMETER1", "ARMARC_TYPE1", _) >> false

        and:
        priorityMBDiagram.warnings[0] == "Mount Compound: ARMARC = ARMARC_MOUNT_VALUE1, Build = MOUNT_VALUE1"
        priorityMBDiagram.warnings[1] == "Mold Compound: ARMARC = ARMARC_MOLD_VALUE1, Build = MOLD_VALUE1"
        priorityMBDiagram.warnings[2] == "Leadframe: ARMARC = ARMARC_LF_VALUE1, Build = LEADFRAME_VALUE1"
        priorityMBDiagram.warnings[3] == "Wire: ARMARC = ARMARC_TYPE1 ARMARC_DIAMETER1, Build = WIRE_VALUE1"
    }

    def "calling updateWarning with a null armarc data doesn't throw"() {

        def component1 = build1.findOrCreateComponent("MB Diagram", Source.VYPER)
        component1.addPriority(0, "Name", "MBDIAGRAM1", Engineering.N)

        when:
        service.updateWarnings(vyper1, build1)

        then:
        1 * armArcLookupService.findArmarc(build1) >> null

        and:
        notThrown NullPointerException
    }

    def "calling updateWarning with a null priority data doesn't throw"() {

        def armarc1 = new Armarc()
        build1.findOrCreateComponent("MB Diagram", Source.VYPER)

        when:
        service.updateWarnings(vyper1, build1)

        then:
        0 * armArcLookupService.findArmarc(build1) >> armarc1

        and:
        notThrown NullPointerException
    }

}
