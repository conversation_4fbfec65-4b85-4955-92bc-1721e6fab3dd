package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.SystemName;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.componentlistener.listener.ChangeComponentListener;
import com.ti.specteam.vyper.componentlistener.context.ChangeComponentListenerContext;
import com.ti.specteam.vyper.core.exception.VyperNotFoundException;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_COMPONENT;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeComponentsAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final ComponentMapService componentMapService;
    private final BomTemplateLoader bomTemplateLoader;
    private final FlowLoader flowLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final AuditService auditService;
    private final PackLoader packLoader;
    private final List<ChangeComponentListener> listeners;

    /**
     * Change the component
     *
     * @param changeComponentsForm ChangeComponentsForm
     * @return Vyper
     */
    public Build execute(ChangeComponentsForm changeComponentsForm) {
        log.debug("execute(changeComponentsForm:{})", changeComponentsForm);

        Vyper vyper = vyperService.fetchVyper(changeComponentsForm);
        Build build = buildService.fetchBuild(changeComponentsForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        // find the index of the component
        int pos = -1;
        for (int n = 0; n < build.getComponents().size(); ++n) {
            Component c = build.getComponents().get(n);
            if (c.getName().equals(changeComponentsForm.getComponent().getName())) {
                pos = n;
                break;
            }
        }

        // if found
        if (-1 == pos) {
            throw new VyperNotFoundException("The component was not found " + changeComponentsForm.getComponent().getName());
        }

        // set the source
        Component newComponent = changeComponentsForm.getComponent();

        if (changeComponentsForm.getRevertPgs()) {
            newComponent.getSource().appointSystem(SystemName.PGS);
        } else {
            newComponent.getSource().appointUser(securityService.user());
        }

        // replace the old component with the new one
        build.getComponents().set(pos, newComponent);

        // clear any selections for this component
        String cName = changeComponentsForm.getComponent().getName();
        build.clearSelections(cName);

        // by default, the value is stored into priority.object.name
        // if there is a getPgsAttrToAtssComponentValue, copy the value to that key
        ComponentMap componentMap = componentMapService.findByName(cName);

        if (componentMap != null) {
            String aName = componentMap.getPgsAttrToAtssComponentValue();
            if (aName != null) {
                //noinspection CodeBlock2Expr
                newComponent.getInstances().forEach(componentInstance -> {
                    componentInstance.getPriorities().forEach(componentPriority -> {

                        Object value = componentPriority.getObject().get("name");
                        Object supplierPartNumberValue = componentPriority.getObject().get("SupplierPartNumber");

                        // when only changed value from  build screen
                        if (supplierPartNumberValue != null && value != null && !value.equals(supplierPartNumberValue)) {
                            componentPriority.getObject().put(aName, value);
                        }

                        if (changeComponentsForm.getRevertPgs()) {
                            componentPriority.getSource().appointSystem(SystemName.PGS);
                        } else {
                            componentPriority.getSource().appointUser(securityService.user());
                        }
                    });
                });
            }
        }

        // refresh, because changes to components can change the bom template
        bomTemplateLoader.load(vyper, build);
        packLoader.load(vyper, build);
        flowLoader.load(vyper, build);
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // notify the listeners
        ChangeComponentListenerContext context = ChangeComponentListenerContext.builder()
                .vyper(vyper)
                .build(build)
                .form(changeComponentsForm)
                .componentName(cName)
                .build();

        listeners.forEach(listeners -> listeners.onChangeComponent(context));

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_COMPONENT,
                "changed component " + changeComponentsForm.getComponent().getName() + " to: " + changeComponentsForm.display()
        );

        return buildService.saveBuild(build);
    }

}
