package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.RequiredComponentLoader;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.SelectionItem;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.REMOVE_FLOW_COMPONENT;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RemoveFlowComponentAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;

    public Build execute(RemoveFlowComponentForm removeFlowComponentForm) {
        log.debug("execute(removeTravelerComponentForm:{})", removeFlowComponentForm);

        Vyper vyper = vyperService.fetchVyper(removeFlowComponentForm);
        Build build = buildService.fetchBuild(removeFlowComponentForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        String values = "";

        // remove the traveler component(s)
        FlowOperation flowOperation = build.getFlow().getObject().getOperations().stream()
                .filter(fo -> StringUtils.equalsIgnoreCase(fo.getName(), removeFlowComponentForm.getOperationName()))
                .findFirst()
                .orElse(null);

        if (flowOperation != null) {

            // remove the component
            flowOperation.getComponents()
                    .removeIf(c -> StringUtils.equalsIgnoreCase(c.getName(), removeFlowComponentForm.getComponentName()));

            // build a string of the selection values
            values = build.getSelections().stream()
                    .filter(selection -> StringUtils.equalsIgnoreCase(selection.getName(), removeFlowComponentForm.getComponentName()))
                    .flatMap(selection -> selection.getItems().stream())
                    .map(SelectionItem::getValue)
                    .collect(Collectors.joining(", "));
        }

        // remove the selection that matches the operation and component
        build.getSelections().removeIf(selection -> StringUtils.equalsIgnoreCase(selection.getOperation(), removeFlowComponentForm.getOperationName()) &&
                StringUtils.equalsIgnoreCase(selection.getName(), removeFlowComponentForm.getComponentName()));

        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        String detail = "removed flow component: ";
        if (flowOperation != null) {
            detail += flowOperation.getName() + " - ";
        }
        detail += removeFlowComponentForm.getComponentName() + " with values: " + values;

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                REMOVE_FLOW_COMPONENT,
                detail);

        return buildService.saveBuild(build);
    }

}
