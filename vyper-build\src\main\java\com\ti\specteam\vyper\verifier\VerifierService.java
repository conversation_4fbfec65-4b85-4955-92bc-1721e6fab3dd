package com.ti.specteam.vyper.verifier;

import com.ti.specteam.vyper.build.componentmap.ComponentMap;
import com.ti.specteam.vyper.build.componentmap.ComponentMapService;
import com.ti.specteam.vyper.build.componentmap.util.YesNo;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.verifier.model.VerifierSource;
import com.ti.specteam.vyper.vscn.model.Vscn;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.ti.specteam.vyper.build.componentmap.util.YesNo.YES;

@Service
@Slf4j
@RequiredArgsConstructor
public class VerifierService {

    private final ComponentMapService componentMapService;
    private final List<String> symbolComponents = Arrays.asList("Topside Symbol","ECAT","CUST1","CUST2","CUST3","CUST4","CUST5","CUST6","CUST7","CUST8","CUST9");

    /**
     * This method creates the verifiers for the components in the PRA object.
     *
     * @param pra {@link Pra} The PRA object.
     */
    public void initializeVerifiers(Pra pra) {

        // clear the verifiers
        pra.clearVerifiers();

        // loop through the components
        pra.getComponents().forEach(component -> {

            // get the component map
            ComponentMap componentMap = componentMapService.findByName(component.getName());

            if (null != componentMap) {
                component.stream()
                        .flatMap(ComponentInstance::stream)
                        .forEach(componentPriority -> {

                            // for each component value, initialize a verifier for it
                            // the order of these calls determines the order that the icons appear in the ui
                            initVerifier(pra, componentMap.getPraPgsValidator(), component, componentPriority, VerifierSource.SOURCE_PGS);
                            initVerifier(pra, componentMap.getPraPavvComponentValidator(), component, componentPriority, VerifierSource.SOURCE_PAVV_COMPONENT);
                            initVerifier(pra, componentMap.getPraArmarcValidator(), component, componentPriority, VerifierSource.SOURCE_ARMARC);
                            initVerifier(pra, componentMap.getPraAtssGlobalValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_GLOBAL);
                            initVerifier(pra, componentMap.getPraAtssAtValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_AT);
                            initVerifier(pra, componentMap.getPraAtssBomValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_BOM);
                            initVerifier(pra, componentMap.getPraDieValidator(), component, componentPriority, VerifierSource.SOURCE_DIE);
                            initVerifier(pra, componentMap.getPraDiagramValidator(), component, componentPriority, VerifierSource.SOURCE_DIAGRAM);
                            initVerifier(pra, componentMap.getPraBomQualifiedValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_QUALIFIED);
                        });
            }

        });

    }

    /**
     * If the YesNo is Yes, create a verifier for the value.
     *
     * @param pra       {@link Pra} The PRA object
     * @param yesNo     {@link YesNo} A YES/NO flag. If yes then the verifier is created
     * @param component {@link Component} The component to create a verifier
     * @param priority  {@link ComponentPriority} The component priority (the component's value)
     * @param source    {@link VerifierSource} The verifier source
     */
    public void initVerifier(Pra pra, YesNo yesNo, Component component, ComponentPriority priority, VerifierSource source) {
        if (yesNo == YES) {
            pra.findOrCreateVerifier(component, priority, source);
        }
    }

    public void initVerifier(Vscn vscn, YesNo yesNo, Component component, ComponentPriority priority, VerifierSource source) {
        if (yesNo == YES) {
            vscn.findOrCreateVerifier(component, priority, source);
        }
    }

    public void initializeVerifiers(Vscn vscn) {

        TravelerComponent travelerComponent = vscn.findTestTravelerComponentByComponentName("Test Program 1");
        if (travelerComponent != null) {
            vscn.findOrCreateVerifier(travelerComponent.getName(), travelerComponent.getValue(), travelerComponent.getEngineering(), VerifierSource.SOURCE_TSM);
        }

        vscn.findOrCreateVerifier("Material", vscn.getMaterial().getMaterial(), Engineering.N, VerifierSource.SOURCE_SCS);
        if (vscn.getChangeNumber().getChangeNumber() != null) {
            vscn.findOrCreateVerifier("ChangeNumber", vscn.getChangeNumber().getChangeNumber(), Engineering.N, VerifierSource.SOURCE_CHANGELINK);
        }

        // loop through the components
        vscn.getComponents().stream().filter(component -> !symbolComponents.contains(component.getName()))
                .forEach(component -> {

            // get the component map
            ComponentMap componentMap = componentMapService.findByName(component.getName());

            if (null != componentMap) {
                component.stream()
                        .flatMap(ComponentInstance::stream)
                        .forEach(componentPriority -> {

                            // for each component value, initialize a verifier for it
                            initVerifier(vscn, componentMap.getPraPgsValidator(), component, componentPriority, VerifierSource.SOURCE_PGS);
                            initVerifier(vscn, componentMap.getPraArmarcValidator(), component, componentPriority, VerifierSource.SOURCE_ARMARC);
                            initVerifier(vscn, componentMap.getPraAtssGlobalValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_GLOBAL);
                            initVerifier(vscn, componentMap.getPraAtssAtValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_AT);
                            initVerifier(vscn, componentMap.getPraAtssBomValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_BOM);
                            initVerifier(vscn, componentMap.getPraDieValidator(), component, componentPriority, VerifierSource.SOURCE_DIE);
                            initVerifier(vscn, componentMap.getPraDiagramValidator(), component, componentPriority, VerifierSource.SOURCE_DIAGRAM);
                            initVerifier(vscn, componentMap.getPraBomQualifiedValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_QUALIFIED);

                            if (StringUtils.equalsIgnoreCase(component.getName(), "Die")) {
                                vscn.findOrCreateVerifier("Die", componentPriority.getValue(), Engineering.N, VerifierSource.SOURCE_SCS_MATERIAL_DIE);
                            }
                        });
            }

        });

    }

    public void initializeVerifiersForDie(Vscn vscn) {
        // loop through the components
        vscn.getComponents().stream()
                .filter(component -> StringUtils.equalsIgnoreCase(component.getName(), "Die"))
                .forEach(component -> {

                    // get the component map
                    ComponentMap componentMap = componentMapService.findByName(component.getName());

                    if (null != componentMap) {
                        component.stream()
                                .flatMap(ComponentInstance::stream)
                                .forEach(componentPriority -> {
                                    // for each component value, initialize a verifier for it
                                    initVerifier(vscn, componentMap.getPraPgsValidator(), component, componentPriority, VerifierSource.SOURCE_PGS);
                                    initVerifier(vscn, componentMap.getPraArmarcValidator(), component, componentPriority, VerifierSource.SOURCE_ARMARC);
                                    initVerifier(vscn, componentMap.getPraAtssGlobalValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_GLOBAL);
                                    initVerifier(vscn, componentMap.getPraAtssAtValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_AT);
                                    initVerifier(vscn, componentMap.getPraAtssBomValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_BOM);
                                    initVerifier(vscn, componentMap.getPraDieValidator(), component, componentPriority, VerifierSource.SOURCE_DIE);
                                    initVerifier(vscn, componentMap.getPraDiagramValidator(), component, componentPriority, VerifierSource.SOURCE_DIAGRAM);
                                    initVerifier(vscn, componentMap.getPraBomQualifiedValidator(), component, componentPriority, VerifierSource.SOURCE_ATSS_QUALIFIED);
                                    vscn.findOrCreateVerifier("Die", componentPriority.getValue(), Engineering.N, VerifierSource.SOURCE_SCS_MATERIAL_DIE);
                                });
                    }

                });
    }


}
