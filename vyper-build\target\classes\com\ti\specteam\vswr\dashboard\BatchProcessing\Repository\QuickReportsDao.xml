<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.dashboard.BatchProcessing.Repository.QuickReportsDao">
    <!-- This is used to pull a single page of filtered/sorted data -->
    <select id="getPage" parameterType="map" resultType="java.util.LinkedHashMap">
        SELECT * FROM (
            SELECT t.*, rownum rn FROM (
                SELECT * FROM (
                    SELECT * FROM ${tableName}
        <where>
            <foreach collection="params.filterTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    ${key} = #{term}
                </if>
                <if test="term == null">
                    ${key} IS NULL
                </if>
            </foreach>
            <if test="siteExt != null and siteExt.size > 0" >
                and (
                <foreach collection="siteExt" item="item" index="key" separator=" or ">
                    <foreach collection="item" item="value" index="idx" >
                    ${idx} = #{value}
                    </foreach>
                </foreach>
                )
            </if>
            <if test="swrStat != null" >
                and current_status in (${swrStat})
            </if>
        </where>
        )
        <where>
            <foreach collection="params.searchTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    <choose>
                        <when test="term.end != null &amp;&amp; term.end != 'undefined'">
                            UPPER(${key}) between to_date(#{term.start},'mm/dd/yyyy') AND to_date(#{term.end},'mm/dd/yyyy')
                        </when>
                        <otherwise>
                            UPPER(${key}) LIKE '%'|| UPPER(#{term.start}) ||'%'  
                        </otherwise>
                    </choose>
                </if>
            </foreach>
        </where>
        <if test="params.orderClause != null and !params.orderClause.equals('')">
             ORDER BY ${params.orderClause}
        </if>
            ) t
        ) WHERE rn &gt;= ${params.firstRow} AND rn &lt; ${params.firstRow} + ${params.rowsPerPage}
    </select>

    <!-- This gets column name/type info for use by sort/filter logic  -->
    <select id="getColumns" parameterType="map"
          resultType="Map">
        SELECT column_id, column_name, data_type, data_length,
               nullable, avg_col_len
          FROM all_tab_cols
         WHERE table_name = #{tableName}
         ORDER BY column_id
    </select>

    <!-- This gets total number of rows for the "filtered from 8,735 total entries" prompt  -->
    <select id="getCount" parameterType="map" resultType="integer">
        SELECT COUNT(1) FROM ${tableName}
    </select>

    <!-- This gets total number of filtere rows for the "Showing 1 to 25 of 2344" prompt  -->
    <select id="getCountBySearch" parameterType="map" resultType="integer">
        SELECT COUNT(1) FROM (
            SELECT * FROM ${tableName}
        <where>                   
            <foreach collection="params.filterTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    ${key} = #{term}
                </if>
                <if test="term == null">
                    ${key} IS NULL
                </if>
            </foreach>
        </where>
        )
        <where>
            <foreach collection="params.searchTerms" item="term" index="key" separator=" AND ">
                <if test="term != null &amp;&amp; term != 'undefined'">
                    <choose>
                        <when test="term.end != null &amp;&amp; term.end != 'undefined'">
                            UPPER(${key}) between to_date(#{term.start},'mm/dd/yyyy') AND to_date(#{term.end},'mm/dd/yyyy')
                        </when>
                        <otherwise>
                            UPPER(${key}) LIKE '%'|| UPPER(#{term.start}) ||'%'  
                        </otherwise>
                    </choose>
                </if>
            </foreach>
        </where>
    </select>
</mapper>
