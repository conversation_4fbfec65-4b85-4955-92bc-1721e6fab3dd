package com.ti.specteam.vyper.build.dataloader

import com.ti.specteam.vyper.atss.attribute.AttributeService
import com.ti.specteam.vyper.atss.component.AtssComponentService
import com.ti.specteam.vyper.atss.traveler.Status
import com.ti.specteam.vyper.build.model.*
import com.ti.specteam.vyper.config.flowopn.DeviceFlowOperation
import spock.lang.Specification
import spock.lang.Unroll

class EcatLoaderSpec extends Specification {

    AttributeService attributeService = Mock(AttributeService)
    AtssComponentService atssComponentService = Mock(AtssComponentService)

    EcatLoader loader = new EcatLoader(attributeService, atssComponentService)

    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _
        build1.material.object.OldMaterial = "THE_DEVICE"
        build1.facility.object.PDBFacility = "THE_FACILITY"

        // Initialize BuildFlow and FlowRows
        def buildFlow = new BuildFlow()
        def flowRow = new DeviceFlowOperation(opnName: "Symbolization")
        buildFlow.flowRows = [flowRow] // Add flow rows as needed
        build1.setBuildFlow(buildFlow)
    }

    def "if found in ATSS, use the ECAT value"() {
        when:
        loader.load(vyper1, build1)

        then:
        1 * atssComponentService.ecat("THE_DEVICE", "THE_FACILITY", Status.ACTIVE) >> "THE_ECAT"

        and:
        build1.components.size() == 1
        build1.components[0].name == "ECAT"
        build1.components[0].instances.size() == 1
        build1.components[0].instances[0].priorities.size() == 1
        build1.components[0].instances[0].priorities[0].value == "THE_ECAT"
    }

    def "if no leadframe component, ecat is created with no instances"() {
        when:
        loader.load(vyper1, build1)

        then:
        1 * atssComponentService.ecat("THE_DEVICE", "THE_FACILITY", Status.ACTIVE) >> null

        and:
        build1.components.size() == 1
        build1.components[0].name == "ECAT"

        and:
        build1.components[0].instances.size() == 0
    }

    def "if category is not found, no instances are created"() {

        Component component1 = build1.findOrCreateComponent("Leadframe", Source.VYPER)
        component1.setValue(0, 0, "1234567-0001", Engineering.N, Source.VYPER)

        when:
        loader.load(vyper1, build1)

        then:
        1 * atssComponentService.ecat("THE_DEVICE", "THE_FACILITY", Status.ACTIVE) >> null
        1 * attributeService.findByName("THE_FACILITY", "Leadframe", "1234567-0001") >> [[key: "Finish", value: "NIPDAU"]]

        and:
        build1.components.size() == 2
        build1.components[0].name == "Leadframe"
        build1.components[1].name == "ECAT"

        and:
        build1.components[1].instances.size() == 0
    }

    def "if finish is not found, no instances are created"() {

        Component component1 = build1.findOrCreateComponent("Leadframe", Source.VYPER)
        component1.setValue(0, 0, "1234567-0001", Engineering.N, Source.VYPER)

        when:
        loader.load(vyper1, build1)

        then:
        1 * atssComponentService.ecat("THE_DEVICE", "THE_FACILITY", Status.ACTIVE) >> null
        1 * attributeService.findByName("THE_FACILITY", "Leadframe", "1234567-0001") >> [[key: "Environmental Ctgry", value: "4"]]

        and:
        build1.components.size() == 2
        build1.components[0].name == "Leadframe"
        build1.components[1].name == "ECAT"

        and:
        build1.components[1].instances.size() == 0
    }

    @Unroll
    def "the correct ECAT is determined - #finish and #category is #result"() {

        Component component1 = build1.findOrCreateComponent("Leadframe", Source.VYPER)
        component1.setValue(0, 0, "1234567-0001", Engineering.N, Source.VYPER)

        when:
        loader.load(vyper1, build1)

        then:
        1 * atssComponentService.ecat("THE_DEVICE", "THE_FACILITY", Status.ACTIVE) >> null
        1 * attributeService.findByName("THE_FACILITY", "Leadframe", "1234567-0001") >>
                [[key: "Finish", value: finish], [key: "Environmental Ctgry", value: category]]

        and:
        build1.components.size() == 2
        build1.components[0].name == "Leadframe"
        build1.components[1].name == "ECAT"

        and:
        build1.components[1].getValue(0, 0) == result

        where:
        finish   | category | result
        "NIPDAU" | "4"      | "G4"
        "NIPDAU" | "3"      | "G3"
        "SN"     | "4"      | "E4"
        "SN"     | "3"      | "E3"
    }

}
