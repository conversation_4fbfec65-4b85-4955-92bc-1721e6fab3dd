package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.atss.traveler.Status;
import com.ti.specteam.vyper.build.model.TemplateSource;
import com.ti.specteam.vyper.template.TemplateType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TemplateSourceForm {

    private TemplateType templateType = TemplateType.DEVICE_PKGNICHE;
    private String atssFacility;
    private String atssMaterial;
    private Status atssStatus;
    private String vyperBuildNumber;
    private String similarPkgNiche;

    public TemplateSourceForm(TemplateSource templateSource) {
        templateType = templateSource.getTemplateType();
        atssFacility = templateSource.getAtssFacility();
        atssMaterial = templateSource.getAtssMaterial();
        atssStatus = templateSource.getAtssStatus();
        vyperBuildNumber = templateSource.getVyperBuildNumber();
        similarPkgNiche = templateSource.getSimilarPkgNiche();
    }

}
