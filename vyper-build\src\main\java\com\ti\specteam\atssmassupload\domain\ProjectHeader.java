package com.ti.specteam.atssmassupload.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Entity
@Data
@Table(name = "MU_PROJ_VW")
public class ProjectHeader {
    @Id
    private String projId;
    private String projName;
    private String projNumber;
    private String projType;
    private String projStatus;
    private String facilityAt;
    private String refSpecId;
    private String refType;
    private String refSpecDevice;
    private String refFacilityAt;
    private String refVyperBuildNumber;
    private String refVyperPraNumber;
    private String refStatus;
    private String cmsNumber;
    private String ownerId;
    private String createdBy;
    private String createdDttm;
    private String updatedBy;
    private String updatedDttm;
    private String buildState;
    private String praState;

}
