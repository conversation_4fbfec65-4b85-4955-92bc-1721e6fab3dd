package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.compare.CompareService;
import com.ti.specteam.vyper.apitraveler.export.TravelerToTextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.JDOMException;
import org.springframework.stereotype.Service;

import javax.xml.soap.SOAPException;
import java.io.IOException;

@Service
@Slf4j
@RequiredArgsConstructor
public class CompareTravelerAction {

    private final TravelerToTextService travelerToTextService;
    private final CompareService compareService;
    private final BuildService buildService;

    /**
     * Compare a Vyper (ATSS Style) traveler to an actual ATSS traveler.
     *
     * @param compareTravelerForm CompareTravelerForm
     * @return CompareResult the results
     * @throws SOAPException If accessing SpecTravSS fails
     * @throws IOException   If accessing SpecTravSS fails
     * @throws JDOMException If parsing the traveler xml fails
     */
    public CompareTravelerResult execute(CompareTravelerForm compareTravelerForm) throws SOAPException, IOException, JDOMException {
        log.debug("execute(compareTravelerForm:{})", compareTravelerForm);

        Build build1 = buildService.findByBuildNumber(compareTravelerForm.getBuildNumber1());
        Build build2 = buildService.findByBuildNumber(compareTravelerForm.getBuildNumber2());

        CompareTravelerResult result = new CompareTravelerResult();

        result.getDifferences().addAll(compareService.compare(
                travelerToTextService.getTraveler(build1),
                travelerToTextService.getTraveler(build2)
        ));

        return result;
    }

}
