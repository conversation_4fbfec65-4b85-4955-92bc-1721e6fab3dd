package com.ti.specteam.vyper.actions;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ti.specteam.vyper.build.model.CustomObject;
import com.ti.specteam.vyper.build.model.SymbolObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeSymbolizationForm extends BuildNumberForm {

    @NotNull
    private SymbolObject symbol;

    @NotNull
    private List<CustomObject> customs;

    @NotNull
    private String ecat;

    @JsonIgnore
    public String display() {
        List<String> names = new ArrayList<>();
        names.add(symbol.getName());
        for (CustomObject customObject : customs) {
            names.add(customObject.getValue());
        }
        return String.join(", ", names);
    }

}
