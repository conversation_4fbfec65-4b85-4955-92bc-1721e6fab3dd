package com.ti.specteam.vyper.actions;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ti.specteam.vyper.build.model.SelectionItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeSelectionForm extends BuildNumberForm {

    @NotNull
    private String operation;

    @NotNull
    @Size(min = 1)
    private String name;

    @NotNull
    private List<SelectionItem> items;

    @JsonIgnore
    public String display() {
        return items.stream().map(SelectionItem::getValue).collect(Collectors.joining(", "));
    }

}
