package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.armarc.ArmArcRepository2;
import com.ti.specteam.vyper.armarc.Armarc2;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.ComponentInstance;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.pra.PraDiagramVerifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.ti.specteam.vyper.security.user.UserUtilsService;

import static com.ti.specteam.vyper.audit.AuditActivity.PRA_REFRESH_MB_DIAGRAMS;

@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshPraDiagramsAction {

    private final VyperService vyperService;
    private final PraService praService;
    private final ValidateService validateService;
    private final ArmArcRepository2 armArcRepository;
    private final RefreshPraAction refreshPraAction;
    private final AuditService auditService;
    private final UserUtilsService userUtilsService;

    public Pra execute(PraNumberForm praNumberForm) {
        log.debug("execute(praNumberForm:{})", praNumberForm);

        // grab the objects
        Vyper vyper = vyperService.fetchVyper(praNumberForm);
        Pra pra = praService.fetchPra(praNumberForm);

        // validate current user can do this
        userUtilsService.validateUserByFacility(pra.getFacility().getPdbFacility());
        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, pra);

        execute(pra);
        praService.savePra(pra);

        auditService.createPra(
                pra.getVyperNumber(),
                pra.getPraNumber(),
                pra.getBuildNumber(),
                PRA_REFRESH_MB_DIAGRAMS,
                "refresh mb diagrams");

        // refresh the pra
        return refreshPraAction.execute(praNumberForm);
    }

    public void execute(Pra pra){
        // loop through the components
        // get the mb diagrams
        // filter out non-pavv
        // query packageDb for updated data
        // update out component priority

        pra.getComponents().stream()
                .filter(component -> StringUtils.equalsIgnoreCase(component.getName(), "MB Diagram"))
                .flatMap(Component::stream)
                .flatMap(ComponentInstance::stream)
                .filter(componentPriority -> componentPriority.getValue().matches(PraDiagramVerifier.PAVV_REGEX))
                .forEach(componentPriority -> {

                    Armarc2 armarc = armArcRepository.findLatestArmarcByDiagram(componentPriority.getValue());
                    if(null != armarc) {
                        componentPriority.getObject().put("revision", armarc.getRevision());
                    }

                });
    }

}
