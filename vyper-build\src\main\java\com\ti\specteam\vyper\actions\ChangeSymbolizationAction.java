package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.atss.paragraph.ReplacementService;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_SYMBOLIZATION;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeSymbolizationAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final CustLoader custLoader;
    private final FlowLoader flowLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final ReplacementService replacementService;
    private final PackLoader packLoader;

    public Build execute(ChangeSymbolizationForm changeSymbolizationForm) {
        log.debug("execute(symbolizationForm:{})", changeSymbolizationForm);

        Vyper vyper = vyperService.fetchVyper(changeSymbolizationForm);
        Build build = buildService.fetchBuild(changeSymbolizationForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAt(vyper, build);
        validateService.checkEditable(vyper, build);

        // add the symbol
        Symbol symbol = new Symbol();
        symbol.setObject(changeSymbolizationForm.getSymbol());
        symbol.getSource().appointUser(securityService.user());

        build.getSymbolization().getSymbols().clear();
        build.getSymbolization().getSymbols().add(symbol);

        // add components for the new cust values
        changeSymbolizationForm.getCustoms().forEach(customObject -> {
            Component component = build.findOrCreateComponent(
                    customObject.getName(),
                    new Source().appointUser(securityService.user()));
            component.clear();
            component.setValue(
                    0,
                    0,
                    customObject.getValue(),
                    Engineering.N,
                    new Source().appointUser(securityService.user())
            );

            component.getSource().appointUser(securityService.user());

            component.setKeyValue(
                    0,
                    0,
                    "ignoreBlank",
                    customObject.getIgnoreBlank(),
                    Engineering.N,
                    new Source().appointUser(securityService.user())
            );

            build.clearSelections(customObject.getName());
        });

        // create/update the ecat
        Source source = new Source().appointUser(securityService.user());
        Component ecat = build.findOrCreateComponent("ECAT", source);
        ecat.clear();
        ecat.setValue(
                0,
                0,
                changeSymbolizationForm.getEcat(),
                Engineering.N,
                new Source().appointUser(securityService.user())
        );

        // update the ecat selection the selection
        Selection selection = build.findOrCreateSelection("Symbol", "ECAT", Source.VYPER);
        selection.setItem(changeSymbolizationForm.getEcat(), Engineering.N, source);


        // update the rest of the data
        custLoader.load(vyper, build);
        packLoader.load(vyper, build);
        flowLoader.load(vyper, build);
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);

        // update the display - do this after setting the selection
        SymbolObject symbolObject = symbol.getObject();
        String text = replacementService.replaceTemplateText(build, symbolObject.getPicture());
        symbolObject.setDisplay(text);

        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_SYMBOLIZATION,
                "changed Symbol to: " + changeSymbolizationForm.display()
        );

        return buildService.saveBuild(build);
    }

}
