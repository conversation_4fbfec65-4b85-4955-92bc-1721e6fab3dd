package com.ti.specteam.atssmassupload.domain;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */

@Entity
@Table(name = "MU_PROJ_DEVICE")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class ProjectDevice {

	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private String id;

	@Column(name = "PROJ_ID", columnDefinition = "raw(16)", nullable = false)
	private String projectId;

	@Column(name = "MATERIAL", length = 20, nullable = false)
	private String material;

	@Column(name = "OLD_MATERIAL", length = 20, nullable = false)
	private String oldMaterial;

	@Column(name = "SPEC_DEVICE", length = 20, nullable = false)
	private String specDevice;

	@Column(name = "REF_SPEC_ID", columnDefinition = "raw(16)", nullable = true)
	private String refSpecId;

	@Column(name = "VSCN_NUMBER", length = 20, nullable = true)
	private String vscnNumber;

	@Column(name = "SCN_ID", length = 20, nullable = true)
	private String scnId;

	@Column(name = "STATUS", length = 20, nullable = true)
	private String status;

	@Column(name = "UI_SEQUENCE", length = 10, nullable = true)
	private Integer uiSequence;
	@Column(name = "IS_MULTI_BUILD", length = 1, nullable = false , columnDefinition = "number(1) default 0")
	private Boolean isMultiBuild = false;

	@Column(name = "CMS_NUMBER", length = 10, nullable = true)
	private String cmsNumber;

	@OneToMany(mappedBy = "projectDevice", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
	List<SpecChange> specChanges = new ArrayList<>();
}
