package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.armarc.ArmArcLookupService;
import com.ti.specteam.vyper.armarc.Armarc;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.ComponentPriority;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.componentlistener.processor.LeadframeProcessor;
import com.ti.specteam.vyper.componentlistener.processor.MoldCompoundProcessor;
import com.ti.specteam.vyper.componentlistener.processor.MountCompoundProcessor;
import com.ti.specteam.vyper.componentlistener.processor.WireProcessor;
import com.ti.specteam.vyper.componentlistener.processor.utils.MatchContext;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class CheckArmarcAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final ArmArcLookupService armArcLookupService;
    private final LeadframeProcessor leadframeProcessor;
    private final MoldCompoundProcessor moldCompoundProcessor;
    private final MountCompoundProcessor mountCompoundProcessor;
    private final WireProcessor wireProcessor;

    public Build execute(CheckArmarcForm checkArmarcForm) {
        log.debug("execute(checkArmarcForm:{})", checkArmarcForm);

        Vyper vyper = vyperService.fetchVyper(checkArmarcForm);
        Build build = buildService.fetchBuild(checkArmarcForm);

        validateService.checkEditable(vyper, build);

        // get the armarc object
        Armarc armarc = armArcLookupService.findArmarc(build);

        if(armarc != null){
            checkMountCompound(build.getComponents(), armarc.getMountCompound());
            checkMoldCompound(build.getComponents(), armarc.getMoldCompound());
            checkLeadframe(build.getComponents(), armarc.getLeadframe());
            checkWire(build.getComponents(), armarc.getWireDiameter(), armarc.getWireType());
            return buildService.saveBuild(build);
        }

        return build;
    }

    private void checkMountCompound(List<Component> components, String value) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Mount Compound", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (mountCompoundProcessor.matchValue(priority, value, context)) {
            priority.setArmarcCheckMessage(null);
        } else {
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            String message = "Mount Compound: ARMARC = " + value + ", Build = " + priority.getValue() + pn;
            priority.setArmarcCheckMessage(message);
        }

    }

    private void checkMoldCompound(List<Component> components, String value) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Mold Compound", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (moldCompoundProcessor.matchValue(priority, value, context)) {
            priority.setArmarcCheckMessage(null);
        } else {
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            String message = "Mold Compound: ARMARC = " + value + ", Build = " + priority.getValue() + pn;
            priority.setArmarcCheckMessage(message);
        }

    }

    private void checkLeadframe(List<Component> components, String value) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Leadframe", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (leadframeProcessor.matchValue(priority, value, context)) {
            priority.setArmarcCheckMessage(null);
        } else {
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            String message = "Leadframe: ARMARC = " + value + ", Build = " + priority.getValue() + pn;
            priority.setArmarcCheckMessage(message);
        }

    }

    private void checkWire(List<Component> components, String armarcDiameter, String armarcType) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Wire", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (wireProcessor.matchValue(priority, armarcDiameter, armarcType, context)) {
            priority.setArmarcCheckMessage(null);
        } else {
            String armarcWire = StringUtils.trim(armarcType + " " + armarcDiameter);
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            String message = "Wire: ARMARC = " + armarcWire + ", Build = " + priority.getValue() + pn;
            priority.setArmarcCheckMessage(message);
        }

    }

}
