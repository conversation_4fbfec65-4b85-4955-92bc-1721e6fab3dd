package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntransitStatus {
    private String shippingPlant;
    private String shippingFacility;
    private String receivingPlant;
    private String receivingFacility;
    private String material;
    private String batch;
    private String poNumber;
    private String poLineItem;
    private String scheduleLine;
    private String waybill;
    private String deliveryNumber;
    private String deliveryItem;
    private String receiptDate;
    private String shipDate;
    private String qty;
    private String waferQty;
}
