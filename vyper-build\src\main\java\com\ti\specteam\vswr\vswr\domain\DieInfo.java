package com.ti.specteam.vswr.vswr.domain;

import java.util.List;
import java.util.ArrayList;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DieInfo {

    private String vswrID;
    
    private int sequence;
    
    private int priority;
    
    private String matlMasterDieName;

    private boolean selected;

    private String plant;
    
    @JsonAlias({"Die Revision Major","Die Revision"})
    private String dieRev;
    
    @JsonAlias({"Die Designator", "Designator"})
    private String dieDesignator;
    
    @JsonAlias("Scribe Width X um")
    private String xScribeWidth;
    
    @JsonAlias("Scribe Width Y um")
    private String yScribeWidth;

    @JsonAlias("Scribe Width")
    private String scribeWidth;
    
    @JsonAlias({"Die Size With Scribe X", "Die Size X UM" })
    private String xDieSize;
    
    @JsonAlias({"Die Size With Scribe Y","Die Size Y UM"})
    private String yDieSize;
    
    private String dieSize;
    
    @JsonAlias({"Fab Source Code","Fab Code"})
    private String fabCode;
    
    @JsonAlias({"LRPTech","Process"})
    private String fabTechnology;
    
    @JsonAlias({"Wafer Diameter mm","Wafer Diameter"})
    private String waferDiameter;
    
    @JsonAlias("Incoming Wafer Thick")
    private String waferThickness;
    
    @JsonAlias("Final Backgrind Thickness")
    private String backgrindThickness;

    @Builder.Default
    private List<DieLotInfo> dieLots = new ArrayList<DieLotInfo>();
}
