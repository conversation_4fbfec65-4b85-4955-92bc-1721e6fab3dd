package com.ti.specteam.atssmassupload.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.ProjectEntity;
import com.ti.specteam.atssmassupload.domain.ReferenceSpecEntity;
import com.ti.specteam.atssmassupload.domain.SpecChange;
import com.ti.specteam.atssmassupload.domain.SpecChangeDataSheetGrid;
import com.ti.specteam.atssmassupload.domain.SpecChangeMaterialPlanFactors;
import com.ti.specteam.atssmassupload.domain.SpecChangeRule;
import com.ti.specteam.atssmassupload.domain.SpecChangeTestProgram;

/**
 *
 * <AUTHOR>
 */
@Validated

public interface SpecChangeService {

	public List<SpecChangeDataSheetGrid> findSpecChangesByProjectId(String projId);

	@Transactional
	public void saveSpecChanges(ProjectEntity projectEntity,
			Map<ProjectDevice, List<SpecChange>> projectDeviceSpecChangesMap);

	public List<SpecChangeTestProgram> findSpecChangeTestProgram(List<String> programNames);

	public List<SpecChangeMaterialPlanFactors> findSpecChangeMaterialsPlanFactor(List<String> materials);

	public Optional<ReferenceSpecEntity> findReferenceSpecEntity(String projNumber);

	public List<SpecChangeRule> findAllSpecChangeRule();

	public void deleteSpecChanges(Map<ProjectDevice, List<SpecChange>> projectDeviceSpecChangesMap,
			ProjectDevice projectDevice);

}