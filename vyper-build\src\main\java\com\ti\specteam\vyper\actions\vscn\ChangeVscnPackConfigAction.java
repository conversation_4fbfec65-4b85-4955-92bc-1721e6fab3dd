package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.flow.FlowService;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfig;
import com.ti.specteam.vyper.config.flowpackconfig.FlowPackConfigService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.template.TemplateType;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeVscnPackConfigAction {
    private final VyperService vyperService;
    private final BuildService buildService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final FlowPackConfigService flowPackConfigService;
    private final BomTemplateLoader bomTemplateLoader;
    private final FlowLoader flowLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final AuditService auditService;
    private final FlowService flowService;
    private final PackLoader packLoader;

    public Vscn execute(ChangeVscnPackConfigForm changeVscnPackConfigForm) {
        log.debug("execute(changeVscnPackConfigForm:{})", changeVscnPackConfigForm);

        Vscn vscn = vscnService.fetchVscn(changeVscnPackConfigForm.getVscnNumber());
        Vyper vyper = vyperService.fetchVyper(vscn.getVyperNumber());
        Build buildCopy = buildService.fetchBuild(vscn.getBuildNumber());

        buildCopy.getTemplateSource().setTemplateType(TemplateType.ATSS);

        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        if (buildCopy.getBuildFlow().getFlowId() == null || buildCopy.getBuildFlow().getFlowName().equalsIgnoreCase("TKY")) {
            vscn.getPackConfig().getObject().put("value", changeVscnPackConfigForm.getValue());
            vscn.getPackConfig().getSource().appointUser(securityService.user());
            buildCopy.getPackConfig().getObject().put("value", changeVscnPackConfigForm.getValue());
            buildCopy.getPackConfig().getSource().appointUser(securityService.user());
        } else {
            List<FlowPackConfig> flowPackConfigs = flowPackConfigService.getAllPackConfigByFlowId(buildCopy.getBuildFlow().getFlowId());
            List<String> packConfigList = flowPackConfigs.stream().map(flowPackConfig -> flowPackConfig.getPackConfig().getValue().toString()).collect(Collectors.toList());
            if (packConfigList.indexOf(changeVscnPackConfigForm.getValue()) > -1) {
                vscn.getPackConfig().getObject().put("value", changeVscnPackConfigForm.getValue());
                vscn.getPackConfig().getSource().appointSystem(SystemName.PGS);
                buildCopy.getPackConfig().getObject().put("value", changeVscnPackConfigForm.getValue());
                buildCopy.getPackConfig().getSource().appointUser(securityService.user());
            } else {
                throw new VyperException("Pack Config is invalid for the current flow");
            }
        }

        // clear the existing pack
        flowService.removeOperationsBySubFlow(buildCopy, SubFlow.PACK);

        bomTemplateLoader.load(vyper, buildCopy);
        packLoader.load(vyper, buildCopy);
        flowLoader.load(vyper, buildCopy);
        requiredComponentLoader.load(vyper, buildCopy);
        selectionLoader.load(vyper, buildCopy);
        travelerRefreshService.load(vyper, buildCopy);

        List<TravelerOperation> packTravelerOperationsList = buildCopy.getTraveler().getOperations().stream()
                .filter(travelerOperation -> isPack(travelerOperation))
                .collect(Collectors.toList());

        vscn.getTraveler().getOperations().removeIf(travelerOperation -> isPack(travelerOperation));
        vscn.getTraveler().getOperations().addAll(packTravelerOperationsList);

        // add PACK group to list of VSCN approvers
        String packGroup ="VYPER_"+vscn.getFacility().getPdbFacility()+"_PACK";
        if(!vscn.getChangedComponentsGroup().contains(packGroup)){
            vscn.getChangedComponentsGroup().add(packGroup);
        }

        auditService.createVscn(
                vscn.getVyperNumber(),
                vscn.getVscnNumber(),
                AuditActivity.VSCN_CHANGE_PACK_CONFIG,
                "changed pack config to: " + changeVscnPackConfigForm.getValue());

        return vscnService.saveVscn(vscn);
    }

    public boolean isPack(TravelerOperation travelerOperation){
        return StringUtils.equalsIgnoreCase(travelerOperation.getSubflowType(),"PACK");
    }
}
