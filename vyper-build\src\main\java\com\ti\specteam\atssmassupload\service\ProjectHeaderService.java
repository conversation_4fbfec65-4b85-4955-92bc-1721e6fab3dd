package com.ti.specteam.atssmassupload.service;

import com.ti.specteam.atssmassupload.domain.ProjectEntity;
import com.ti.specteam.atssmassupload.domain.ProjectHeader;
import com.ti.specteam.atssmassupload.domain.ProjectHeaderForm;
import com.ti.specteam.atssmassupload.domain.ReferenceSpecEntity;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadException;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadProjectNotFound;
import com.ti.specteam.atssmassupload.repository.ProjectEntityRepository;
import com.ti.specteam.atssmassupload.repository.ProjectHeaderRepository;
import com.ti.specteam.atssmassupload.repository.ReferenceSpecEntityRepository;
import com.ti.specteam.foundational.smarttable.SmartColumn;
import com.ti.specteam.foundational.smarttable.SmartResult;
import com.ti.specteam.foundational.smarttable.SmartTableService;
import com.ti.specteam.vyper.actions.*;
import com.ti.specteam.vyper.atss.device.AtssMaterialRepository;
import com.ti.specteam.vyper.atss.traveler.Status;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.entity.vyper.VyperEntity;
import com.ti.specteam.vyper.entity.vyper.VyperEntityService;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.pra.model.PraState;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.verifier.model.ValidatedComponent;
import com.ti.specteam.vyper.vscn.actions.CreateVscnForm;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.template.TemplateType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.AUTO_VALIDATE_OPERATIONS;
import static com.ti.specteam.vyper.audit.AuditActivity.PRA_AUTO_CHECK_COMPONENTS;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjectHeaderService {

    private final ProjectHeaderRepository projectHeaderRepository;
    private final SmartTableService smartTableService;
    private final List<SmartColumn> columns = new ArrayList<>();
    private final ProjectEntityRepository projectEntityRepository;
    private final ReferenceSpecEntityRepository referenceSpecEntityRepository;
    private final SecurityService securityService;
    private final VyperEntityService vyperEntityService;
    private final AddBuildAction addBuildAction;
    private final AtssMaterialRepository atssMaterialRepository;
    private final BuildService buildService;
    private final VyperService vyperService;
    private final CreatePraAction createPraAction;
    private final RefreshPraAction refreshPraAction;
    private final AuditService auditService;
    private final RefreshPraDiesAction refreshPraDiesAction;
    private final RefreshPraDiagramsAction refreshPraDiagramsAction;
    private final PraService praService;
    private final ProjectWorkflowService projectWorkflowService;
    private final ProjectDeviceVscnService projectDeviceVscnService;
    private final UserUtilsService userUtilsService;

    public void run() {
        log.debug("run()");
        columns.add(new SmartColumn("projNumber", "PROJ_NUMBER", true));
        columns.add(new SmartColumn("projName", "PROJ_NAME", true));
        columns.add(new SmartColumn("projType", "PROJ_TYPE", true));
        columns.add(new SmartColumn("projStatus", "PROJ_STATUS", true));
        columns.add(new SmartColumn("facilityAt", "FACILITY_AT", true));
        columns.add(new SmartColumn("refType", "REF_TYPE", true));
        columns.add(new SmartColumn("refSpecDevice", "REF_SPEC_DEVICE", true));
        columns.add(new SmartColumn("refFacilityAt", "REF_FACILITY_AT", true));
        columns.add(new SmartColumn("refVyperBuildNumber", "REF_VYPER_BUILD_NUMBER", true));
        columns.add(new SmartColumn("refVyperPraNumber", "REF_VYPER_PRA_NUMBER", true));
        columns.add(new SmartColumn("refStatus", "REF_STATUS", true));
        columns.add(new SmartColumn("cmsNumber", "CMS_NUMBER", true));
        columns.add(new SmartColumn("ownerId", "OWNER_ID", true));
        columns.add(new SmartColumn("createdBy", "CREATED_BY", true));
        columns.add(new SmartColumn("createdDttm", "CREATED_DTTM", true));
        columns.add(new SmartColumn("updatedBy", "UPDATED_BY", true));
        columns.add(new SmartColumn("updatedDttm", "UPDATED_DTTM", true));
    }

    public SmartResult<ProjectHeader> search(Pageable pageable, List<String> filters) {

        log.debug("search(pageable:{}, filters:{})", pageable, filters);
        if (!userUtilsService.isInternalUser()){
            List<String> listFacility = userUtilsService.getUserApiAccessSitesOrEmpty();
            filters.add(0, "projStatus|in|AT REVIEW");
            filters.add(0, "facilityAt|in|" + String.join(";", listFacility));
        }
        return smartTableService.search(projectHeaderRepository, columns, pageable, filters);

    }

    public ProjectEntity create(ProjectHeaderForm projectHeaderForm) {
        log.debug("create(projectHeaderForm:{})", projectHeaderForm);

        com.ti.specteam.vyper.security.user.User user = securityService.user();
        Date now = new Date();

        ProjectEntity project = new ProjectEntity();
        if (!projectHeaderForm.getProjType().equals("NEW") && projectHeaderForm.getRefStatus() == null) {
            throw new AtssMassUploadException("Traveler information not found.");
        }

        if (projectHeaderForm.getRefStatus() != null) {
            project.setReferenceSpec(new ReferenceSpecEntity());
            project.getReferenceSpec().setRefType("ATSS");
            project.getReferenceSpec().setCreatedBy(user.getUserid());
            project.getReferenceSpec().setCreatedDttm(now);
            project.getReferenceSpec().setUpdatedBy(user.getUserid());
            project.getReferenceSpec().setUpdatedDttm(now);
        }

        copy(project, projectHeaderForm);

        ReferenceSpecEntity referenceSpec = new ReferenceSpecEntity();

        if (projectHeaderForm.getRefStatus() != null) {
            referenceSpec = referenceSpecEntityRepository.save(project.getReferenceSpec());
        }

        if (projectHeaderForm.getStatus() == null) {
            project.setStatus("DRAFT");
        }

        if (projectHeaderForm.getRefStatus() != null) {
            // creating vyper project
            VyperEntity vyperEntity = vyperEntityService.create();
            Vyper vyper = vyperEntityService.fromJson(vyperEntity.getJson());

            // fetching sap material with spec device and facility from atss
            String sapMaterial = atssMaterialRepository.findSapMaterialBySpecDeviceAndFacility(
                    projectHeaderForm.getSpecDevice(), projectHeaderForm.getFacilityAt());

            // creating vyper build
            AddBuildForm addBuildForm = new AddBuildForm();
            addBuildForm.setVyperNumber(vyper.getVyperNumber());
            addBuildForm.setMaterial(sapMaterial);
            populateAddBuildFormRefTraveler(addBuildForm, projectHeaderForm);
            Build build = addBuildAction.execute(addBuildForm);

            // save buildNumber in referenceSpecTable
            referenceSpec.setVyperBuildNumber(build.getBuildNumber());
            referenceSpecEntityRepository.save(referenceSpec);

            if (projectHeaderForm.getFacilityAt().equals(projectHeaderForm.getTargetFacility()) && projectHeaderForm.getRefStatus().equalsIgnoreCase("A")) {
                User systemUser = new User();
                systemUser.setUserid("a0999999");
                systemUser.setUsername("SYSTEM");
                //auto check the operations by the system
                build.getValidatedOperations().stream()
                        .map(validatedOperation -> {
                            validatedOperation.setUserid("a0999999");
                            validatedOperation.setUsername("SYSTEM");
                            validatedOperation.setWhen(OffsetDateTime.now());
                            return validatedOperation;
                        })
                        .collect(Collectors.toList());

                Component mbDiagramComponent = build.findComponentByName("MB Diagram");
                if (mbDiagramComponent != null) {
                    List<ComponentPriority> mbDiagramPriorities = mbDiagramComponent.getPriorities(0);
                    if (mbDiagramPriorities != null && !mbDiagramPriorities.isEmpty()) {
                        mbDiagramPriorities.stream().distinct().forEach(componentPriority -> {
                            DiagramApproval diagramApproval = new DiagramApproval();
                            UserTime systemUserTime = new UserTime();
                            systemUserTime.setUser(systemUser);
                            diagramApproval.setApprover(systemUserTime);
                            diagramApproval.setType(DiagramApprovalType.PRODUCTION);
                            build.getDiagramApprovals().put(componentPriority.getValue(), diagramApproval);
                        });
                    }
                }

                String buildDetail = "All operations are validated by SYSTEM";

                // save the audit
                auditService.createBuild(
                        vyper.getVyperNumber(),
                        build.getBuildNumber(),
                        AUTO_VALIDATE_OPERATIONS,
                        buildDetail
                );

                build.getSubmitter().getUser().setUsername(user.getUsername());
                build.getSubmitter().getUser().setUserid(user.getUserid());
                build.setState(BuildState.FINAL_APPROVED);
            }

            buildService.saveBuild(build);
        }

        project.setProjNumber(projectEntityRepository.generateProjectNumber());
        project.setRefSpecId(referenceSpec.getId());
        project.setOwnerId(user.getUserid());
        project.setCreatedBy(user.getUserid());
        project.setCreatedDttm(now);
        project.setUpdatedBy(user.getUserid());
        project.setUpdatedDttm(now);
        return projectEntityRepository.save(project);
    }

    public ProjectHeader createPraForRefTraveler(ProjectHeader projectHeader) {
        if (projectHeader == null) {
            throw new AtssMassUploadProjectNotFound("No projectNumber found");
        }

        Build build = buildService.fetchBuild(projectHeader.getRefVyperBuildNumber());
        if (build == null) {
            throw new AtssMassUploadException("No Build found for projectNumber " + projectHeader.getProjId());
        } else if (!build.getState().equals(BuildState.FINAL_APPROVED)) {
            throw new AtssMassUploadException("Build should be in final aproved state to create pra for projectNumber " + projectHeader.getProjId());
        }

        String vyperNumber = Build.convertBuildNumbertoVyperNumber(build.getBuildNumber());
        Vyper vyper = vyperService.findByVyperNumber(vyperNumber);
        if (vyper == null) {
            throw new AtssMassUploadException("No Vyper project found for projectNumber " + projectHeader.getProjId());
        }

        ReferenceSpecEntity referenceSpec = referenceSpecEntityRepository.getReferenceById(projectHeader.getRefSpecId());

        if (referenceSpec == null) {
            throw new AtssMassUploadException("No Reference Traveler found for projectNumber " + projectHeader.getProjId());
        }

        if (projectHeader.getRefStatus() != null) {
            CreatePraForm createPraForm = new CreatePraForm();
            createPraForm.setBuildNumber(build.getBuildNumber());
            createPraForm.setVyperNumber(vyper.getVyperNumber());

            //create the pra
            Pra pra = createPraAction.execute(createPraForm);

            // save praNumber in referenceSpecTable
            referenceSpec.setVyperPraNumber(pra.getPraNumber());
            referenceSpecEntityRepository.save(referenceSpec);

            // refresh the pra
            PraNumberForm praNumberForm = new PraNumberForm();
            praNumberForm.setVyperNumber(createPraForm.getVyperNumber());
            praNumberForm.setPraNumber(pra.getPraNumber());
            refreshPraDiesAction.execute(pra);
            refreshPraDiagramsAction.execute(pra);
            praService.savePra(pra);
            pra = refreshPraAction.execute(praNumberForm);

            if (projectHeader.getFacilityAt().equals(projectHeader.getRefFacilityAt()) && projectHeader.getRefStatus().equals("A")) {
                // auto check the components by the system
                HashMap<String, ValidatedComponent> validatedComponents = pra.getValidatedComponents();
                validatedComponents.keySet().stream()
                        .map(key -> validatedComponents.get(key))
                        .map(validatedComponent -> {
                            validatedComponent.setUsername("SYSTEM");
                            validatedComponent.setUserid("a0999999");
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ssZ");
                            validatedComponent.setWhen(simpleDateFormat.format(new Date()));
                            validatedComponent.setChecked(true);
                            return validatedComponent;
                        })
                        .collect(Collectors.toList());

                String praDetail = "All components checked by SYSTEM";

                auditService.createPra(
                        pra.getVyperNumber(),
                        pra.getPraNumber(),
                        pra.getBuildNumber(),
                        PRA_AUTO_CHECK_COMPONENTS,
                        praDetail);

                pra.setState(PraState.PRA_APPROVED);
                praService.savePra(pra);
            }
        }


        return projectHeaderRepository.findByProjNumber(projectHeader.getProjNumber());
    }

    public ProjectEntity update(String projNumber, ProjectHeaderForm projectHeaderForm) {

        log.debug("edit(projectHeaderForm:{})", projectHeaderForm);
        com.ti.specteam.vyper.security.user.User user = securityService.user();
        Date now = new Date();

        ProjectEntity project = projectEntityRepository.findByProjNumber(projNumber);
        copy(project, projectHeaderForm);
        project.setProjNumber(projNumber);
        project.setUpdatedBy(user.getUserid());
        project.setUpdatedDttm(now);
        return projectEntityRepository.save(project);

    }

    public ProjectEntity copy(ProjectEntity project, ProjectHeaderForm projectHeaderForm) {

        if (projectHeaderForm.getProjName() != null) {
            project.setProjName(projectHeaderForm.getProjName());
        }

        if (projectHeaderForm.getProjType() != null) {
            project.setProjType(projectHeaderForm.getProjType());
        }

        if (projectHeaderForm.getFacilityAt() != null) {
            project.setFacilityAt(projectHeaderForm.getFacilityAt());
            if (projectHeaderForm.getRefStatus() != null) {
                project.getReferenceSpec().setFacilityAt(projectHeaderForm.getFacilityAt());
            }
        }

        if (projectHeaderForm.getCmsNumber() != null) {
            project.setCmsNumber(projectHeaderForm.getCmsNumber());
        }

        if (projectHeaderForm.getTargetFacility() != null) {
            project.setFacilityAt(projectHeaderForm.getTargetFacility());
        }

        if (projectHeaderForm.getStatus() != null) {
            project.setStatus(projectHeaderForm.getStatus());
        }

        if (projectHeaderForm.getRefStatus() != null) {
            if (projectHeaderForm.getSpecDevice() != null) {
                project.getReferenceSpec().setSpecDevice(projectHeaderForm.getSpecDevice());
            }

            if (projectHeaderForm.getRefStatus() != null) {
                project.getReferenceSpec().setStatus(projectHeaderForm.getRefStatus());
            }
        }

        return project;
    }

    public ProjectHeader getProjectHeaderById(String projId) {
        return validateUserByProjectId(projId);
    }

    public ProjectHeader getProjectHeaderByIdForInternal(String projId) {
        return projectHeaderRepository.findById(projId);
    }


    public ProjectHeader validateUserByProjectId(String projId) {
        ProjectHeader projectHeader = projectHeaderRepository.findById(projId);
        if (! userUtilsService.isUserHasAccessToFacility(projectHeader.getFacilityAt())){
            throw new AtssMassUploadException("User do not have permissions");
        };
        return projectHeader;
    }

    public ProjectHeader validateUserByProjectNumber(String projNumber) {
        ProjectHeader projectHeader = projectHeaderRepository.findByProjNumber(projNumber);
        if (! userUtilsService.isUserHasAccessToFacility(projectHeader.getFacilityAt())){
            throw new AtssMassUploadException("User do not have permissions");
        };
        return projectHeader;
    }

    public ProjectHeader getProjectHeaderByProjNumber(String projNumber) {
        return validateUserByProjectNumber(projNumber);
    }

    public void populateAddBuildFormRefTraveler(AddBuildForm addBuildForm, ProjectHeaderForm projectHeaderForm) {
        addBuildForm.setMode("BUILD_REFERENCE");

        addBuildForm.setFacility(projectHeaderForm.getTargetFacility());
        addBuildForm.setDescription(projectHeaderForm.getProjName());
        addBuildForm.setMultiBuild(false);
        addBuildForm.setSpecDevice(projectHeaderForm.getSpecDevice());
        addBuildForm.setBuildType("Reference");
        addBuildForm.setBuildFlow("TKY");

        TemplateSourceForm templateSourceForm = new TemplateSourceForm();

        templateSourceForm.setTemplateType(TemplateType.ATSS_FULL_COPY);
        templateSourceForm.setAtssMaterial(projectHeaderForm.getSpecDevice());
        templateSourceForm.setAtssFacility(projectHeaderForm.getFacilityAt());
        if (projectHeaderForm.getRefStatus().equalsIgnoreCase("A")) {
            templateSourceForm.setAtssStatus(Status.ACTIVE);
        } else if (projectHeaderForm.getRefStatus().equalsIgnoreCase("W")) {
            templateSourceForm.setAtssStatus(Status.WORKING);
        }
        addBuildForm.setTemplateSourceForm(templateSourceForm);
    }

    public ProjectEntity changeWorkFlow(String projNumber, String projectStatus) {
        return projectWorkflowService.changeWorkFlow(projNumber, projectStatus);
    }

    public List<String> createProjectVscns(String projNumber, CreateVscnForm vscnForm) {
        log.debug("createProjectDeviceVscns(projNumber:{},vscnForm:{})", projNumber, vscnForm);
        return projectDeviceVscnService.createProjectDeviceVscns(projNumber, vscnForm);
    }
}
