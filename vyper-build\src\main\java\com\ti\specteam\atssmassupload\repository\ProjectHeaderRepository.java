package com.ti.specteam.atssmassupload.repository;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.ti.specteam.atssmassupload.domain.ProjectHeader;
import com.ti.specteam.vyper.build.util.ReadOnlyRepository;

public interface ProjectHeaderRepository
                extends ReadOnlyRepository<ProjectHeader, String>, JpaSpecificationExecutor<ProjectHeader> {
    ProjectHeader findById(String id);
    ProjectHeader findByProjNumber(String number);
}
