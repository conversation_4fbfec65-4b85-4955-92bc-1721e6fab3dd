package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.atss.attribute.AttributeService;
import com.ti.specteam.vyper.audit.AuditActivity;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.model.System;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.security.user.UserUtilsService;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.VerifierService;
import com.ti.specteam.vyper.verifier.vscn.VscnDieVerifier;
import com.ti.specteam.vyper.verifier.vscn.VscnMaterialDieVerifier;
import com.ti.specteam.vyper.verifier.vscn.VscnPgsVerifier;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeVscnComponentAction {

    private final VyperService vyperService;
    private final VscnService vscnService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final AttributeService attributeService;
    private final UserUtilsService userUtilsService;
    private final SecurityService securityService;
    private final VerifierService verifierService;
    private final VscnPgsVerifier vscnPgsVerifier;
    private final VscnDieVerifier vscnDieVerifier;
    private final VscnMaterialDieVerifier vscnMaterialDieVerifier;

    public Vscn execute(ChangeVscnComponentForm changeVscnComponentForm) {
        log.debug("execute(changeVscnComponentForm:{})", changeVscnComponentForm);

        Vscn vscn = vscnService.fetchVscn(changeVscnComponentForm.getVscnNumber());
        Vyper vyper = vyperService.fetchVyper(vscn.getVyperNumber());
        String facility = vscn.getFacility().getPdbFacility();

        userUtilsService.validateUserByFacility(facility);
        validateService.checkOpen(vyper);
        validateService.checkEditable(vscn);

        changeComponent(vscn, changeVscnComponentForm);

        vscnPgsVerifier.verify(vyper,vscn);
        vscnDieVerifier.verify(vyper,vscn);
        vscnMaterialDieVerifier.verify(vyper,vscn);
        auditService.createVscn(
                vscn.getVyperNumber(),
                vscn.getVscnNumber(),
                AuditActivity.VSCN_CHANGE_COMPONENT,
                "changed component " + changeVscnComponentForm.getName() + " to " + changeVscnComponentForm.display());

        return vscnService.saveVscn(vscn);
    }

    public void changeComponent(Vscn vscn, ChangeVscnComponentForm changeVscnComponentForm){
        // replace the component value
        for (Component component : vscn.getComponents()) {
            if (StringUtils.equalsIgnoreCase(component.getName(), changeVscnComponentForm.getName())) {
                ComponentInstance componentInstance = component.getInstances().get(0);
                if (componentInstance != null) {
                    List<ComponentPriority> componentPriorityList = componentInstance.getPriorities();
                    componentPriorityList.clear();
                    for (SelectionItem selectionItem : changeVscnComponentForm.getItems()) {
                        ComponentPriority componentPriority = new ComponentPriority();
                        componentPriority.setEngineering(selectionItem.getEngineering());
                        componentPriority.setSource(selectionItem.getSource());
                        List<Map<String, Object>> atssAttributes = attributeService.findByName(vscn.getFacility().getPdbFacility(), component.getName(), selectionItem.getValue());
                        atssAttributes.forEach(item -> componentPriority.getObject().put((String) item.get("key"), item.get("value")));
                        componentPriority.getObject().put("name", selectionItem.getValue());
                        componentPriority.getSource().setSystem(new System());
                        componentPriority.getSource().appointUser(securityService.user());
                        componentPriorityList.add(componentPriority);
                    }
                }
            }
        }

        vscn.getVerifiers().removeIf(verifier -> StringUtils.equalsIgnoreCase(verifier.getName(), changeVscnComponentForm.getName()));
        verifierService.initializeVerifiersForDie(vscn);
    }
}
