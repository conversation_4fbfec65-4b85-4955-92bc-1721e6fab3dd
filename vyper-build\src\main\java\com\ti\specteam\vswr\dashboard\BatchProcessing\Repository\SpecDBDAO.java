package com.ti.specteam.vswr.dashboard.BatchProcessing.Repository;

import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 */
@MyBatisSpecRepository
public interface SpecDBDAO {
    
    int validateDeviceName(@Param("deviceName") String deviceName, @Param("atSite") String atSite);
    
    int validateMatlSpecDevice(@Param("deviceName") String deviceName, @Param("specDevice") String specDevice);
    
}
