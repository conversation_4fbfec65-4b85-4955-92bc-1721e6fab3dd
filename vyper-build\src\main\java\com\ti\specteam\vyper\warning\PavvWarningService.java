package com.ti.specteam.vyper.warning;

import com.ti.specteam.vyper.armarc.ArmArcLookupService;
import com.ti.specteam.vyper.armarc.Armarc;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.ComponentPriority;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.componentlistener.context.ChangeComponentListenerContext;
import com.ti.specteam.vyper.componentlistener.context.WarningRefreshContext;
import com.ti.specteam.vyper.componentlistener.listener.ChangeComponentListener;
import com.ti.specteam.vyper.componentlistener.listener.WarningRefreshListener;
import com.ti.specteam.vyper.componentlistener.processor.LeadframeProcessor;
import com.ti.specteam.vyper.componentlistener.processor.MoldCompoundProcessor;
import com.ti.specteam.vyper.componentlistener.processor.MountCompoundProcessor;
import com.ti.specteam.vyper.componentlistener.processor.WireProcessor;
import com.ti.specteam.vyper.componentlistener.processor.utils.MatchContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * Listen for changes to the PAVV components, and update any needed warnings
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PavvWarningService implements ChangeComponentListener, WarningRefreshListener {

    private final ArmArcLookupService armArcLookupService;
    private final LeadframeProcessor leadframeProcessor;
    private final MoldCompoundProcessor moldCompoundProcessor;
    private final MountCompoundProcessor mountCompoundProcessor;
    private final WireProcessor wireProcessor;

    private final List<String> componentNames = List.of(
            "MB Diagram", "Leadframe", "Mount Compound", "Mold Compound", "Flux", "Wire");

    /**
     * a specific component has changed. if it's a pavv component, then update warnings
     *
     * @param context {@link ChangeComponentListenerContext}
     */
    @Override
    public void onChangeComponent(ChangeComponentListenerContext context) {
        log.debug("onChangeComponent()");

        Vyper vyper = context.getVyper();
        Build build = context.getBuild();

        // only run on our components
        if (!componentNames.contains(context.getComponentName())) {
            return;
        }

        updateWarnings(vyper, build);
    }

    /**
     * all warnings are being refreshed, so update the pavv warnings.
     *
     * @param context {@link WarningRefreshContext context}
     */
    @Override
    public void onRefreshWarnings(WarningRefreshContext context) {
        log.debug("onRefreshWarnings()");

        Vyper vyper = context.getVyper();
        Build build = context.getBuild();

        updateWarnings(vyper, build);
    }

    public void updateWarnings(Vyper vyper, Build build) {

        // find the mb diagram component's priority
        Component component = build.findComponentByName("MB Diagram");
        if (null == component) {
            return;
        }

        component.clearWarnings();

        ComponentPriority mbPriority = component.getPriority(0, 0);
        if(null == mbPriority) {
            return;
        }

        // get the armarc object
        Armarc armarc = armArcLookupService.findArmarc(build);
        if(armarc == null) {
            return;
        }

        checkMountCompound(mbPriority, build.getComponents(), armarc.getMountCompound());
        checkMoldCompound(mbPriority, build.getComponents(), armarc.getMoldCompound());
        checkLeadframe(mbPriority, build.getComponents(), armarc.getLeadframe());
        checkWire(mbPriority, build.getComponents(), armarc.getWireDiameter(), armarc.getWireType());
    }

    private void checkMountCompound(ComponentPriority mbPriority, List<Component> components, String value) {
        Assert.notNull(mbPriority, "mbPriority cannot be null");
        Assert.notNull(components, "components cannot be null");

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Mount Compound", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (!mountCompoundProcessor.matchValue(priority, value, context)) {
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            mbPriority.addWarning("Mount Compound: ARMARC = " + value + ", Build = " + priority.getValue() + pn);
        }

    }

    private void checkMoldCompound(ComponentPriority mbPriority, List<Component> components, String value) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Mold Compound", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (!moldCompoundProcessor.matchValue(priority, value, context)) {
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            mbPriority.addWarning("Mold Compound: ARMARC = " + value + ", Build = " + priority.getValue() + pn);
        }

    }

    private void checkLeadframe(ComponentPriority mbPriority, List<Component> components, String value) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Leadframe", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (!leadframeProcessor.matchValue(priority, value, context)) {
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            mbPriority.addWarning("Leadframe: ARMARC = " + value + ", Build = " + priority.getValue() + pn);
        }

    }

    private void checkWire(ComponentPriority mbPriority, List<Component> components, String armarcDiameter, String armarcType) {

        ComponentPriority priority = components.stream()
                .filter(component -> StringUtils.equalsIgnoreCase("Wire", component.getName()))
                .flatMap(component -> component.getInstances().stream())
                .flatMap(componentInstance -> componentInstance.getPriorities().stream())
                .findFirst()
                .orElse(null);

        if (priority == null) {
            return;
        }

        MatchContext context = new MatchContext();
        if (!wireProcessor.matchValue(priority, armarcDiameter, armarcType, context)) {
            String armarcWire = StringUtils.trim(armarcType + " " + armarcDiameter);
            String pn = priority.getPartNumber() == null ? "" : " (" + priority.getPartNumber() + ")";
            mbPriority.addWarning("Wire: ARMARC = " + armarcWire + ", Build = " + priority.getValue() + pn);
        }

    }

}
