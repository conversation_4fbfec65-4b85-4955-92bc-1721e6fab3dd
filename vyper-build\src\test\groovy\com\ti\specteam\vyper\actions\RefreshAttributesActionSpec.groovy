package com.ti.specteam.vyper.actions

import com.ti.specteam.vyper.build.TravelerRefreshService
import com.ti.specteam.vyper.build.dataloader.AtssLoader
import com.ti.specteam.vyper.build.dataloader.PgsLoader
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.build.vyper.BuildService
import com.ti.specteam.vyper.build.vyper.VyperService
import com.ti.specteam.vyper.topsymbol.TopSymbolLoader
import com.ti.specteam.vyper.validate.ValidateService
import spock.lang.Specification

class RefreshAttributesActionSpec extends Specification {
    VyperService vyperService = Mock(VyperService)
    BuildService buildService = Mock(BuildService)
    ValidateService validateService = Mock(ValidateService)
    PgsLoader pgsLoader = Mock(PgsLoader)
    TopSymbolLoader topSymbolLoader = Mock(TopSymbolLoader)
    AtssLoader atssLoader = Mock(AtssLoader)
    TravelerRefreshService travelerRefreshService = Mock(TravelerRefreshService)
    ValidateOperationLoader validateOperationLoader = Mock(ValidateOperationLoader)

    RefreshAttributesAction action = new RefreshAttributesAction(
            vyperService,
            buildService,
            validateService,
            pgsLoader,
            topSymbolLoader,
            atssLoader,
            travelerRefreshService,
            validateOperationLoader
    )

    RefreshAttributesForm form1 = new RefreshAttributesForm()
    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _
    }

    def "execute clears existing warning if no mismatches"() {

        when:
        def ret = action.execute(form1)

        then:
        1 * vyperService.fetchVyper(form1) >> vyper1
        1 * buildService.fetchBuild(form1) >> build1
        1 * validateService.checkEditable(vyper1, build1)
        1 * pgsLoader.refreshAllDieAttributes(build1)
        1 * topSymbolLoader.load(vyper1, build1)
        1 * atssLoader.loadAttributes(vyper1, build1)
        1 * travelerRefreshService.load(vyper1, build1)
        1 * validateOperationLoader.load(vyper1, build1)

        1 * buildService.saveBuild(build1) >> build1

        and:
        ret == build1
    }

}
