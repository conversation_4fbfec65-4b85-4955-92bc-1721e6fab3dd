package com.ti.specteam.vyper.componentlistener.processor

import com.ti.specteam.vyper.atss.traveler.Traveler
import com.ti.specteam.vyper.build.model.*
import com.ti.specteam.vyper.componentlistener.context.AtssLoaderListenerContext
import com.ti.specteam.vyper.componentlistener.context.PgsListenerContext
import com.ti.specteam.vyper.componentlistener.context.PgsRefreshListenerContext
import com.ti.specteam.vyper.pgs.PgsParserService
import spock.lang.Specification

class CustProcessorSpec extends Specification {

    PgsParserService pgsParserService = Mock(PgsParserService)
    private final List<String> custNames = List.of("CUST1", "CUST2")

    CustProcessor processor = new CustProcessor(pgsParserService, custNames)

    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _
    }

    def "onPgsLoader - does nothing if no pgs attributes"() {

        def bomName1 = new BomName(name: "BOM1")

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.itemsAttrsByType(_, "Device") >> []

        and:
        0 * _
    }

    def "onPgsLoader - sets CUSTx values from PGS data"() {

        def bomName1 = new BomName(name: "BOM1")

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.itemsAttrsByType(_, "Device") >> [
                [
                        TopsideMarkingCust1: "VALUE1",
                        TopsideMarkingCust2: "VALUE2"
                ]
        ]

        and:
        build1.getComponentKeyValue("CUST1", 0, 0, "name") == "VALUE1"
        build1.getComponentKeyValue("CUST2", 0, 0, "name") == "VALUE2"
    }

    def "onAtssLoader - traveler component not found - marked as processed"() {

        def traveler = new Traveler()
        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("CUST1")
        context.componentsProcessed.contains("CUST2")
    }

    def "onAtssLoader - component not found - marked as processed"() {

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        to.create("CUST1", "VALUE1")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("CUST1")
        context.componentsProcessed.contains("CUST2")
    }

    def "onAtssLoader - sets the build component priority value"() {
        def c1 = build1.findOrCreateComponent("CUST1", Source.VYPER)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "OLD_VALUE1", Engineering.N)
        p1.put "Marking", "VALUE1"

        def c2 = build1.findOrCreateComponent("CUST2", Source.VYPER)
        def i2 = c2.createInstance()
        def p2 = i2.addPriority("name", "OLD_VALUE2", Engineering.N)
        p2.put "Marking", "VALUE2"

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        to.create("CUST1", "VALUE1")
        to.create("CUST2", "VALUE2")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("CUST1")
        context.componentsProcessed.contains("CUST2")

        and:
        build1.getComponentKeyValue("CUST1", 0, 0, "name") == "VALUE1"
        build1.getComponentKeyValue("CUST1", 0, 0, "Marking") == "VALUE1"
        build1.getComponentKeyValue("CUST2", 0, 0, "name") == "VALUE2"
        build1.getComponentKeyValue("CUST2", 0, 0, "Marking") == "VALUE2"
    }

    def "onAtssLoader - change pgs source to atss"() {
        def c1 = build1.findOrCreateComponent("CUST1", Source.PGS)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "VALUE1", Engineering.N)
        p1.put "Marking", "VALUE1"

        def c2 = build1.findOrCreateComponent("CUST2", Source.PGS)
        def i2 = c2.createInstance()
        def p2 = i2.addPriority("name", "VALUE2", Engineering.N)
        p2.put "Marking", "VALUE2"

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        to.create("CUST1", "VALUE1")
        to.create("CUST2", "VALUE2")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        build1.findComponentByName("CUST1").getPriority(0, 0).source.system.name == SystemName.ATSS
        build1.findComponentByName("CUST2").getPriority(0, 0).source.system.name == SystemName.ATSS
    }

}
