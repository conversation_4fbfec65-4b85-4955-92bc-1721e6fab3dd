package com.ti.specteam.atssmassupload.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ti.specteam.atssmassupload.entity.ProjectDeviceLog;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceLogRepository;
import com.ti.specteam.vyper.vscn.model.AtssFieldError;
import com.ti.specteam.vyper.vscn.model.AtssScn;
import com.ti.specteam.vyper.vscn.model.KafkaAtssTopicResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j

public class ProjectDeviceLogService {

    private final ObjectMapper objectMapper;
    private final ProjectDeviceLogRepository projectDeviceLogRepository;

    public void createLog(String deviceId, KafkaAtssTopicResponse objToStore){
        ProjectDeviceLog projectDeviceLog = new ProjectDeviceLog();
        projectDeviceLog.setDeviceId(deviceId);
        projectDeviceLog.setLogDate(new Date());
        AtssScn atssScn = new AtssScn();
        atssScn.setScnId(objToStore.getScnId());
        atssScn.setStatus(objToStore.getMessage());
        atssScn.setAtssFieldErrors(objToStore.getValidations());
        try {
            projectDeviceLog.setLogMessage(objectMapper.copy().writeValueAsString(atssScn));
        } catch (JsonProcessingException e) {
            log.error("error reading the response {}",atssScn);
            projectDeviceLog.setLogMessage("Error reading the response");
        }
        projectDeviceLogRepository.save(projectDeviceLog);
    }
    public void createLog(String deviceId, String objToStore){
        ProjectDeviceLog projectDeviceLog = new ProjectDeviceLog();
        projectDeviceLog.setDeviceId(deviceId);
        projectDeviceLog.setLogDate(new Date());
        projectDeviceLog.setLogMessage(objToStore);
        projectDeviceLogRepository.save(projectDeviceLog);
    }

    public void createLog(String deviceId, Map<String,Object> objToStore){
        ProjectDeviceLog projectDeviceLog = new ProjectDeviceLog();
        projectDeviceLog.setDeviceId(deviceId);
        projectDeviceLog.setLogDate(new Date());
        AtssScn atssScn = new AtssScn();
        if ( (Boolean) objToStore.getOrDefault("success", Boolean.FALSE) ){
            atssScn.setScnId((Long) (objToStore.getOrDefault("scnId",null)));
        }else{
            try{
                List<AtssFieldError> responseErrors =
                        Collections.unmodifiableList((List<AtssFieldError>) objToStore.getOrDefault("body", List.of()));
                atssScn.setAtssFieldErrors(responseErrors);
            }catch (Exception err){
                log.error("Error reading the response");
                projectDeviceLog.setLogMessage("Error reading the response");
            }
        }
        try {
            projectDeviceLog.setLogMessage(objectMapper.copy().writeValueAsString(atssScn));
        } catch (JsonProcessingException e) {
            projectDeviceLog.setLogMessage("Error reading the response");
        }
        projectDeviceLogRepository.save(projectDeviceLog);
    }

    public List<AtssScn> findProjectDeviceLogByDeviceId(String deviceId){
        List<ProjectDeviceLog> allLogs = projectDeviceLogRepository.findAllByDeviceIdOrderByLogDateDesc(deviceId);
        return allLogs.stream()
                .map( logEntry -> {
                    AtssScn atssScn = new AtssScn();
                    try {
                        atssScn = objectMapper.copy().readValue(logEntry.getLogMessage(), AtssScn.class);
                    } catch (JsonProcessingException e) {
                        log.debug(e.getMessage(), e);
                        atssScn.setStatus("Error reading log");
                    }
                    atssScn.setLogDate(logEntry.getLogDate());
                    return atssScn;
                })
                .collect(Collectors.toList());
    }

}
