package com.ti.specteam.vyper.apitraveler.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ApiTraveler {

    private boolean dryRun;
    private String submitter;
    private String vyperBuildNumber;
    private String vyperScnNumber;
    private String scnNumber;
    private boolean flagSubmitForSignOff = false;
    private boolean flagMultiBuildDevice = false;
    private final Map<String, Object> facility = new HashMap<>();
    private final Map<String, Object> material = new HashMap<>();
    private ApiTravelerHeader header = new ApiTravelerHeader();
    private final List<ApiTravelerSubflow> subFlows = new ArrayList<>();

    public ApiTraveler(ApiTraveler other) {
        this.dryRun = other.dryRun;
        this.submitter = other.submitter;
        this.vyperBuildNumber = other.vyperBuildNumber;
        this.vyperScnNumber = other.vyperScnNumber;
        this.scnNumber = other.scnNumber;
        this.flagMultiBuildDevice = other.isFlagMultiBuildDevice();
        this.flagSubmitForSignOff = other.isFlagSubmitForSignOff();
        this.facility.putAll(other.facility);
        this.material.putAll(other.material);
        this.header = new ApiTravelerHeader(other.header);
        this.subFlows.addAll(other.subFlows);
    }

    public ApiTravelerSubflow createSubflow(String type) {
        ApiTravelerSubflow subFlow = new ApiTravelerSubflow(type);
        subFlows.add(subFlow);
        return subFlow;
    }

    public ApiTravelerSubflow findSubflow(String type) {
        return subFlows.stream()
                .filter(subFlow -> StringUtils.equalsIgnoreCase(type, subFlow.getType()))
                .findFirst()
                .orElse(null);
    }

    public ApiTravelerOperation findOperation(String sfType, String oName) {
        return subFlows.stream()
                .filter(subFlow -> StringUtils.equalsIgnoreCase(sfType, subFlow.getType()))
                .flatMap(ApiTravelerSubflow::stream)
                .filter(apiTravelerOperation -> StringUtils.equalsIgnoreCase(oName, apiTravelerOperation.getName()))
                .findFirst()
                .orElse(null);
    }

    public ApiTravelerOperation findOrCreateOperation(String sfType, String oName) {
        ApiTravelerSubflow requiredSubFlow = findSubflow(sfType);
        if (requiredSubFlow == null) {
            requiredSubFlow = createSubflow(sfType);
        }

        ApiTravelerOperation requestedOperation =
                requiredSubFlow.getOperations().stream()
                        .filter(Objects::nonNull)
                        .filter(apiTravelerOperation -> StringUtils.equalsIgnoreCase(oName, apiTravelerOperation.getName()))
                        .findFirst()
                        .orElse(null);

        if (requestedOperation == null) {
            requestedOperation = new ApiTravelerOperation(oName);
            requiredSubFlow.getOperations().add(requestedOperation);
        }
        return requestedOperation;
    }

    public void addComponentToOperation(ApiTravelerOperation operation, ApiTravelerComponent component) {
        operation.getComponents().removeIf(comp -> comp.getName().equalsIgnoreCase(component.getName()));
        operation.getComponents().add(component);
    }
}
