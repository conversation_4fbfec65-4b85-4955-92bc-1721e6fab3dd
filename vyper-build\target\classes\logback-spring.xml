<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!--    <logger name="org.springframework" level="info"/>-->
    <!--    <logger name="com.ti.sfw.gateway" level="info"/>-->
    <!--    <logger name="org.mybatis" level="info"/>-->
    <!--    <logger name="org.hibernate" level="info"/>-->
    <!--    <logger name="org.hibernate.SQL" level="info"/>-->
    <!--    <logger name="org.hibernate.type.descriptor.sql" level="info"/>-->

    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>