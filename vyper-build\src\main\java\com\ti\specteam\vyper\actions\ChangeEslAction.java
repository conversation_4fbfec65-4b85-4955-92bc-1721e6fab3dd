package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_ESL;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeEslAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final BomTemplateLoader bomTemplateLoader;
    private final FlowLoader flowLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final PackLoader packLoader;

    public Build execute(ChangeEslForm changeEslForm)  {
        log.debug("execute(changeEslForm:{})", changeEslForm);

        Vyper vyper = vyperService.fetchVyper(changeEslForm);
        Build build = buildService.fetchBuild(changeEslForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getEsl().getObject().put("value", changeEslForm.getValue());
        build.getEsl().getSource().appointUser(securityService.user());

        bomTemplateLoader.load(vyper, build);
        packLoader.load(vyper, build);
        flowLoader.load(vyper, build);
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_ESL,
                "changed esl to: " + changeEslForm.getValue()
        );

        return buildService.saveBuild(build);
    }

}
