package com.ti.specteam.vyper.build.dataloader


import com.ti.specteam.vyper.bomtemplate.BomTemplateService
import com.ti.specteam.vyper.bomtemplate.merge.MergeContext
import com.ti.specteam.vyper.bomtemplate.pkgdb.FlowTemplateDetail
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.SystemName
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.template.TemplateType
import spock.lang.Specification

class BomTemplateLoaderSpec extends Specification {

    BomTemplateService bomTemplateService = Mock(BomTemplateService)

    BomTemplateLoader loader = new BomTemplateLoader(bomTemplateService)

    def vyper1 = new Vyper()
    def build1 = new Build()

    def ftd1 = new FlowTemplateDetail()
    def ftd2 = new FlowTemplateDetail()
    def ftd3 = new FlowTemplateDetail()

    def setup() {
        0 * _

        ftd1.templateType = "GLOBAL"
        ftd1.templateName = "NAME_GLOBAL"
        ftd1.templateRevision = "1"

        ftd2.templateType = "BOM"
        ftd2.templateName = "NAME_BOM"
        ftd2.templateRevision = "2"

        ftd3.templateType = "AT"
        ftd3.templateName = "NAME_AT"
        ftd3.templateRevision = "3"
    }

    def "load does nothing, and returns empty context if template type is ATSS"() {

        build1.templateSource.templateType = TemplateType.ATSS

        when:
        def ret = loader.load(vyper1, build1)

        then:
        ret.items.isEmpty()
        ret.flowTemplateDetails.empty
        ret.mergeTemplates.empty
        ret.errors.empty
        ret.ruleContext.isEmpty()
    }

    def "load does nothing, and returns empty context if template type is VYPER"() {

        build1.templateSource.templateType = TemplateType.VYPER

        when:
        def ret = loader.load(vyper1, build1)

        then:
        ret.items.isEmpty()
        ret.flowTemplateDetails.empty
        ret.mergeTemplates.empty
        ret.errors.empty
        ret.ruleContext.isEmpty()
    }

    def "load runs when template type is SIMILAR_PKGNICHE"() {

        build1.templateSource.templateType = TemplateType.SIMILAR_PKGNICHE

        def mc = new MergeContext()
        mc.flowTemplateDetails << ftd1 << ftd2 << ftd3

        when:
        def ret = loader.load(vyper1, build1)

        then:
        1 * bomTemplateService.initMergeContext(vyper1, build1) >> mc
        1 * bomTemplateService.addTemplatesToOperations(mc, build1.getBomTemplateObject())

        and:
        ret.items."Template Name" == "NAME_GLOBAL#1 (GLOBAL), NAME_BOM#2 (BOM), NAME_AT#3 (AT)"
        ret.flowTemplateDetails == [ftd1, ftd2, ftd3]
        ret.mergeTemplates.empty
        ret.errors.empty
        ret.ruleContext.isEmpty()

        and:
        build1.bomTemplateObject.version == 1
        build1.bomTemplateObject.context."Template Name" == "NAME_GLOBAL#1 (GLOBAL), NAME_BOM#2 (BOM), NAME_AT#3 (AT)"
    }

    def "load runs when template type is DEVICE_PKGNICHE"() {

        build1.templateSource.templateType = TemplateType.DEVICE_PKGNICHE

        def mc = new MergeContext()
        mc.flowTemplateDetails << ftd1 << ftd2 << ftd3

        when:
        def ret = loader.load(vyper1, build1)

        then:
        1 * bomTemplateService.initMergeContext(vyper1, build1) >> mc
        1 * bomTemplateService.addTemplatesToOperations(mc, build1.getBomTemplateObject())

        and:
        ret.items."Template Name" == "NAME_GLOBAL#1 (GLOBAL), NAME_BOM#2 (BOM), NAME_AT#3 (AT)"
        ret.flowTemplateDetails == [ftd1, ftd2, ftd3]
        ret.mergeTemplates.empty
        ret.errors.empty
        ret.ruleContext.isEmpty()

        and:
        build1.bomTemplateObject.version == 1
        build1.bomTemplateObject.context."Template Name" == "NAME_GLOBAL#1 (GLOBAL), NAME_BOM#2 (BOM), NAME_AT#3 (AT)"
    }

    def "the development flag is not set if all flows are active"() {

        build1.templateSource.templateType = TemplateType.DEVICE_PKGNICHE

        def mc = new MergeContext()
        mc.flowTemplateDetails << ftd1 << ftd2 << ftd3

        ftd1.preferenceCode = "A"
        ftd2.preferenceCode = "A"
        ftd3.preferenceCode = "A"

        when:
        loader.load(vyper1, build1)

        then:
        1 * bomTemplateService.initMergeContext(vyper1, build1) >> mc
        1 * bomTemplateService.addTemplatesToOperations(mc, build1.getBomTemplateObject())

        and:
        !build1.bomTemplateObject.isDevelopment
    }

    def "the development flag is set if any flows are experimental"() {

        build1.templateSource.templateType = TemplateType.DEVICE_PKGNICHE

        def mc = new MergeContext()
        mc.flowTemplateDetails << ftd1 << ftd2 << ftd3

        ftd1.preferenceCode = "A"
        ftd2.preferenceCode = "E"
        ftd3.preferenceCode = "A"

        when:
        loader.load(vyper1, build1)

        then:
        1 * bomTemplateService.initMergeContext(vyper1, build1) >> mc
        1 * bomTemplateService.addTemplatesToOperations(mc, build1.getBomTemplateObject())

        and:
        build1.bomTemplateObject.isDevelopment
    }

    def "context errors are copied to the bom template object"() {

        build1.templateSource.templateType = TemplateType.DEVICE_PKGNICHE

        def mc = new MergeContext()
        mc.errors << "ERROR1"

        when:
        loader.load(vyper1, build1)

        then:
        1 * bomTemplateService.initMergeContext(vyper1, build1) >> mc
        1 * bomTemplateService.addTemplatesToOperations(mc, build1.getBomTemplateObject())

        and:
        build1.bomTemplateObject.errors == ["ERROR1"]
    }

    def "the system is set to vyper"() {

        build1.templateSource.templateType = TemplateType.DEVICE_PKGNICHE

        def mc = new MergeContext()

        when:
        loader.load(vyper1, build1)

        then:
        1 * bomTemplateService.initMergeContext(vyper1, build1) >> mc
        1 * bomTemplateService.addTemplatesToOperations(mc, build1.getBomTemplateObject())

        and:
        build1.bomTemplate.source.system.name == SystemName.VYPER
    }

}
