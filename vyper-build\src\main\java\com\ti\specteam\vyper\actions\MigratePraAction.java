package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.entity.pra.PraEntityRepository;
import com.ti.specteam.vyper.entity.pra.PraEntityService;
import com.ti.specteam.vyper.pra.model.Pra;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MigratePraAction implements CommandLineRunner {

    private final PraEntityRepository praEntityRepository;
    private final PraEntityService praEntityService;
    private final PraService praService;

    @Override
    public void run(String... args) throws Exception {
        migrate01();
    }

    // older versions of pra have verifiers as an object. change to a list of verifiers.
    // added the version column
    private void migrate01() {
        log.debug("migrate01()");

        praEntityRepository.findAllByVersionIsNull().forEach(pe -> {
            log.debug(pe.getPraNumber());

            // convert verifiers from object to array

            String json1 = pe.getJson();
            String json2 = json1.replace("\"verifiers\":{}", "\"verifiers\":[]");
            pe.setJson(json2);

            // set the version number
            Pra pra = praEntityService.fromJson(pe.getJson());
            pra.setVersion(1);
            praService.savePra(pra);
        });

    }

}
