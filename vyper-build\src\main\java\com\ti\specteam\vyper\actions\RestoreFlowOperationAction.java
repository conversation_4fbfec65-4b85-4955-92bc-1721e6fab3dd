package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.Required;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.RequiredComponentLoader;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.RESTORE_FLOW_OPERATION;

@Service
@Slf4j
@RequiredArgsConstructor
public class RestoreFlowOperationAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;

    public Build execute(RestoreFlowOperationForm restoreFlowOperationForm) {
        log.debug("execute(restoreFlowOperationForm:{})", restoreFlowOperationForm);

        Vyper vyper = vyperService.fetchVyper(restoreFlowOperationForm);
        Build build = buildService.fetchBuild(restoreFlowOperationForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAt(vyper, build);
        validateService.checkEditable(vyper, build);

        // get the flow operation
        FlowOperation flowOperation = build.getFlow().getObject().getOperations().stream()
                .filter(fo -> StringUtils.equalsIgnoreCase(fo.getName(), restoreFlowOperationForm.getName()))
                .findFirst()
                .orElse(null);

        if (flowOperation != null) {

            // make sure the operation is required, and buildtype is experimental
            if (flowOperation.getRequired() != Required.REQUIRED) {
                throw new VyperException("Cannot restore the  operation. The operation is not required.");
            }

            if (!StringUtils.equalsIgnoreCase(build.getBuildtype(), "Experimental")) {
                throw new VyperException("Cannot restore the  operation. The buildtype is not Experimental.");
            }

            // if the operation is required, then we'll do an engineering delete
            flowOperation.setEngineeringDeleted(false);
        }

        // refresh
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                RESTORE_FLOW_OPERATION,
                "restored flow operation: " + restoreFlowOperationForm.getName()
        );

        return buildService.saveBuild(build);
    }

}
