package com.ti.specteam.vyper.actions.vscn;

import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
@Jacksonized
public class ChangeVscnMaterialForm extends VscnNumberForm {

    @NotNull
    @Size(min = 1)
    private String material;
}
