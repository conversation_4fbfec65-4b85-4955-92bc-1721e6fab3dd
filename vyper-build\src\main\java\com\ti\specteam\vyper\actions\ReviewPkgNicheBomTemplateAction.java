package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.BomTemplateRequestEmailProperties;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.email.EmailContext;
import com.ti.specteam.vyper.email.EmailService;
import com.ti.specteam.vyper.security.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.mail.MessagingException;
import java.io.IOException;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReviewPkgNicheBomTemplateAction {
    private final SecurityService securityService;
    private final EmailService emailService;
    private final BomTemplateRequestEmailProperties bomTemplateRequestEmailProperties;

    public Build execute(ReviewPkgNicheBomTemplateForm reviewPkgNicheBomTemplateForm) throws IOException, MessagingException {
        log.debug("execute(reviewBomTemplateForm:{})", reviewPkgNicheBomTemplateForm);

        EmailContext context = new EmailContext();
        context.setTemplateName("review bom template");
        com.ti.specteam.vyper.security.user.User user = securityService.user();

        // prepare email data
        context.getBinding().put("owners", user.getUsername());
        context.getBinding().put("material", reviewPkgNicheBomTemplateForm.getMaterial());
        context.getBinding().put("facility", reviewPkgNicheBomTemplateForm.getFacility());
        context.getBinding().put("buildnumber", "Not applicable");
        context.getBinding().put("pin", reviewPkgNicheBomTemplateForm.getPin());
        context.getBinding().put("pkg", reviewPkgNicheBomTemplateForm.getPkg());
        context.getBinding().put("pkggroup", reviewPkgNicheBomTemplateForm.getPkgGroup());
        if(reviewPkgNicheBomTemplateForm.getPackageNiche() != null && !reviewPkgNicheBomTemplateForm.getPackageNiche().equals("")){
            context.getBinding().put("pkgniche", reviewPkgNicheBomTemplateForm.getPackageNiche());
        }else{
            context.getBinding().put("pkgniche", reviewPkgNicheBomTemplateForm.getPkgGroup());
        }
        context.getBinding().put("templates", "Not applicable");
        context.getBinding().put("from", user.getUsername());
        context.getBinding().put("mergecontext", "Not Applicable");

        // set subject, addresses
        context.setSubject(bomTemplateRequestEmailProperties.getSubject());
        context.getTos().add(bomTemplateRequestEmailProperties.getTo());
        context.getCcs().add(securityService.userid() + "@ti.com");

        emailService.send(context);
        return new Build();
    }

}
