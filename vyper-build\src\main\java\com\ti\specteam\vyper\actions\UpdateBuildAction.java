package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperCopyBuildAction;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.UPDATE_BUILD_ACTION;

@Service
@Slf4j
@RequiredArgsConstructor
public class UpdateBuildAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final AuditService auditService;
    private final ChangeMaterialAction changeMaterialAction;
    private final ChangeFacilityAction changeFacilityAction;
    private final ChangeDescriptionAction changeDescriptionAction;
    private final ChangeBuildtypeAction changeBuildtypeAction;
    private final VyperCopyBuildAction vyperCopyBuildAction;

    public Build execute(UpdateBuildForm updateBuildForm) {
        log.debug("execute(updateBuildForm:{})", updateBuildForm);

        Vyper vyper = vyperService.fetchVyper(updateBuildForm);
        Build build = buildService.fetchBuild(updateBuildForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        changeMaterialAction.execute(vyper, build, updateBuildForm.getMaterial());
        changeFacilityAction.execute(vyper, build, updateBuildForm.getFacility());
        changeDescriptionAction.execute(vyper, build, updateBuildForm.getDescription());
        changeBuildtypeAction.execute(vyper, build, updateBuildForm.getBuildType());

        // update the multi-build
        build.getMultiBuild().setMultiBuild(updateBuildForm.isMultiBuild());
        build.getMultiBuild().setSpecDevice(updateBuildForm.getSpecDevice());

        // copy from build
        if (null != updateBuildForm.getCopyBuildNumber()) {
            vyperCopyBuildAction.execute(vyper, build, updateBuildForm.getCopyBuildNumber());
        }

        String detail = "Changed material to " + updateBuildForm.getMaterial() + ". ";
        detail += "Changed facility to " + updateBuildForm.getFacility() + ". ";
        detail += "Changed description to " + updateBuildForm.getDescription() + ". ";
        detail += "Changed build type to " + updateBuildForm.getBuildType() + ". ";
        detail += "Changed multi-build to " + updateBuildForm.isMultiBuild() + ". ";
        detail += "Changed specDevice to " + updateBuildForm.getSpecDevice() + ". ";

        if (null != updateBuildForm.getCopyBuildNumber()) {
            detail += "copied from build  " + updateBuildForm.getCopyBuildNumber() + ". ";
        }

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                UPDATE_BUILD_ACTION,
                detail
        );

        return buildService.saveBuild(build);
    }
}
