package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_PKG_NICHE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangePkgNicheAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final PgsLoader pgsLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final BomTemplateLoader bomTemplateLoader;
    private final FlowLoader flowLoader;
    private final AtssLoader atssLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;
    private final PackLoader packLoader;

    public Build execute(ChangePkgNicheForm changePkgNicheForm) {
        log.debug("execute(changePkgNicheForm:{})", changePkgNicheForm);

        Vyper vyper = vyperService.fetchVyper(changePkgNicheForm);
        Build build = buildService.fetchBuild(changePkgNicheForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        build.getPackageNiche().setName(changePkgNicheForm.getPkgNiche());
        build.getPackageNiche().getSource().appointUser(securityService.user());

        requiredComponentLoader.load(vyper, build);
        pgsLoader.load(vyper, build);
        bomTemplateLoader.load(vyper, build);
        atssLoader.loadAttributes(vyper, build);
        packLoader.load(vyper, build);
        flowLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_PKG_NICHE,
                "changed pkg niche to: " + changePkgNicheForm.getPkgNiche()
        );

        return buildService.saveBuild(build);
    }

}
