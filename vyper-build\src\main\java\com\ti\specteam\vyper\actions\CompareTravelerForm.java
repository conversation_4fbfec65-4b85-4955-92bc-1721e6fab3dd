package com.ti.specteam.vyper.actions;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ti.specteam.vyper.build.model.Build;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@ToString
public class CompareTravelerForm {

    @NotNull
    private String buildNumber1;

    @NotNull
    private String buildNumber2;

    private String date1;

    private String date2;

    public String getVyperNumber1() {
        return (buildNumber1 == null) ? null : Build.convertBuildNumbertoVyperNumber(buildNumber1);
    }

    public String getVyperNumber2() {
        return (buildNumber2 == null) ? null : Build.convertBuildNumbertoVyperNumber(buildNumber2);
    }

}
