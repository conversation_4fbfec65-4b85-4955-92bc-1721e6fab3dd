package com.ti.specteam.atssmassupload.service;

import com.ti.specteam.atssmassupload.domain.ProjectDevice;
import com.ti.specteam.atssmassupload.domain.ProjectDeviceForm;
import com.ti.specteam.atssmassupload.domain.ProjectHeader;
import com.ti.specteam.atssmassupload.domain.SpecChange;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadException;
import com.ti.specteam.atssmassupload.exception.AtssMassUploadProjectNotFound;
import com.ti.specteam.atssmassupload.repository.ProjectDeviceRepository;
import com.ti.specteam.atssmassupload.repository.ProjectHeaderRepository;
import com.ti.specteam.atssmassupload.repository.SpecChangeRuleRepository;
import com.ti.specteam.vyper.actions.vscn.*;
import com.ti.specteam.vyper.atss.traveler.AtssRepository;
import com.ti.specteam.vyper.atss.traveler.TravelerService;
import com.ti.specteam.vyper.build.ComponentService;
import com.ti.specteam.vyper.build.componentmap.util.FilterMethod;
import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.ComponentChoiceForm;
import com.ti.specteam.vyper.build.vyper.PraService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.changelink.change.RptChangeDeviceImpStatusRepository;
import com.ti.specteam.vyper.pra.model.Pra;
import com.ti.specteam.vyper.validate.ValidateService;
import com.ti.specteam.vyper.verifier.model.Verifier;
import com.ti.specteam.vyper.verifier.model.VerifierStatus;
import com.ti.specteam.vyper.vscn.actions.CreateVscnAction;
import com.ti.specteam.vyper.vscn.actions.CreateVscnForm;
import com.ti.specteam.vyper.vscn.actions.VscnNumberForm;
import com.ti.specteam.vyper.vscn.model.Vscn;
import com.ti.specteam.vyper.vscn.model.VscnService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ti.specteam.vyper.audit.AuditActivity.MASS_UPLOAD_VSCN_CREATE;

@Service
@RequiredArgsConstructor
public class ProjectDeviceVscnService {

    private final ProjectHeaderRepository projectHeaderRepository;
    private final ProjectDeviceRepository projectDeviceRepository;
    private final AtssRepository atssRepository;
    private final TravelerService travelerService;
    private final CreateVscnAction createVscnAction;
    private final ChangeVscnMaterialAction changeVscnMaterialAction;
    private final ChangeVscnChangeLinkAction changeVscnChangeLinkAction;
    private final ChangeVscnComponentAction changeVscnComponentAction;
    private final ChangeVscnSymbolizationAction changeVscnSymbolizationAction;
    private final VscnUploadTestAction vscnUploadTestAction;
    private final RefreshVscnAction refreshVscnAction;
    private final AuditService auditService;
    private final VscnService vscnService;
    private final ComponentService componentService;
    private final SpecChangeRuleRepository specChangeRuleRepository;
    private final ValidateVscnChangeNumberRequiredAction validateVscnChangeNumberRequiredAction;
    private final RptChangeDeviceImpStatusRepository rptChangeDeviceImpStatusRepository;
    private final VyperService vyperService;
    private final BuildService buildService;
    private final PraService praService;
    private final ValidateService validateService;

    @Autowired
    private final List<String> custNames;

    public List<String> createProjectDeviceVscns(String projNumber, CreateVscnForm vscnForm){
        ProjectHeader projectHeader = projectHeaderRepository.findByProjNumber(projNumber);
        if (projectHeader == null){
            throw new AtssMassUploadProjectNotFound(HttpStatus.BAD_REQUEST);
        }

        List<ProjectDevice> projectDevices = projectDeviceRepository.findProjectDeviceByProjectId(projectHeader.getProjId());
        List<String> createdVscns = new ArrayList<>();

        Vyper vyper = vyperService.fetchVyper(vscnForm.getVyperNumber());
        Build build = buildService.findByBuildNumber(vscnForm.getBuildNumber());
        Pra pra = praService.findByPraNumber(vscnForm.getPraNumber());

        validateService.checkOpen(vyper);
        validateService.checkPraApproved(pra);

        // Create VSCN for each project device
        for(ProjectDevice device : projectDevices){
            Vscn vscn = null;

            // Find device VSCN if any
            if(device.getVscnNumber() != null){
                vscn = vscnService.findByVscnNumber(device.getVscnNumber());
            }
            else{
                vscn = vscnService.create(vyper, build, pra);
            }

            // No need to change facility as project target facility must match VSCN facility(inherited from PRA facility,i.e. refSpec facility)

            // Update VSCN with project device material
            ChangeVscnMaterialForm materialForm = ChangeVscnMaterialForm.builder()
                    .vscnNumber(vscn.getVscnNumber())
                    .material(device.getMaterial())
                    .build();
            changeVscnMaterialAction.changeMaterial(vscn,materialForm);

            // Update VSCN with project changelink number if any
            if(projectHeader.getCmsNumber() != null){
                ChangeVscnChangeLinkForm changelinkForm = ChangeVscnChangeLinkForm.builder()
                        .vscnNumber(vscn.getVscnNumber())
                        .changeNumber(projectHeader.getCmsNumber())
                        .build();
                changeVscnChangeLinkAction.changeChangelinkNumber(vscn, changelinkForm);
            }

            // Update VSCN with device spec changes
            List<SelectionItem> dieItems = new ArrayList<>(); // Hold multiple Dies from spec changes

            for(SpecChange specChange : device.getSpecChanges()){
                if(specChange.getComponentName().equalsIgnoreCase("Die")){
                    SelectionItem sItem = new SelectionItem();
                    sItem.setValue(specChange.getComponentValue());
                    sItem.setEngineering(Engineering.N);
                    dieItems.add(sItem);
                }
                if(specChange.getComponentName().equalsIgnoreCase("Topside Symbol")){
                    String ecat = "";
                    List<CustomObject> cObjs = vscn.getComponents().stream()
                            .filter(c -> custNames.contains(c.getName()))
                            .map(c -> {
                                CustomObject obj = new CustomObject();
                                obj.setName(c.getName());
                                obj.setValue(c.getValue(0,0));
                                return obj;
                            })
                            .collect(Collectors.toList());
                    Optional<Component> opComp = vscn.getComponents().stream()
                            .filter(c -> c.getName().equalsIgnoreCase("ECAT"))
                            .findFirst();
                    if(opComp.isPresent()){
                        ecat = opComp.get().getValue(0,0);
                    }
                    SymbolObject sObj = new SymbolObject();
                    sObj.setName(specChange.getComponentValue());
                    com.ti.specteam.vyper.atss.traveler.Component comp = new com.ti.specteam.vyper.atss.traveler.Component();
                    comp.setName("Topside Symbol");
                    comp.setValue(specChange.getComponentValue());
                    comp.add(atssRepository.readParagraph(comp));
                    if(specChange.getAttributeName() != null){
                        if(!specChange.getAttributeName().equalsIgnoreCase("ECAT")){
                            CustomObject cObj = new CustomObject();
                            cObj.setName(specChange.getAttributeName());
                            cObj.setValue(specChange.getAttributeValue());
                            cObjs.add(cObj);
                        }
                        else{
                            ecat = specChange.getAttributeValue();
                        }
                    }
                    // If no spec change attribute and no existing CUSTx or ECAT value
                    if(cObjs.isEmpty() && ecat.isEmpty()){
                        sObj.setDisplay(travelerService.getLinesFromParagraphs(comp.getParagraphs(), null, "").stream().collect(Collectors.joining("\n")));
                        sObj.setPicture(sObj.getDisplay());
                    }
                    // If paragraph has ECAT embed
                    else if(comp.getParagraphs().stream().anyMatch(p -> p.getEmbedName() != null && p.getEmbedName().equalsIgnoreCase("ECAT"))){
                        sObj.setDisplay(travelerService.getLinesFromParagraphs(comp.getParagraphs(), null, ecat).stream().
                                collect(Collectors.joining("\n")));
                        sObj.setPicture(sObj.getDisplay()
                                .replace(ecat, "{ECAT}"));
                    }
                    // If paragraph has CUSTx embed
                    else{
                        for(CustomObject cObj : cObjs){
                            if(comp.getParagraphs().stream()
                                    .anyMatch(p -> p.getEmbedName() != null && p.getEmbedName().equalsIgnoreCase(cObj.getName()))){
                                sObj.setDisplay(travelerService.getLinesFromParagraphs(comp.getParagraphs(), null, cObj.getValue()).stream().
                                        collect(Collectors.joining("\n")));
                                sObj.setPicture(sObj.getDisplay()
                                        .replace(cObj.getValue(), "{" + cObj.getName() + "}"));
                                break;
                            }
                        }
                    }
                    ChangeVscnSymbolizationForm symbolChangeForm = ChangeVscnSymbolizationForm.builder()
                            .vscnNumber(vscn.getVscnNumber())
                            .symbol(sObj)
                            .customs(cObjs)
                            .ecat(ecat)
                            .build();
                    changeVscnSymbolizationAction.changeSymbolization(vscn, vyper, symbolChangeForm);
                }
                if(specChange.getFlowType().equalsIgnoreCase("TEST") && vscn.getTest().getContent() != null && !vscn.getTest().getContent().isBlank()){
                    String testInfo = vscn.getTest().getContent();
                    int compNameIdx = -1;
                    if(specChange.getOperationName() == null){
                        compNameIdx = testInfo.indexOf(specChange.getComponentName());
                        // Replace with specChange component value wherever present
                        while(compNameIdx != -1){
                            testInfo = updateVscnTestComponentInfo(testInfo, specChange, compNameIdx);
                            compNameIdx = testInfo.indexOf(specChange.getComponentName(),compNameIdx + 1);
                        }
                    }
                    else{
                        int opnNameIdx = testInfo.indexOf(specChange.getOperationName());
                        if(opnNameIdx != -1){
                            compNameIdx = testInfo.indexOf(specChange.getComponentName(),opnNameIdx);
                            // Replace with specChange component value for the specChange operation
                            if(compNameIdx != -1){
                                testInfo = updateVscnTestComponentInfo(testInfo, specChange, compNameIdx);
                            }
                        }
                    }
                    VscnUploadTestForm testForm = VscnUploadTestForm.builder()
                            .vscnNumber(vscn.getVscnNumber())
                            .content(testInfo)
                            .build();
                    vscnUploadTestAction.changeTestInformation(vscn, testForm);
                }
                // TODO: Handle PACK related spec changes for future phase
            }
            // Update VSCN with Die information
            if(!dieItems.isEmpty()){
                ChangeVscnComponentForm compChangeForm = ChangeVscnComponentForm.builder()
                        .vscnNumber(vscn.getVscnNumber())
                        .name("Die")
                        .items(dieItems)
                        .build();
                changeVscnComponentAction.changeComponent(vscn, compChangeForm);
            }

            // Refresh VSCN verifiers
            refreshVscnAction.execute(vyper, vscn);

            auditService.createVscn(
                    vyper.getVyperNumber(),
                    vscn.getVscnNumber(),
                    MASS_UPLOAD_VSCN_CREATE,
                    "Created VSCN for Mass Upload Project: " + projNumber
            );

            vscnService.saveVscn(vscn);

            // Update device with VSCN info
            device.setVscnNumber(vscn.getVscnNumber());
            projectDeviceRepository.save(device);
            createdVscns.add(vscn.getVscnNumber());
        }

        return createdVscns;
    }

    private String updateVscnTestComponentInfo(String testInfo, SpecChange specChange, int compNameIdx){
        // Every component name is succeeded by a ':' and a whitespace, followed by the component value
        int compValStartIdx = testInfo.indexOf(":", compNameIdx) + 2;
        int compValEndIdx = testInfo.indexOf(" ",compValStartIdx) - 1;
        int compValLength =  compValEndIdx - compValStartIdx + 1;
        String newCompVal = String.format("%1$-" + compValLength + "s", specChange.getComponentValue());
        testInfo = testInfo.substring(0,compValStartIdx) + newCompVal + testInfo.substring(compValEndIdx + 1);
        compValEndIdx = testInfo.indexOf(" ",compValStartIdx) - 1;

        // Replace with specChange attribute value if any
        if(specChange.getAttributeName() != null){
            int attrNameIdx = testInfo.indexOf(specChange.getAttributeName(),compValEndIdx + 1);
            if(attrNameIdx != -1){
                // Every attribute name is succeeded by a ':' and a whitespace, followed by the attribute value
                int attrValStartIdx = testInfo.indexOf(":", attrNameIdx) + 2;
                int attrValEndIdx = testInfo.indexOf("\n",attrValStartIdx) - 1;
                int attrValLength =  attrValEndIdx - attrValStartIdx + 1;
                String newAttrVal = String.format("%1$-" + attrValLength + "s",specChange.getAttributeValue());
                testInfo = testInfo.substring(0,attrValStartIdx) + newAttrVal + testInfo.substring(attrValEndIdx + 1);
            }
        }
        return testInfo;
    }

    public void validateVscnForProduction(ProjectHeader projectHeader, ProjectDeviceForm givenDevice){

        if(givenDevice.getVscnNumber() == null) {
            throw new AtssMassUploadException("Vscn Number does not exits for device " + givenDevice.getSpecDevice());
        }

        Vscn vscn = vscnService.fetchVscn(givenDevice.getVscnNumber());
        String validationMessage = verifyVscnProductionReadiness(projectHeader, vscn);
        if ( validationMessage != null){
            throw new AtssMassUploadException(givenDevice.getSpecDevice()+ " : "+validationMessage );
        }
        // Is change link required
        if(validateVscnChangeNumberRequiredAction.execute(VscnNumberForm.builder().vscnNumber(givenDevice.getVscnNumber()).build())) {
            if(givenDevice.getCmsNumber() == null && projectHeader.getCmsNumber() == null) {
                throw new AtssMassUploadException("Changelink number required for device " + givenDevice.getSpecDevice());
            }
            if(!rptChangeDeviceImpStatusRepository.isChangeNumberAndMaterialImplStatusReady(
                    givenDevice.getCmsNumber() == null ? projectHeader.getCmsNumber() : givenDevice.getCmsNumber(), givenDevice.getMaterial())) {
                throw new AtssMassUploadException("Changelink Implementation Status not Ready for device " + givenDevice.getSpecDevice());
            }
        }


    }

    private String verifyVscnProductionReadiness(ProjectHeader projectHeader,Vscn vscn){

        // Die must be of same family of parent
        ComponentChoiceForm dieComponentForm = new ComponentChoiceForm();
        dieComponentForm.setComponentName("Die");
        dieComponentForm.setPraNumber(projectHeader.getRefVyperPraNumber());
        dieComponentForm.setMethod(FilterMethod.PARENT_CHILD_DIE);
        List<String> allowedDies = componentService.componentChoices(dieComponentForm);
        List<String> vscnDies = vscn.getComponents().stream().filter( component -> component.getName().equalsIgnoreCase("Die"))
                .flatMap(Component::stream)
                .flatMap(ComponentInstance::stream)
                .map(ComponentPriority::getValue)
                .collect(Collectors.toList());
        String nonChildDie = vscnDies.stream().filter( vscnDie -> !allowedDies.contains(vscnDie)).findFirst().orElse(null);
        if ( nonChildDie != null){
            return "Die "+nonChildDie + " is not allowed as per validated PRA Dies ";
        }

        // Check verifiers for dedicated components

        List<String> componentNames = specChangeRuleRepository.findAllowedComponentNames();
        Verifier notVerified = vscn.getVerifiers()
                .stream()
                .filter( verifier -> componentNames.contains(verifier.getName()))
                .filter( verifier -> verifier.getStatus() == VerifierStatus.NOT_VERIFIED )
                .findFirst()
                .orElse(null);


        if ( notVerified != null ){
            String errMessage = "Validation failed for "+notVerified.getName() + " "+notVerified.getValue();
            if ( notVerified.getMessages() != null && !notVerified.getMessages().isEmpty() ){
                errMessage = "\n "+ notVerified.getMessages().get(0).getText();
            }

            return errMessage;
        }


        return null;
    }

}
