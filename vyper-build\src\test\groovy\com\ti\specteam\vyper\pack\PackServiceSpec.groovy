package com.ti.specteam.vyper.pack

import com.ti.specteam.vyper.approvaloperation.ApprovalOperation
import com.ti.specteam.vyper.approvaloperation.ApprovalOperationService
import spock.lang.Specification

class PackServiceSpec extends Specification {

    ApprovalOperationService approvalOperationService = Mock(ApprovalOperationService);
    PackService service = new PackService(approvalOperationService)
    def packOperation = new ApprovalOperation("Tape / Reel","_PACK")

    def setup() {
        0 * _
    }

    def "isPack returns false if not a pack operation"() {
        when:
        def ret = service.isPack("Bond")

        then:
        1 * approvalOperationService.findListByGroup('_PACK') >> [packOperation]

        and:
        ret == false;
    }

    def "isPack returns true if a pack operation"() {
        when:
        def ret = service.isPack("Tape / Reel")

        then:
        1 * approvalOperationService.findListByGroup('_PACK') >> [packOperation]

        and:
        ret == true;
    }

}