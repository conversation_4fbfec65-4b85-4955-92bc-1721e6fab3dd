package com.ti.specteam.vswr.vswr.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.beans.factory.annotation.Autowired;

import com.ti.specteam.vswr.vswr.domain.ATSWRForm;
import com.ti.specteam.vswr.vswr.domain.DieLotStatus;
import com.ti.specteam.vswr.vswr.domain.ForecastedInfo;
import com.ti.specteam.vswr.vswr.domain.IntransitStatus;
import com.ti.specteam.vswr.vswr.domain.RequestorInfo;
import com.ti.specteam.vswr.vswr.service.FetchATSWRService;
import com.ti.specteam.vswr.vswr.service.FetchVyperService;
import com.ti.specteam.vyper.security.SecurityService;
import org.springframework.security.access.prepost.PreAuthorize;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@Validated
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/v1/fetch")
@PreAuthorize("@externalAuthCheck.validateUser()")
public class FetchATSWRController {
    @Autowired
    FetchATSWRService fetchATSWRService;

    @Autowired
    FetchVyperService fetchVyperService;
    
    @Autowired
    SecurityService securityService;


    @GetMapping("/helloWorld")
    public ResponseEntity<Map<String,String>> helloWorld(){
        log.info("helloWorld called from FetchATSWRController");
        return ResponseEntity.ok(fetchATSWRService.helloWorld());
    }
    
    @GetMapping("/requestorInfo")
    public ResponseEntity<RequestorInfo> fetchRequestorInfo(){
        log.info("fetchRequestorInfo called from FetchATSWRController");
        try {
            String aid = securityService.user().getUserid();
            System.out.println(aid);
            return  ResponseEntity.ok(fetchATSWRService.fetchRequestorInfo(aid));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new RequestorInfo());
        }
    }

    @GetMapping("/forecastedInfo/{forecastID}")
    public ResponseEntity<ForecastedInfo> fetchForecastedInfo(@PathVariable ("forecastID") String forecastID){
        log.info("fetchForecastedInfo called from FetchATSWRController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchForecastedInfo(forecastID));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new ForecastedInfo());
        }
    }

    @GetMapping("/fetchData/{vbuildID}")
    public ResponseEntity<HashMap<String, Object>> fetchData(
        @PathVariable ("vbuildID") String vbuildID,
        @RequestParam("attributesToFetch") List<String> attributesToFetch
    ){
        log.info("fetchRequestorInfo called from FetchATSWRController");
        try {
            return ResponseEntity.ok(fetchVyperService.fetchData(vbuildID, attributesToFetch));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new HashMap<String, Object>());
        }
    }
    
    @GetMapping("/fetchVswr/{vswrID}")
    public ResponseEntity<ATSWRForm> fetchVswr(@PathVariable ("vswrID") String vswrID){
        try {
            return ResponseEntity.ok(fetchATSWRService.fetchVswr(vswrID));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new ATSWRForm());
        }
    }
    
    @GetMapping("/fetchDieLotStatus")
    public ResponseEntity<List<DieLotStatus>> fetchDieLotStatus(@RequestParam String plant, @RequestParam String material, @RequestParam boolean inPlant){
        try {
            return ResponseEntity.ok(fetchATSWRService.fetchDieLotStatus(material, plant, inPlant));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new ArrayList<DieLotStatus>());
        }
    }

    @GetMapping("/fetchDieLotIntransitStatus")
    public ResponseEntity<List<IntransitStatus>> fetchDieLotIntransitStatus(@RequestParam String plant, @RequestParam String material){
        try {
            return ResponseEntity.ok(fetchATSWRService.fetchDieLotIntransitStatus(material, plant));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(new ArrayList<IntransitStatus>());
        }
    }
}