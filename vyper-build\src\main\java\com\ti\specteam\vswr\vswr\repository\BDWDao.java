package com.ti.specteam.vswr.vswr.repository;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.ti.specteam.vswr.vswr.domain.RequestorInfo;

@Repository
public interface BDWDao {

    public RequestorInfo fetchRequestorInfo(@Param("aid") String aid);

    public String fetchWwid(@Param("oldMaterial") String oldMaterial);

    public String fetchMcm(@Param("oldMaterial") String oldMaterial);

    public String fetchIso(@Param("oldMaterial") String oldMaterial);

    public String fetchApl(@Param("oldMaterial") String oldMaterial);

}
