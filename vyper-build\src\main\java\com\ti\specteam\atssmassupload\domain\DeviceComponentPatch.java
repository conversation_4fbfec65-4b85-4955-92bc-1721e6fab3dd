package com.ti.specteam.atssmassupload.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceComponentPatch {
    String facility;
    String name;
    String value;
    Set<DeviceComponentAttribute> attributes;
    List<String> specDevices;

    public DeviceComponentPatch(DeviceComponent deviceComponent, List<String> specDevicesList){
        facility = deviceComponent.getFacility();
        name = deviceComponent.getName();
        value = deviceComponent.getValue();
        attributes = new HashSet<>();
        attributes.addAll(deviceComponent.getAttributes());
        specDevices = new ArrayList<>();
        specDevices.addAll(specDevicesList);
    }
}
