package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.validate.ValidateService;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_DESCRIPTION;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeDescriptionAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Build execute(ChangeDescriptionForm changeDescriptionForm) {
        log.debug("execute(changeDescriptionForm:{})", changeDescriptionForm);

        Vyper vyper = vyperService.fetchVyper(changeDescriptionForm);
        Build build = buildService.fetchBuild(changeDescriptionForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        execute(vyper, build, changeDescriptionForm);

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_DESCRIPTION,
                "changed description to: " + build.getDescription()
        );

        return buildService.saveBuild(build);
    }

    public Vyper execute(Vyper vyper, Build build, ChangeDescriptionForm changeDescriptionForm) {
        return execute(vyper, build, changeDescriptionForm.getDescription().trim());
    }

    public Vyper execute(Vyper vyper, Build build, String description) {
        if (StringUtils.isBlank(description)) throw new VyperException("The description is invalid.");

        build.setDescription(description);
        return vyper;
    }

}
