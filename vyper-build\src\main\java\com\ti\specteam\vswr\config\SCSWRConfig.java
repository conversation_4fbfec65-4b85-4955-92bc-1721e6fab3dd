package com.ti.specteam.vswr.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@MapperScan(
    basePackages = {"com.ti.specteam.vswr.vswr.scswrRepository", "com.ti.specteam.vswr.dashboard.BatchProcessing.Repository", "com.ti.specteam.vswr.dashboard.repository" },
    sqlSessionTemplateRef = "scswrSessionTemplate"
)
public class SCSWRConfig {

    @Bean(name = "scswr")
    @ConfigurationProperties(prefix = "spring.datasource.scswr")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "scswrSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("scswr") DataSource datasource) throws Exception {
        SqlSessionFactoryBean ssfb = new SqlSessionFactoryBean();
        ssfb.setDataSource(datasource);
        return ssfb.getObject();
    }

    @Bean(name = "scswrSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("scswrSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
