package com.ti.specteam.vswr.dashboard.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ti.specteam.vswr.dashboard.domain.security.VswrRolesContainer;
import com.ti.specteam.vswr.dashboard.service.VswrSecurityService;
import com.ti.specteam.vyper.security.SecurityService;

@RestController
@Validated
@RequestMapping("/v1/vswr/security")
@PreAuthorize("@externalAuthCheck.validateUser()")
public class VswrSecurityController {
    @Autowired
    SecurityService securityService;
    @Autowired
    VswrSecurityService vswrSecurityService;

    @GetMapping("/me")
    public ResponseEntity<VswrRolesContainer> getLoggedInUserVswrRoles() {
        return ResponseEntity.ok(vswrSecurityService.getVswrRolesByAid(securityService.userid()));
    }
}