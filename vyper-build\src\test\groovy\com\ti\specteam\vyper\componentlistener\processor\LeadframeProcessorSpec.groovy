package com.ti.specteam.vyper.componentlistener.processor

import com.ti.specteam.vyper.atss.component.AtssComponentService
import com.ti.specteam.vyper.atss.traveler.Traveler
import com.ti.specteam.vyper.build.model.*
import com.ti.specteam.vyper.componentlistener.context.AtssLoaderListenerContext
import com.ti.specteam.vyper.componentlistener.context.PgsListenerContext
import com.ti.specteam.vyper.componentlistener.processor.utils.MatchContext
import com.ti.specteam.vyper.pgs.PgsParserService
import spock.lang.Specification

class LeadframeProcessorSpec extends Specification {

    PgsParserService pgsParserService = Mock(PgsParserService)
    AtssComponentService atssComponentService = Mock(AtssComponentService)

    LeadframeProcessor processor = new LeadframeProcessor(
            pgsParserService,
            atssComponentService
    )

    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _
    }

    def "onPgsLoader - load the Leadframe"() {
        def bomName1 = new BomName(id: "ID1", name: "BOM1")

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.metadataRefObjectById(_, "ID1") >> [
                attrs: [LeadframePartNumber: ["NUMBER1", "NUMBER2"]]
        ]
        1 * pgsParserService.metadataRefObjectsByType(_, "Leadframe") >> [
                [attrs: [PartNumber: "NUMBER1", LeadframeFinish: "FINISH1", LeadframePartNumber:"NUMBER1"]],
                [attrs: [PartNumber: "NUMBER2", LeadframeFinish: "FINISH2", LeadframePartNumber:"NUMBER2"]]
        ]

        and:
        build1.getComponentKeyValue("Leadframe", 0, 0, "name") == "NUMBER1"
        build1.getComponentKeyValue("Leadframe", 0, 0, "PartNumber") == "NUMBER1"
        build1.getComponentKeyValue("Leadframe", 0, 0, "LeadframeFinish") == "FINISH1"
        build1.getComponentKeyValue("Leadframe", 0, 1, "name") == "NUMBER2"
        build1.getComponentKeyValue("Leadframe", 0, 1, "PartNumber") == "NUMBER2"
        build1.getComponentKeyValue("Leadframe", 0, 1, "LeadframeFinish") == "FINISH2"
    }

    def "onAtssLoader - traveler component not found - marked as processed"() {

        def traveler = new Traveler()
        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Leadframe")
    }

    def "onAtssLoader - component not found - marked as processed"() {

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Leadframe", "CVALUE1")
        tc.create("Supplier Number", "SN12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Leadframe")
    }

    def "onAtssLoader - sets the build component priority value"() {
        build1.findOrCreateComponent("Leadframe", Source.VYPER)

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Leadframe", "CVALUE1")
        tc.create("Supplier Number", "SN12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Leadframe")

        and:
        build1.getComponentKeyValue("Leadframe", 0, 0, "name") == "CVALUE1"
        build1.getComponentKeyValue("Leadframe", 0, 0, "supplier_number") == "SN12345"
    }

    def "onAtssLoader - change pgs source to atss"() {
        def c1 = build1.findOrCreateComponent("Leadframe", Source.PGS)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "CVALUE1", Engineering.N)
        p1.put "supplier_number", "SN12345"

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Leadframe", "CVALUE1")
        tc.create("Supplier Number", "SN12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        build1.findComponentByName("Leadframe").getPriority(0, 0).source.system.name == SystemName.ATSS
    }

    def "matchValue - #description"() {

        ComponentPriority priority = new ComponentPriority()
        priority.setValue(vyperValue, Engineering.N, Source.VYPER)
        priority.setKeyValue("EDGE Number", edgeNumber, Engineering.N, Source.VYPER)

        MatchContext context = new MatchContext()

        when:
        def ret = processor.matchValue(priority, armarcValue, context)

        then:
        ret == result

        and:
        context.match == result
        context.armarcValue == armarcValue || context.armarcValue == ""
        context.vyperValue == vyperValue
        context.edgeNumber == edgeNumber

        where:
        result | armarcValue    | vyperValue     | edgeNumber     | description
        false  | null           | null           | null           | "fail if armarc is null"
        false  | "1234567-1234" | null           | null           | "fail if component not found"
        false  | "1234567-1234" | "1111111-1111" | null           | "fail if values are different"
        true   | "1234567-1234" | "1234567-1234" | null           | "pass if values are the same"
        false  | "1234567"      | "1234567-1234" | null           | "fail if pra values begins with the armarc value"
        false  | "1234567"      | "12345678"     | null           | "fail if no edge number"
        false  | "1234567"      | "12345678"     | "1111111"      | "fail if edge number doesn't match armarc (not sid number)"
        false  | "1234567"      | "12345678"     | "1234567"      | "fail if edge number matches armarc (not sid number)"
        false  | "1234567"      | "12345678"     | "1234567-1234" | "fail if 7-4 edge number doesnt match 7 armarc (not sid number)"
        false  | "1234567-1234" | "12345678"     | "1234567-1234" | "fail if 7-4 edge number matches 7-4 armarc (not sid number)"
        false  | "1234567"      | "SID#12345678" | "1111111"      | "fail if edge number doesn't match armarc"
        false  | "1234567"      | "SID#12345678" | "1234567"      | "fail if edge number matches armarc (not a 7-4)"
        false  | "1234567"      | "SID#12345678" | "1234567-1234" | "fail if 7-4 edge number doesnt match 7 armarc"
        true   | "1234567-1234" | "SID#12345678" | "1234567-1234" | "fail if 7-4 edge number matches 7-4 armarc"
    }

}
