{"groups": [{"name": "atss.api.config", "type": "com.ti.specteam.atssmassupload.config.AtssApiConfig", "sourceType": "com.ti.specteam.atssmassupload.config.AtssApiConfig"}, {"name": "atss.spectrav", "type": "com.ti.specteam.vyper.atss.spectrav.SpecTravProperties", "sourceType": "com.ti.specteam.vyper.atss.spectrav.SpecTravProperties"}, {"name": "bom-template-request-email", "type": "com.ti.specteam.vyper.BomTemplateRequestEmailProperties", "sourceType": "com.ti.specteam.vyper.BomTemplateRequestEmailProperties"}, {"name": "bomtemplate.api", "type": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "comment-notification", "type": "com.ti.specteam.vyper.email.CommentNotificationProperties", "sourceType": "com.ti.specteam.vyper.email.CommentNotificationProperties"}, {"name": "emas.api", "type": "com.ti.specteam.vyper.emas.EmasProperties", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "pgs.api", "type": "com.ti.specteam.vyper.pgs.PgsProperties", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pkgniche.api", "type": "com.ti.specteam.vyper.packageniche.PackageNicheProperties", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "rolesvc.api", "type": "com.ti.specteam.vyper.roleservice.RoleSvcProperties", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "spring.datasource.scswr", "type": "javax.sql.DataSource", "sourceType": "com.ti.specteam.vswr.config.SCSWRConfig", "sourceMethod": "public javax.sql.DataSource dataSource() "}, {"name": "spring.datasource.vyper", "type": "javax.sql.DataSource", "sourceType": "com.ti.specteam.vyper.VyperApplication", "sourceMethod": "public javax.sql.DataSource dataSource() "}, {"name": "task-notification", "type": "com.ti.specteam.vyper.email.TaskNotificationProperties", "sourceType": "com.ti.specteam.vyper.email.TaskNotificationProperties"}, {"name": "task-service.api", "type": "com.ti.specteam.vyper.taskService.TaskServiceProperties", "sourceType": "com.ti.specteam.vyper.taskService.TaskServiceProperties"}, {"name": "vyper.api.config", "type": "com.ti.specteam.vyper.security.SecurityUserProperties", "sourceType": "com.ti.specteam.vyper.security.SecurityUserProperties"}, {"name": "vyper.batchprocessing.email", "type": "com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailCfg", "sourceType": "com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailCfg"}, {"name": "vyper.email", "type": "com.ti.specteam.vyper.email.EmailProperties", "sourceType": "com.ti.specteam.vyper.email.EmailProperties"}, {"name": "vyper.link", "type": "com.ti.specteam.vyper.security.buildlink.VyperLinkProperties", "sourceType": "com.ti.specteam.vyper.security.buildlink.VyperLinkProperties"}], "properties": [{"name": "atss.api.config.api-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.atssmassupload.config.AtssApiConfig"}, {"name": "atss.api.config.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.atssmassupload.config.AtssApiConfig"}, {"name": "atss.api.config.cams-component-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.atssmassupload.config.AtssApiConfig"}, {"name": "atss.spectrav.url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.atss.spectrav.SpecTravProperties"}, {"name": "bom-template-request-email.subject", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.BomTemplateRequestEmailProperties"}, {"name": "bom-template-request-email.to", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.BomTemplateRequestEmailProperties"}, {"name": "bomtemplate.api.access-token-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.client-id", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.client-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.flowtemplates-search-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.grant-type", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.header-name", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "bomtemplate.api.header-value", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.bomtemplate.BomTemplateProperties"}, {"name": "comment-notification.subject", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.CommentNotificationProperties"}, {"name": "emas.api.access-token-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "emas.api.api-cache-time-min", "type": "java.lang.Integer", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "emas.api.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "emas.api.client-id", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "emas.api.client-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "emas.api.vyper-uuid", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.emas.EmasProperties"}, {"name": "pgs.api.access-token-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.arm-arc-job-lookup-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.client-id", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.client-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.device-lookup-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.die-lookup-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.grant-type", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.header-name", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.header-value", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.material-search-by-plant-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pgs.api.material-search-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.pgs.PgsProperties"}, {"name": "pkgniche.api.access-token-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.client-id", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.client-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.grant-type", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.material-niche-by-material-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.material-niche-by-ppp-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "pkgniche.api.pkgniche-info-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.packageniche.PackageNicheProperties"}, {"name": "rolesvc.api.access-token-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "rolesvc.api.authorities-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "rolesvc.api.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "rolesvc.api.client-id", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "rolesvc.api.client-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "rolesvc.api.grant-type", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "rolesvc.api.permissions-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.roleservice.RoleSvcProperties"}, {"name": "task-notification.calltoaction", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.TaskNotificationProperties"}, {"name": "task-notification.enable-emails", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.TaskNotificationProperties"}, {"name": "task-notification.environment", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.TaskNotificationProperties"}, {"name": "task-notification.header", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.TaskNotificationProperties"}, {"name": "task-notification.subject", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.TaskNotificationProperties"}, {"name": "task-service.api.access-token-uri", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.taskService.TaskServiceProperties"}, {"name": "task-service.api.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.taskService.TaskServiceProperties"}, {"name": "task-service.api.client-id", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.taskService.TaskServiceProperties"}, {"name": "task-service.api.client-secret", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.taskService.TaskServiceProperties"}, {"name": "vyper.api.config.emas-cache-time", "type": "java.lang.Long", "sourceType": "com.ti.specteam.vyper.security.SecurityUserProperties"}, {"name": "vyper.api.config.system-users-list", "type": "java.util.List<java.lang.String>", "sourceType": "com.ti.specteam.vyper.security.SecurityUserProperties"}, {"name": "vyper.batchprocessing.email.from", "type": "java.lang.String", "sourceType": "com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailCfg"}, {"name": "vyper.batchprocessing.email.server-name", "type": "java.lang.String", "sourceType": "com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailCfg"}, {"name": "vyper.batchprocessing.email.swr-dashboard-tool-link", "type": "java.lang.String", "sourceType": "com.ti.specteam.vswr.dashboard.email.BatchProcessingEmailCfg"}, {"name": "vyper.email.from", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.email.EmailProperties"}, {"name": "vyper.link.base-url", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.security.buildlink.VyperLinkProperties"}, {"name": "vyper.link.build-selection-link", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.security.buildlink.VyperLinkProperties"}, {"name": "vyper.link.build-single-build-page", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.security.buildlink.VyperLinkProperties"}, {"name": "vyper.link.vscn-single-vscn-page", "type": "java.lang.String", "sourceType": "com.ti.specteam.vyper.security.buildlink.VyperLinkProperties"}], "hints": []}