package com.ti.specteam.vyper.componentlistener.processor

import com.ti.specteam.vyper.atss.component.AtssComponentService
import com.ti.specteam.vyper.atss.traveler.Traveler
import com.ti.specteam.vyper.build.model.*
import com.ti.specteam.vyper.componentlistener.context.AtssLoaderListenerContext
import com.ti.specteam.vyper.componentlistener.context.PgsListenerContext
import com.ti.specteam.vyper.componentlistener.processor.utils.MatchContext
import com.ti.specteam.vyper.config.flowopn.DeviceFlowOperation
import com.ti.specteam.vyper.pgs.PgsParserService
import spock.lang.Specification

class MountCompoundProcessorSpec extends Specification {

    PgsParserService pgsParserService = Mock(PgsParserService)
    AtssComponentService atssComponentService = Mock(AtssComponentService)

    MountCompoundProcessor processor = new MountCompoundProcessor(
            pgsParserService,
            atssComponentService
    )

    def vyper1 = new Vyper()
    def build1 = new Build()

    def setup() {
        0 * _

        def buildFlow = new BuildFlow()
        def flowRow = new DeviceFlowOperation(opnName: "Mount Compound")
        buildFlow.flowRows = [flowRow] // Add flow rows as needed
        build1.setBuildFlow(buildFlow)
    }

    def "onPgsLoader - load the Mount Compound"() {
        def bomName1 = new BomName(id: "ID1", name: "BOM1")

        PgsListenerContext context = PgsListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .bomName(bomName1)
                .build()

        when:
        processor.onPgsLoader(context)

        then:
        1 * pgsParserService.metadataRefObjectById(_, "ID1") >> [
                attrs: [MountCompoundPartNumber: ["NUMBER1", "NUMBER2"]]
        ]
        1 * pgsParserService.metadataRefObjectsByType(_, "MountCompound") >> [
                [attrs:[PartNumber:"NUMBER1", SupplierPartNumber:"SUPPLIER1"]],
                [attrs:[PartNumber:"NUMBER2", SupplierPartNumber:"SUPPLIER2"]]
        ]

        and:
        build1.getComponentKeyValue("Mount Compound", 0, 0, "name") == "SUPPLIER1"
        build1.getComponentKeyValue("Mount Compound", 0, 0, "PartNumber") == "NUMBER1"
        build1.getComponentKeyValue("Mount Compound", 0, 0, "SupplierPartNumber") == "SUPPLIER1"
        build1.getComponentKeyValue("Mount Compound", 0, 1, "name") == "SUPPLIER2"
        build1.getComponentKeyValue("Mount Compound", 0, 1, "PartNumber") == "NUMBER2"
        build1.getComponentKeyValue("Mount Compound", 0, 1, "SupplierPartNumber") == "SUPPLIER2"
    }

    def "onAtssLoader - traveler component not found - marked as processed"() {

        def traveler = new Traveler()
        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Mount Compound")
    }

    def "onAtssLoader - component not found - marked as processed"() {

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Mount Compound", "CVALUE1")
        tc.create("Supplier Number", "SN12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Mount Compound")
    }

    def "onAtssLoader - sets the build component priority value"() {
        build1.findOrCreateComponent("Mount Compound", Source.VYPER)

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Mount Compound", "CVALUE1")
        tc.create("Supplier Number", "SN12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        context.componentsProcessed.contains("Mount Compound")

        and:
        build1.getComponentKeyValue("Mount Compound", 0, 0, "name") == "SN12345"
        build1.getComponentKeyValue("Mount Compound", 0, 0, "supplier_number") == "SN12345"
    }

    def "onAtssLoader - change pgs source to atss"() {
        def c1 = build1.findOrCreateComponent("Mount Compound", Source.PGS)
        def i1 = c1.createInstance()
        def p1 = i1.addPriority("name", "SN12345", Engineering.N)
        p1.put "supplier_number", "SN12345"

        def traveler = new Traveler()
        def sf = traveler.createSubFLow("ASSY")
        def to = sf.create("HEADER")
        def tc = to.create("Mount Compound", "CVALUE1")
        tc.create("Supplier Number", "SN12345")

        Set<String> componentsProcessed = new HashSet<>()

        AtssLoaderListenerContext context = AtssLoaderListenerContext.builder()
                .vyper(vyper1)
                .build(build1)
                .traveler(traveler)
                .componentsProcessed(componentsProcessed)
                .build()

        when:
        processor.onAtssLoader(context)

        then:
        build1.findComponentByName("Mount Compound").getPriority(0, 0).source.system.name == SystemName.ATSS
    }

    def "matchValue - #description"() {

        ComponentPriority priority = new ComponentPriority()
        priority.setValue(vyperValue, Engineering.N, Source.VYPER)
        priority.setKeyValue("PartNumber", partNumber, Engineering.N, Source.VYPER)
        priority.setKeyValue("Supplier Number", supplierNumber, Engineering.N, Source.VYPER)

        MatchContext context = new MatchContext()

        when:
        def ret = processor.matchValue(priority, armarcValue, context)

        then:
        ret == result

        and:
        context.match == ret
        context.partNumber == partNumber
        context.supplierNumber == supplierNumber
        context.vyperValue == vyperValue

        where:
        result | armarcValue    | vyperValue     | partNumber     | supplierNumber | description
        false  | null           | null           | null           | null           | "handle nulls without crash"
        true   | "1111111-1111" | "1111111-1111" | null           | null           | "match value"
        true   | "1111111-1111" | null           | "1111111-1111" | null           | "match part number"
        true   | "ALPHA-AAAA"   | null           | null           | "ALPHA-AAAA"   | "match supplier number"
        true   | "ALPHA#AAAA"   | null           | null           | "ALPHA AAAA"   | "match supplier number (different punctuation)"
    }

}
