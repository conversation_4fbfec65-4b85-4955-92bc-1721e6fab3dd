package com.ti.specteam.vswr.dashboard.utility;

import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellValueMap;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

/** ScswrExcelUtility. */
public class ScswrExcelUtility {
  public static String MULTIVALUE_DELIM = ";";

  /**
   * Returns a string representing an error if the given cell config value isn't found or if the
   * value is found but doesn't match valid options.
   *
   * @param scswrExcelCellConfig excel config to use
   * @param row excel row to validate
   */
  public static String getErrorMessageIfValueNotPresentOrInvalid(
      @Valid ScswrExcelCellConfig scswrExcelCellConfig, Row row) {
    String uploadError = "";
    if (!ScswrExcelUtility.isValuePresent(scswrExcelCellConfig, row)) {
      return scswrExcelCellConfig.getExcelLabel() + " required<br/>";
    }
    if (!ScswrExcelUtility.isValueValid(scswrExcelCellConfig, row)) {
      if (scswrExcelCellConfig.isMultiselect()) {
        uploadError +=
            "\""
                + scswrExcelCellConfig.getExcelLabel()
                + "\" each option must be unique and include one of the following options seperated"
                + " by a semicolon:";
      } else {
        uploadError +=
            "\"" + scswrExcelCellConfig.getExcelLabel() + "\" must be one of the following:";
      }
      List<ScswrExcelCellValueMap> possibleValues = scswrExcelCellConfig.getPossibleValues();
      String delim = "$_";
      for (int i = 0; i < possibleValues.size(); i++) {
        uploadError += delim + "• " + possibleValues.get(i).getExcelValue();
      }
      uploadError += "<br/>";
    }
    return uploadError;
  }

  /**
   * Returns true if there is a value for the given excel configuration.
   *
   * @param scswrExcelCellConfig excel config to use
   * @param row excel row to check from
   * @return true if value is found, else false
   */
  public static boolean isValuePresent(@Valid ScswrExcelCellConfig scswrExcelCellConfig, Row row) {
    Cell cell = row.getCell(scswrExcelCellConfig.getExcelIndex());
    return cell != null && !"".equals(cell.toString());
  }

  /**
   * Returns true if possibleValues is null or empty or if the value extracted from row matches one
   * of the possible values from the excel config.
   *
   * @param scswrExcelCellConfig excel config to use
   * @param row excel row to check from
   * @return true if value is found, else false
   */
  public static boolean isValueValid(ScswrExcelCellConfig scswrExcelCellConfig, Row row) {
    List<ScswrExcelCellValueMap> possibleValues = scswrExcelCellConfig.getPossibleValues();
    if (possibleValues == null || possibleValues.isEmpty()) {
      return true;
    }
    Cell cell = row.getCell(scswrExcelCellConfig.getExcelIndex());

    if (scswrExcelCellConfig.isMultiselect()) {
      String excelValue = cell.toString();
      List<String> excelValueList = List.of(excelValue.split(ScswrExcelUtility.MULTIVALUE_DELIM));
      Optional<String> invalidValue =
          excelValueList.stream()
              .filter(
                  value -> {
                    return !ScswrExcelUtility.isValueValid(possibleValues, value);
                  })
              .findAny();
      boolean areValidValues = !invalidValue.isPresent();

      Set<String> set = new HashSet<String>(excelValueList);
      boolean areUniqueValues = set.size() == excelValueList.size();

      return areValidValues && areUniqueValues;
    } else {
      String excelValue = cell.toString();
      return ScswrExcelUtility.isValueValid(possibleValues, excelValue);
    }
  }

  private static boolean isValueValid(
      List<ScswrExcelCellValueMap> possibleValues, String excelValue) {
    return possibleValues.stream()
        .filter(
            possibleValue -> {
              return StringUtils.equals(possibleValue.getExcelValue(), excelValue)
                  || StringUtils.equals(possibleValue.getDatabaseValue(), excelValue);
            })
        .findAny()
        .isPresent();
  }

  /**
   * Returns true if the given string matches the dbvalue from the given row based on the given
   * config. Immediately returns false if the value in the row is not a valid option
   *
   * @param targetDbValue db value to look for
   * @param scswrExcelCellConfig excel config to use
   * @param row excel row to check from
   * @return true if value is found
   */
  public static boolean isDbValue(
      @Valid @NotBlank String targetDbValue, ScswrExcelCellConfig scswrExcelCellConfig, Row row) {
    if (!ScswrExcelUtility.isValueValid(scswrExcelCellConfig, row)) {
      return false;
    }
    String value = ScswrExcelUtility.extractDbValue(scswrExcelCellConfig, row);
    return value.contentEquals(targetDbValue);
  }

  /**
   * Returns db value of given config from the given row.
   *
   * @param scswrExcelCellConfig excel config to use
   * @param row excel row to check from
   * @return true if value is found, else false
   * @throws NoSuchElementException if the value from the row is not a valid value based on the
   *     excel config
   */
  public static String extractDbValue(@Valid ScswrExcelCellConfig scswrExcelCellConfig, Row row) {
    if (!ScswrExcelUtility.isValuePresent(scswrExcelCellConfig, row)) {
      return null;
    }

    String givenValue = row.getCell(scswrExcelCellConfig.getExcelIndex()).toString();

    List<ScswrExcelCellValueMap> possibleValues = scswrExcelCellConfig.getPossibleValues();
    if (possibleValues == null || possibleValues.isEmpty()) {
      return givenValue;
    }
    Cell cell = row.getCell(scswrExcelCellConfig.getExcelIndex());

    if (scswrExcelCellConfig.isMultiselect()) {
      String excelValue = cell.toString();
      List<String> excelValueList = List.of(excelValue.split(ScswrExcelUtility.MULTIVALUE_DELIM));
      return excelValueList.stream()
          .reduce(
              "",
              (finalString, partialExcelValue) -> {
                String databaseValue =
                    ScswrExcelUtility.getDatabaseValueFromConfigsByExcelValue(
                        possibleValues, partialExcelValue);
                return finalString + databaseValue + ScswrExcelUtility.MULTIVALUE_DELIM;
              });
    } else {
      String excelValue = cell.toString();
      return ScswrExcelUtility.getDatabaseValueFromConfigsByExcelValue(possibleValues, excelValue);
    }
  }

  private static String getDatabaseValueFromConfigsByExcelValue(
      List<ScswrExcelCellValueMap> possibleValues, String excelValue) {
    ScswrExcelCellValueMap foundValueMap =
        possibleValues.stream()
            .filter(
                possibleValue -> {
                  return StringUtils.equals(possibleValue.getExcelValue(), excelValue)
                      || StringUtils.equals(possibleValue.getDatabaseValue(), excelValue);
                })
            .findAny()
            .get();

    return foundValueMap.getDatabaseValue();
  }
}
