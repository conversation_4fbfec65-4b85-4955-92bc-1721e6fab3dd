package com.ti.specteam.atssmassupload.domain;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "MU_REF_SPEC")
@Getter
@Setter
public class ReferenceSpecEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private String id;

    @Column(length = 10)
    private String refType;

    @Column(length = 20)
    private String specDevice;

    @Column(length = 10)
    private String facilityAt;

    @Column(length = 1)
    private String status;

    @Column(length = 30)
    private String vyperBuildNumber;

    @Column(length = 30)
    private String vyperPraNumber;

    @Column(length = 10)
    private String createdBy;

    private Date createdDttm;

    @Column(length = 10)
    private String updatedBy;

    private Date updatedDttm;

}
