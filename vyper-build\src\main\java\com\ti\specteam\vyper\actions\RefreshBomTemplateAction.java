package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.dataloader.BomTemplateLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.REFRESH_BOM_TEMPLATE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshBomTemplateAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final BomTemplateLoader bomTemplateLoader;
    private final AuditService auditService;

    public Build execute(RefreshBomTemplateForm refreshBomTemplateForm) {

        log.debug("execute(refreshBomTemplateForm:{})", refreshBomTemplateForm);

        Vyper vyper = vyperService.fetchVyper(refreshBomTemplateForm);
        Build build = buildService.fetchBuild(refreshBomTemplateForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        bomTemplateLoader.load(vyper, build);

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                REFRESH_BOM_TEMPLATE,
                "refreshed Bill of Process Template"
        );

        return buildService.saveBuild(build);
    }
}
