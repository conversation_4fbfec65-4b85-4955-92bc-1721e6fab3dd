package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;


@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PraToggleCheckedForm extends PraNumberForm {

    @NotNull
    @Size(min = 1)
    private String componentName;

    @NotNull
    private Boolean checked;

}
