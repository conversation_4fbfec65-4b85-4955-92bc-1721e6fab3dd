package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.componentlistener.context.DryBakeListenerContext;
import com.ti.specteam.vyper.componentlistener.listener.DryBakeListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Set the DryBake value based on the MSL component's value
 * if MSL = NC, MSL1, MSL2, MSL2a, then value = No. Else, value = Y
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DryBakeLoader  {

    private final List<DryBakeListener> listeners;

    public void load(@SuppressWarnings("unused") Vyper vyper, Build build) {

        DryBakeListenerContext context = DryBakeListenerContext.builder()
                .vyper(vyper)
                .build(build)
                .build();

        listeners.forEach(listener ->
                listener.onDryBakeLoader(context)
        );

    }

}
