package com.ti.specteam.vyper.vscn.model

import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.pra.model.Pra
import com.ti.specteam.vyper.verifier.VerifierService
import com.ti.specteam.vyper.vscn.entity.VscnEntity
import com.ti.specteam.vyper.vscn.entity.VscnEntityService
import spock.lang.Specification

class VscnServiceSpec extends Specification {

    VscnEntityService vscnEntityService = Mock(VscnEntityService)
    VerifierService verifierService = Mock(VerifierService)
    VscnService service = new VscnService(vscnEntityService,verifierService)

    def vscnEntity1 = new VscnEntity(json: "{1}")
    def vscnEntity2 = new VscnEntity(json: "{2}")
    def vscn1 = Vscn.builder().build()
    def vscn2 = Vscn.builder().build()

    def setup() {
        0 * _
    }

    def "getNextId returns the next vscn id for the vyper number"() {
        Vscn last = Vscn.builder().vscnNumber("VSCN1234567-5555").build()
        VscnEntity lastEntity = new VscnEntity(json: "{}")

        when:
        def ret = service.getNextId("VYPER1234567")

        then:
        1 * vscnEntityService.findFirstByVyperNumberOrderByVscnNumberDesc("VYPER1234567") >> lastEntity
        1 * vscnEntityService.fromJson("{}") >> last

        and:
        ret == 5556
    }

    def "getNextId returns the 1 if the vyper project doesn't have any vscns"() {
        when:
        def ret = service.getNextId("VYPER1234567")

        then:
        1 * vscnEntityService.findFirstByVyperNumberOrderByVscnNumberDesc("VYPER1234567") >> null

        and:
        ret == 1
    }

    def "create initializes the vscn object and saves it"() {

        def vyper1 = new Vyper(vyperNumber: "VYPER1234567")
        def build1 = new Build(buildNumber: "VBUILD1234567-0100")
        def pra1 = new Pra(praNumber: "PRA1234567-1000")

        when:
        def ret = service.create(vyper1, build1, pra1)

        then:
        1 * vscnEntityService.findFirstByVyperNumberOrderByVscnNumberDesc("VYPER1234567") >> null
        1 * vscnEntityService.toJson(_) >> "{}"
        1 * vscnEntityService.create(_) >> { VscnEntity ve ->
            ve.vscnNumber == "VSCN1234567-0001"
            ve.praNumber == "PRA1234567-1000"
            ve.buildNumber == "VBUILD1234567-0100"
            ve.vyperNumber == "VYPER1234567"
            ve.json == "{}"
            ve
        }
        1 * verifierService.initializeVerifiers(_)

        and:
        ret.version == 0
        ret.vscnNumber == "VSCN1234567-0001"
        ret.praNumber == "PRA1234567-1000"
        ret.buildNumber == "VBUILD1234567-0100"
        ret.vyperNumber == "VYPER1234567"
        ret.state == VscnState.VSCN_DRAFT
    }

    def "findByVscnNumber (found) returns the vscn"() {

        when:
        def ret = service.findByVscnNumber("VSCN1234567-1234")

        then:
        1 * vscnEntityService.findByVscnNumber("VSCN1234567-1234") >> vscnEntity1
        1 * vscnEntityService.fromJson("{1}") >> vscn1

        and:
        ret == vscn1
    }

    def "findByVscnNumber (not found) returns the null"() {

        when:
        def ret = service.findByVscnNumber("VSCN1234567-1234")

        then:
        1 * vscnEntityService.findByVscnNumber("VSCN1234567-1234") >> null

        and:
        ret == null
    }

    def "findAllByPraNumber returns the vscns"() {

        when:
        def ret = service.findAllByPraNumber("PRA1234567-1234")

        then:
        1 * vscnEntityService.findAllByPraNumber("PRA1234567-1234") >> [vscnEntity1, vscnEntity2]
        1 * vscnEntityService.fromJson("{1}") >> vscn1
        1 * vscnEntityService.fromJson("{2}") >> vscn2

        and:
        ret == [vscn1, vscn2]
    }

    def "findAllByBuildNumber returns the vscns"() {

        when:
        def ret = service.findAllByBuildNumber("VBUILD1234567-1234")

        then:
        1 * vscnEntityService.findAllByBuildNumber("VBUILD1234567-1234") >> [vscnEntity1, vscnEntity2]
        1 * vscnEntityService.fromJson("{1}") >> vscn1
        1 * vscnEntityService.fromJson("{2}") >> vscn2

        and:
        ret == [vscn1, vscn2]
    }

    def "findAllByVyperNumber returns the vscns"() {

        when:
        def ret = service.findAllByVyperNumber("VYPER1234567")

        then:
        1 * vscnEntityService.findAllByVyperNumber("VYPER1234567") >> [vscnEntity1, vscnEntity2]
        1 * vscnEntityService.fromJson("{1}") >> vscn1
        1 * vscnEntityService.fromJson("{2}") >> vscn2

        and:
        ret == [vscn1, vscn2]
    }

    def "saveScn fetches the VSCN, updates it, and saves it"() {
        vscn1.vscnNumber = "VSCN1234567-0001"

        when:
        def ret = service.saveVscn(vscn1)

        then:
        1 * vscnEntityService.fetch("VSCN1234567-0001") >> vscnEntity1
        1 * vscnEntityService.toJson(vscn1) >> "{3}"
        1 * vscnEntityService.update(vscnEntity1) >> vscnEntity1

        and:
        ret == vscn1
        vscnEntity1.json == "{3}"
    }

}
