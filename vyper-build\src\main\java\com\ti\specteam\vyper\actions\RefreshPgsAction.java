package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.*;
import com.ti.specteam.vyper.build.model.*;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.componentlistener.context.PgsRefreshListenerContext;
import com.ti.specteam.vyper.componentlistener.listener.PgsRefreshListener;
import com.ti.specteam.vyper.pgs.PgsService;
import com.ti.specteam.vyper.topsymbol.TopSymbolLoader;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ti.specteam.vyper.audit.AuditActivity.REFRESH_PGS;

/**
 * This code will replace the component/selection values with data from PGS.
 * VYPER-1761
 *
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RefreshPgsAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final PgsLoader pgsLoader;
    private final TopSymbolLoader topSymbolLoader;
    private final AuditService auditService;
    private final PackageNicheLoader packageNicheLoader;
    private final RequiredComponentLoader requiredComponentLoader;
    private final BomTemplateLoader bomTemplateLoader;
    private final FlowLoader flowLoader;
    private final AtssLoader atssLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final CustLoader custLoader;
    private final EcatLoader ecatLoader;
    private final PgsService pgsService;
    private final ChangeMaterialAction changeMaterialAction;
    private final FluxLoader fluxLoader;

    private final List<PgsRefreshListener> listeners;

    public Build execute(RefreshPgsForm refreshPgsForm) {

        log.debug("execute(refreshPgsForm:{})", refreshPgsForm);

        Vyper vyper = vyperService.fetchVyper(refreshPgsForm);
        Build build = buildService.fetchBuild(refreshPgsForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);
        validateService.checkEditable(vyper, build);

        // retrieve pgs data
        Build buildPgs = new Build();
        buildPgs.getMaterial().getObject().putAll(build.getMaterial().getObject());
        buildPgs.getFacility().getObject().putAll(build.getFacility().getObject());
        pgsLoader.load(vyper, buildPgs);

        // refresh the pgs fields
        List<String> changeLog = new ArrayList<>();

        refreshMaterialAndFacilities(build);
        refreshWaferSaw(build, buildPgs, changeLog);

        refreshIncomingWaferThickness(build, buildPgs, changeLog);

        PgsRefreshListenerContext context = PgsRefreshListenerContext.builder()
                .vyper(vyper)
                .build(build)
                .buildPgs(buildPgs)
                .changeLog(changeLog)
                .build();

        listeners.forEach(listener ->
                listener.onPgsRefresh(context)
        );

        // re-load the rest of the build
        if (!topSymbolLoader.load(vyper, build)) {
            changeLog.add("The symbolization has changed.");
        }

        fluxLoader.load(vyper, build);

        if (!packageNicheLoader.load(vyper, build)) {
            changeLog.add("The package niche has changed.");
        }

        String oldName = build.getBomTemplateName();
        bomTemplateLoader.load(vyper, build);
        String newName = build.getBomTemplateName();
        if (!StringUtils.equalsIgnoreCase(oldName, newName)) {
            changeLog.add("The Bill of Process Template has changed.");
        }

        atssLoader.loadAttributes(vyper, build);

        oldName = build.findComponentValueByName("ECAT", 0, 0);
        newName = ecatLoader.load(vyper, build);
        if (!StringUtils.equalsIgnoreCase(oldName, newName)) {
            changeLog.add("The ecat has changed.");
        }

        custLoader.load(vyper, build);
        flowLoader.load(vyper, build);
        requiredComponentLoader.load(vyper, build);
        selectionLoader.load(vyper, build);
        travelerRefreshService.load(vyper, build);
        validateOperationLoader.load(vyper, build);

        build.getVyperCopiedFrom().clear();

        build.getPgs().setLastChange(String.join("\n", changeLog));

        // save the audit
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                REFRESH_PGS,
                "refreshed PGS"
        );

        return buildService.saveBuild(build);
    }

    public void refreshMaterialAndFacilities(Build build) {
        Map<String, Object> pgsData = pgsService.fetchMaterialSearchUrl(build.getMaterial().getMaterial(), 100);
        changeMaterialAction.loadMaterial(pgsData, build);
        changeMaterialAction.loadFacilities(pgsData, build);
    }

    @SuppressWarnings("StatementWithEmptyBody")
    public void refreshWaferSaw(Build build, Build buildPgs, List<String> changeLog) {

        build.getWaferSawMethod().getSource().set(Source.PGS);

        String valueBuild = build.getWaferSawMethod().getValue();
        String valuePgs = buildPgs.getWaferSawMethod().getValue();

        if (null == valuePgs) {
            if (null == valueBuild) {
                // both are null, do nothing
            } else {
                // if build is from pgs, the remove it. otherwise, don't change.
                if (build.getWaferSawMethod().getSource().matches(SystemName.PGS)) {
                    build.getWaferSawMethod().getObject().clear();
                    build.getWaferSawMethod().getObject().putAll(buildPgs.getWaferSawMethod().getObject());
                    changeLog.add("Removing Wafer Saw value of " + valueBuild + " as PGS doesn't have a value.");
                }

            }
        } else {
            if (null == valueBuild) {
                // update the build to the pgs value
                build.getWaferSawMethod().getObject().clear();
                build.getWaferSawMethod().getObject().putAll(buildPgs.getWaferSawMethod().getObject());
                build.getWaferSawMethod().getSource().set(Source.PGS);
                changeLog.add("Setting Wafer Saw value to " + valuePgs + ".");
            } else {
                // if build is from pgs, then update. otherwise, don't change it
                if (build.getWaferSawMethod().getSource().matches(SystemName.PGS)) {

                    if (!StringUtils.equals(valueBuild, valuePgs)) {
                        build.getWaferSawMethod().getObject().clear();
                        build.getWaferSawMethod().getObject().putAll(buildPgs.getWaferSawMethod().getObject());
                        build.getWaferSawMethod().getSource().set(Source.PGS);
                        changeLog.add("Changing Wafer Saw value from " + valueBuild + " to " + valuePgs + ".");
                    }
                }
            }

        }
    }

    /**
     * Copy the incoming wafer thickness from the pgs build to the build.
     *
     * @param build     {@link Build}
     * @param buildPgs  {@link Build}
     * @param changeLog A list of string, with each string is a change in data
     */
    public void refreshIncomingWaferThickness(Build build, Build buildPgs, List<String> changeLog) {

        // copy the incoming wafer thickness
        for (int n = 0; n < buildPgs.getDies().size(); ++n) {
            DieInstance instanceBuild = build.getDies().findOrCreateInstance(n);
            DieInstance instancePgs = buildPgs.getDies().get(n);

            for (int dieIndex = 0; dieIndex < instanceBuild.getDies().size(); ++dieIndex) {
                Die dieBuild = instanceBuild.findOrCreateDie(dieIndex);
                Die diePgs = instancePgs.findOrCreateDie(dieIndex);

                Integer valueBuild = dieBuild.getIncomingWaferThick();
                Integer valuePgs = diePgs.getIncomingWaferThick();

                // if the pgs value is null, don't change the build
                if (valuePgs == null) {
                    continue;
                }

                if (!valuePgs.equals(valueBuild)) {
                    changeLog.add("Changing die " + dieBuild.getName() + " incoming wafer thickness to " + valuePgs);
                    dieBuild.setIncomingWaferThick(diePgs.getIncomingWaferThick());
                }
            }

        }

    }

}
