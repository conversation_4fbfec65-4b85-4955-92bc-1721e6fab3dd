package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_TITLE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeTitleAction {

    private final VyperService vyperService;
    private final ValidateService validateService;
    private final AuditService auditService;

    public Vyper execute(ChangeTitleForm changeTitleForm) {
        log.debug("execute(changeTitleForm:{})", changeTitleForm);

        Vyper vyper = vyperService.fetchVyper(changeTitleForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);

        vyper.setTitle(changeTitleForm.getTitle());

        // save the audit
        auditService.createVyper(
                vyper.getVyperNumber(),
                CHANGE_TITLE,
                "changed title to: " + vyper.getTitle()
        );

        return vyperService.saveVyper(vyper);
    }

}
