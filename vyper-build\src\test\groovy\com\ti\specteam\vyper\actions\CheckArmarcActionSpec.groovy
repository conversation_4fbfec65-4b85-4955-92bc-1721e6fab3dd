package com.ti.specteam.vyper.actions

import com.ti.specteam.vyper.armarc.ArmArcLookupService
import com.ti.specteam.vyper.armarc.Armarc
import com.ti.specteam.vyper.build.model.Build
import com.ti.specteam.vyper.build.model.Engineering
import com.ti.specteam.vyper.build.model.Source
import com.ti.specteam.vyper.build.model.Vyper
import com.ti.specteam.vyper.build.vyper.BuildService
import com.ti.specteam.vyper.build.vyper.VyperService
import com.ti.specteam.vyper.componentlistener.processor.LeadframeProcessor
import com.ti.specteam.vyper.componentlistener.processor.MoldCompoundProcessor
import com.ti.specteam.vyper.componentlistener.processor.MountCompoundProcessor
import com.ti.specteam.vyper.componentlistener.processor.WireProcessor
import com.ti.specteam.vyper.validate.ValidateService
import spock.lang.Specification

class CheckArmarcActionSpec extends Specification {

    VyperService vyperService = Mock(VyperService)
    BuildService buildService = Mock(BuildService)
    ValidateService validateService = Mock(ValidateService)
    ArmArcLookupService armArcLookupService = Mock(ArmArcLookupService)
    LeadframeProcessor leadframeProcessor = Mock(LeadframeProcessor)
    MoldCompoundProcessor moldCompoundProcessor = Mock(MoldCompoundProcessor)
    MountCompoundProcessor mountCompoundProcessor = Mock(MountCompoundProcessor)
    WireProcessor wireProcessor = Mock(WireProcessor)

    CheckArmarcAction action = new CheckArmarcAction(
            vyperService,
            buildService,
            validateService,
            armArcLookupService,
            leadframeProcessor,
            moldCompoundProcessor,
            mountCompoundProcessor,
            wireProcessor
    )

    CheckArmarcForm form1 = new CheckArmarcForm()
    def vyper1 = new Vyper()
    def build1 = new Build()

    Armarc armarc1 = new Armarc(
            leadframe: "ARMARC_LEADFRAME1",
            mountCompound: "ARMARC_MOUNT_COMPOUND1",
            moldCompound: "ARMARC_MOLD_COMPOUND1",
            wireType: "ARMARC_TYPE1",
            wireDiameter: "ARMARC_DIAMETER1"
    )

    def setup() {
        0 * _

        def c1 = build1.findOrCreateComponent("Leadframe", Source.VYPER)
        c1.setValue(0, 0, "BUILD_LEADFRAME", Engineering.N, Source.VYPER)
        c1.getPriority(0, 0).setArmarcCheckMessage("OLD_MESSAGE_1")

        def c2 = build1.findOrCreateComponent("Mount Compound", Source.VYPER)
        c2.setValue(0, 0, "BUILD_MOUNT_COMPOUND", Engineering.N, Source.VYPER)
        c2.getPriority(0, 0).setArmarcCheckMessage("OLD_MESSAGE_2")

        def c3 = build1.findOrCreateComponent("Mold Compound", Source.VYPER)
        c3.setValue(0, 0, "BUILD_MOLD_COMPOUND", Engineering.N, Source.VYPER)
        c3.getPriority(0, 0).setArmarcCheckMessage("OLD_MESSAGE_3")

        def c4 = build1.findOrCreateComponent("Wire", Source.VYPER)
        c4.setValue(0, 0, "BUILD_WIRE", Engineering.N, Source.VYPER)
        c4.getPriority(0, 0).setArmarcCheckMessage("OLD_MESSAGE_4")
    }

    def "execute clears existing warning if no mismatches"() {

        when:
        def ret = action.execute(form1)

        then:
        1 * vyperService.fetchVyper(form1) >> vyper1
        1 * buildService.fetchBuild(form1) >> build1
        1 * validateService.checkEditable(vyper1, build1)
        1 * armArcLookupService.findArmarc(build1) >> armarc1
        1 * mountCompoundProcessor.matchValue(_, _, _) >> true
        1 * moldCompoundProcessor.matchValue(_, _, _) >> true
        1 * leadframeProcessor.matchValue(_, _, _) >> true
        1 * wireProcessor.matchValue(_, _, _, _) >> true

        1 * buildService.saveBuild(build1) >> build1

        and:
        ret == build1

        and:
        null == ret.findComponentPriority("Leadframe", 0, 0).getArmarcCheckMessage()
        null == ret.findComponentPriority("Mount Compound", 0, 0).getArmarcCheckMessage()
        null == ret.findComponentPriority("Mold Compound", 0, 0).getArmarcCheckMessage()
        null == ret.findComponentPriority("Wire", 0, 0).getArmarcCheckMessage()
    }

    def "execute sets warning if mismatches"() {

        when:
        def ret = action.execute(form1)

        then:
        1 * vyperService.fetchVyper(form1) >> vyper1
        1 * buildService.fetchBuild(form1) >> build1
        1 * validateService.checkEditable(vyper1, build1)
        1 * armArcLookupService.findArmarc(build1) >> armarc1
        1 * mountCompoundProcessor.matchValue(_, _, _) >> false
        1 * moldCompoundProcessor.matchValue(_, _, _) >> false
        1 * leadframeProcessor.matchValue(_, _, _) >> false
        1 * wireProcessor.matchValue(_, _, _, _) >> false

        1 * buildService.saveBuild(build1) >> build1

        and:
        ret == build1

        and:
        ret.findComponentPriority("Leadframe", 0, 0).getArmarcCheckMessage() == "Leadframe: ARMARC = ARMARC_LEADFRAME1, Build = BUILD_LEADFRAME"
        ret.findComponentPriority("Mount Compound", 0, 0).getArmarcCheckMessage() == "Mount Compound: ARMARC = ARMARC_MOUNT_COMPOUND1, Build = BUILD_MOUNT_COMPOUND"
        ret.findComponentPriority("Mold Compound", 0, 0).getArmarcCheckMessage() == "Mold Compound: ARMARC = ARMARC_MOLD_COMPOUND1, Build = BUILD_MOLD_COMPOUND"
        ret.findComponentPriority("Wire", 0, 0).getArmarcCheckMessage() == "Wire: ARMARC = ARMARC_TYPE1 ARMARC_DIAMETER1, Build = BUILD_WIRE"
    }

}
