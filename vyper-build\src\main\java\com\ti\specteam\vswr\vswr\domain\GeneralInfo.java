package com.ti.specteam.vswr.vswr.domain;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneralInfo {
    private String vswrID;
    private String title;
    private String currentStatus;
    private String currentRequestor;
    private String purpose;
    private String plant;
    private String specDevice;
    private String facility;
    private String requestDate;
    private String swrType;
    private String swrTypeCategory;
    private String swrTypeFlag;
    private String buildType;
    private String priority;
    private String vbuildID;

    @Builder.Default
    private String existingScswrID = "";

    private String purchaseOrder;
    private String lineItem;
    private String io;
    private String groupEmail;
    private String copyEmail;

    private String vbuildMaterial;
    private String scswrMaterial;
    private String scswrPlant;
    private String scswrFacility;
    private String scswrSpecDevice;

    private String pin;
    private String pkg;
    private String pkgGroup;
    
    private String scswrcontrolnumber;

}