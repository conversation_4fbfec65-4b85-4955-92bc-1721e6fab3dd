package com.ti.specteam.vswr.vswr.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialInfo {

    private String vswrID;
    private int sequence;
    private String material;
    private String plant;
    private String component;
    private String travelerComponent;
    private String forecastedComponent;
    private String match;
    private String isAvailable;
    private String unrestricted;
    private String stock;
    private String qual;
    private String restricted;
    private String blocked;
    private String returns;

}
