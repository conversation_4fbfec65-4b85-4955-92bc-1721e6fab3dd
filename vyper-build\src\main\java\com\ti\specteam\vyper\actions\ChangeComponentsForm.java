package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Component;
import com.ti.specteam.vyper.build.model.ComponentInstance;
import com.ti.specteam.vyper.build.model.ComponentPriority;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeComponentsForm extends BuildNumberForm {

    @NotNull
    private Component component;

    private Boolean revertPgs = false;

    public String display() {

        List<String> names = new ArrayList<>();

        for (ComponentInstance i : component.getInstances()) {
            for (ComponentPriority p : i.getPriorities()) {
                names.add(p.getValue());
            }
        }

        return String.join(", ", names);
    }

}
