package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.TravelerRefreshService;
import com.ti.specteam.vyper.build.dataloader.RequiredComponentLoader;
import com.ti.specteam.vyper.build.dataloader.SelectionLoader;
import com.ti.specteam.vyper.build.dataloader.ValidateOperationLoader;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.FlowComponent;
import com.ti.specteam.vyper.build.model.FlowOperation;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_FLOW_COMPONENT;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeFlowComponentAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final RequiredComponentLoader requiredComponentLoader;
    private final SelectionLoader selectionLoader;
    private final TravelerRefreshService travelerRefreshService;
    private final ValidateOperationLoader validateOperationLoader;
    private final AuditService auditService;

    public Build execute(ChangeFlowComponentForm changeFlowComponentForm) {
        log.debug("execute(changeFlowComponentForm:{})", changeFlowComponentForm);

        Vyper vyper = vyperService.fetchVyper(changeFlowComponentForm);
        Build build = buildService.fetchBuild(changeFlowComponentForm);

        validateService.checkOpen(vyper);
        validateService.checkOwnerOrAtEditAccess(vyper, build);
        validateService.checkEditable(vyper, build);

        // get the operation
        FlowOperation flowOperation = build.getFlow().getObject().getOperations().stream()
                .filter(fo -> StringUtils.equalsIgnoreCase(fo.getName(), changeFlowComponentForm.getOperation()))
                .findFirst()
                .orElse(null);

        if (null != flowOperation) {

            // get the component
            FlowComponent flowComponent = flowOperation.getComponents().stream()
                    .filter(fc -> StringUtils.equalsIgnoreCase(fc.getName(), changeFlowComponentForm.getOldName()))
                    .findFirst()
                    .orElse(null);

            if (null != flowComponent) {

                // change the name
                flowComponent.setName(changeFlowComponentForm.getNewName());
                flowComponent.getSource().appointUser(securityService.user());

                // refresh
                requiredComponentLoader.load(vyper, build);
                selectionLoader.load(vyper, build);
                travelerRefreshService.load(vyper, build);
                validateOperationLoader.load(vyper, build);

                // create the audit record
                auditService.createBuild(
                        vyper.getVyperNumber(),
                        build.getBuildNumber(),
                        CHANGE_FLOW_COMPONENT,
                        "changed flow component: from " + changeFlowComponentForm.getOperation() + " - " + changeFlowComponentForm.getOldName() + " to " + changeFlowComponentForm.getOperation() + " - " + changeFlowComponentForm.getNewName()
                );

            }

        }

        return buildService.saveBuild(build);
    }

}
