package com.ti.specteam.vyper.actions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class FillComponentAtssForm extends BuildNumberForm {

    @Size(min = 1, max = 20)
    @NotNull
    private String facility;

    @Size(min = 1, max = 50)
    @NotNull
    private String material;

    @Size(min = 1, max = 50)
    @NotNull
    private String status;

    public String getDisplay() {
        return facility + " " + material + " " + status;
    }

}
