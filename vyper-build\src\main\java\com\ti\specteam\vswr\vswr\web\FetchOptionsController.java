package com.ti.specteam.vswr.vswr.web;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.ti.specteam.vswr.vswr.service.FetchATSWRService;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;

import lombok.extern.slf4j.Slf4j;

@RestController
@Validated
@Slf4j
@RequestMapping("/v1/fetchOptions")
@PreAuthorize("@externalAuthCheck.validateUser()")
public class FetchOptionsController {
    @Autowired
    FetchATSWRService fetchATSWRService;
    
    @GetMapping("/helloWorld")
    public ResponseEntity<Map<String,String>> helloWorld(){
        log.info("helloWorld called from FetchOptionsController");
        return ResponseEntity.ok(fetchATSWRService.helloWorld());
    }

    @GetMapping("/swrTypeOptions/{vBuildType}")
    public ResponseEntity<List<HashMap<String, String>>> fetchSwrTypeOptions(@PathVariable ("vBuildType") String vBuildType){
        log.info("fetchSwrTypeOptions called from FetchOptionsController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchSwrTypeOptions(vBuildType));
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }
    
    @GetMapping("/priorityOptions")
    public ResponseEntity<List<HashMap<String, String>>> fetchPriorityOptions(){
        log.info("fetchPriorityOptions called from FetchOptionsController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchPriorityOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }

    @GetMapping("/offloadInfoOptions")
    public ResponseEntity<List<HashMap<String, String>>> fetchOffloadInfoOptions(){
        log.info("fetchOffloadInfoOptions called from FetchOptionsController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchOffloadInfoOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }

    @GetMapping("/finishedGoodOptions")
    public ResponseEntity<List<HashMap<String, String>>> fetchFinishedGoodOptions(){
        log.info("fetchFinishedGoodOptions called from FetchOptionsController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchFinishedGoodOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }

    @GetMapping("/waferSkeletonOptions")
    public ResponseEntity<List<HashMap<String, String>>> fetchWaferSkeletonOptions(){
        log.info("fetchWaferSkeletonOptions called from FetchOptionsController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchWaferSkeletonOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }

    @GetMapping("/shippingPlantOptions")
    public ResponseEntity<HashMap<String, HashMap<String, String>>> fetchShippToPlantOptions(){
        log.info("fetchShippToPlantOptions called from FetchOptionsController");
        try {
            return   ResponseEntity.ok(fetchATSWRService.fetchShippToPlantOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }

    @GetMapping("/stateOfFinishOptions")
    public ResponseEntity<List<HashMap<String, String>>> fetchStateOfFinishOptions(){
        log.info("fetchStateOfFinishOptions called from FetchOptionsController");
        try {
            return ResponseEntity.ok(fetchATSWRService.fetchStateOfFinishOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }

    @GetMapping("/dieLocation")
    public ResponseEntity<List<HashMap<String, String>>> fetchDieLocationOptions(){
        log.info("fetchDieLocationOptions called from FetchOptionsController");
        try {
            return ResponseEntity.ok(fetchATSWRService.fetchDieLocationOptions());
        } catch (Exception e) {
            log.error(e.toString());
            return ResponseEntity.ok(null);
        }
    }
}