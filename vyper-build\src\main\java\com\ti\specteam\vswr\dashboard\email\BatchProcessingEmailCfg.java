package com.ti.specteam.vswr.dashboard.email;

import lombok.Data;

import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "vyper.batchprocessing.email", ignoreUnknownFields = false)
@Data
public class BatchProcessingEmailCfg {
  private String from;
  private String serverName;
  private String swrDashboardToolLink;

  @Bean
  public VelocityEngine velocityEngine() {
    VelocityEngine velocityEngine = new VelocityEngine();
    velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "class,file");
    velocityEngine.setProperty("class.resource.loader.class",
        ClasspathResourceLoader.class.getName());
    velocityEngine.setProperty("runtime.log.logsystem.class", "org.apache.velocity.runtime.log.NullLogSystem");
    velocityEngine.init();

    return velocityEngine;
  }
}
