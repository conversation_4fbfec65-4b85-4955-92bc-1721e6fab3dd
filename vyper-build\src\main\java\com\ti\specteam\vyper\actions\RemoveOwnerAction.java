package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperCantRemoveYourselfException;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ti.specteam.vyper.audit.AuditActivity.REMOVE_OWNER;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RemoveOwnerAction {

    private final VyperService vyperService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final AuditService auditService;

    public Vyper execute(RemoveOwnerForm removeOwnerForm) {
        log.debug("execute(addEditorForm:{})", removeOwnerForm);

        Vyper vyper = vyperService.fetchVyper(removeOwnerForm);

        validateService.checkOpen(vyper);
        validateService.checkOwner(vyper);

        String currentUserId = securityService.userid();

        User user = vyper.getOwners().stream()
                .filter(u -> u.getUserid().equalsIgnoreCase(removeOwnerForm.getUserid()))
                .findFirst()
                .orElse(null);

        if(user != null) {

            // you can't remove yourself
            if (StringUtils.equalsIgnoreCase(currentUserId, removeOwnerForm.getUserid())) {
                throw new VyperCantRemoveYourselfException();
            }

            vyper.getOwners().removeIf(o ->
                    Objects.equals(o.getUserid(), removeOwnerForm.getUserid()));

            // save the audit
            auditService.createVyper(
                    vyper.getVyperNumber(),
                    REMOVE_OWNER,
                    "removed owner: " + user.display()
            );

        }

        return vyperService.saveVyper(vyper);
    }

}
