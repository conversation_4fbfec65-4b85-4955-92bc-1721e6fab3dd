<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.vswr.repository.ATSWRDao">
    <resultMap id="ForecastedMap" type="com.ti.specteam.vswr.vswr.domain.ForecastedInfo">
        <result property="swrID" column="SWR_ID"/>
        <result property="title" column="TITLE"/>
        <result property="purpose" column="PURPOSE"/>
        <result property="generalComment" column="COMMENT_REQ"/>
        <result property="material" column="MATERIAL"/>
        <result property="currentStatus" column="CURRENT_STATUS"/>
        <result property="sbe1Site" column="SBE1SITE"/>
        <result property="requestorName" column="REQUESTOR_NAME"/>
        <result property="forecastedFlag" column="FORECASTED_FLAG"/>
        <result property="purchaseOrder" column="PURCHASE_ORDER"/>
        <result property="lineItem" column="LINE_ITEM"/>
        <result property="io" column="IO_NUMBER"/>
        <result property="leadframe" column="LEADFRAME"/>
        <result property="moldCompound" column="MOLD_COMPOUND"/>
        <result property="mountCompound" column="MOUNT_COMPOUND"/>
        <result property="wire" column="WIRE"/>
        <result property="solderball" column="SOLDERBALL"/>
        <result property="chipCapacitor" column="CHIP_CAPACITOR"/>
        <result property="lid" column="LID"/>
        <result property="canister" column="CANISTER"/>
        <result property="dieBoat" column="DIE_BOAT"/>
        <result property="frame" column="FRAME"/>
        <result property="packFillerJewelBox" column="PACK_FILLER_JEWEL_BOX"/>
        <result property="shipTube" column="SHIP_TUBE"/>
        <result property="waferVial" column="WAFER_VIAL"/>
        <result property="tray" column="TRAY"/>
        <result property="reelTapeAndReel" column="REEL_TAPE_AND_REEL"/>
        <result property="tapeTapeAndReel" column="TAPE_TAPE_AND_REEL"/>
        <result property="tapeCover" column="TAPE_COVER"/>
    </resultMap>

    <resultMap id="MaterialMap" type="com.ti.specteam.vswr.vswr.domain.MaterialInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="sequence" column="SEQUENCE"/>
        <result property="material" column="MATERIAL"/>
        <result property="plant" column="PLANT"/>
        <result property="component" column="COMPONENT_NAME"/>
        <result property="travelerComponent" column="TRAVELER_COMPONENT_VALUE"/>
        <result property="forecastedComponent" column="FORECAST_COMPONENT_VALUE"/>
        <result property="match" column="COMPONENT_MATCH"/>
        <result property="unrestricted" column="UNRESTRICTED"/>
        <result property="stock" column="STOCK_IN_TFR"/>
        <result property="qual" column="QUAL_INSPECTION"/>
        <result property="restricted" column="RESTRICTED"/>
        <result property="blocked" column="BLOCKED"/>
        <result property="returns" column="RETURNS"/>
        <result property="isAvailable" column="COMPONENT_AVAILABLE"/>
    </resultMap>

    <resultMap id="StoredMaterialMap" type="com.ti.specteam.vswr.vswr.domain.MaterialInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="sequence" column="SEQUENCE"/>
        <result property="component" column="COMPONENT"/>
        <result property="travelerComponent" column="TRAVELER_COMPONENT"/>
        <result property="forecastedComponent" column="FORECASTED_COMPONENT"/>
        <result property="match" column="COMPONENT_MATCH"/>
        <result property="unrestricted" column="UNRESTRICTED"/>
        <result property="stock" column="STOCK_IN_TFR"/>
        <result property="qual" column="QUAL_INSPE"/>
        <result property="restricted" column="RESTRICTED"/>
        <result property="blocked" column="BLOCKED"/>
        <result property="returns" column="RETURNS"/>
        <result property="isAvailable" column="AVAILABLE"/>
    </resultMap>

    <resultMap id="GeneralInfoMap" type="com.ti.specteam.vswr.vswr.domain.GeneralInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="title" column="TITLE"/>
        <result property="vbuildID" column="VBUILD_ID"/>
        <result property="existingScswrID" column="EXISTING_SCSWR_ID"/>
        <result property="currentStatus" column="CURRENT_STATUS"/>
        <result property="currentRequestor" column="CURRENT_REQUESTOR"/>
        <result property="purpose" column="PURPOSE"/>
        <result property="plant" column="PLANT"/>
        <result property="facility" column="FACILITY"/>
        <result property="swrTypeCategory" column="SWR_TYPE_CATEGORY"/>
        <result property="swrType" column="SWR_TYPE"/>
        <result property="swrTypeFlag" column="SWR_TYPE_FLAG"/>
        <result property="priority" column="PRIORITY"/>
        <result property="specDevice" column="SPEC_DEVICE"/>
        <result property="purchaseOrder" column="PURCHASE_ORDER"/>
        <result property="lineItem" column="LINE_ITEM"/>
        <result property="io" column="IO"/>
        <result property="requestDate" column="REQUEST_DATE"/>
        <result property="groupEmail" column="PKG_GROUP_EMAIL"/>
        <result property="copyEmail" column="COPY_EMAIL"/>
        <result property="vbuildMaterial" column="VBUILD_MATERIAL"/>
        <result property="scswrMaterial" column="SCSWR_MATERIAL"/>
        <result property="scswrPlant" column="SCSWR_PLANT"/>
        <result property="scswrFacility" column="SCSWR_FACILITY"/>
        <result property="scswrSpecDevice" column="SCSWR_SPEC_DEVICE"/>
        <result property="pin" column="PIN"/>
        <result property="pkg" column="PKG"/>
        <result property="pkgGroup" column="PKG_GROUP"/>
        <result property="buildType" column="BUILD_TYPE"/>
    </resultMap>

    <resultMap id="RequestorInfoMap" type="com.ti.specteam.vswr.vswr.domain.RequestorInfo">
        <result property="itssID" column="REQUESTOR_USER_ID"/>
        <result property="name" column="REQUESTOR_USER_NAME"/>
        <result property="email" column="REQUESTOR_EMAIL"/>
        <result property="phone" column="REQUESTOR_PHONE"/>
        <result property="costCenter" column="REQUESTOR_COST_CENTER"/>
        <result property="group" column="REQUESTOR_GROUP"/>
    </resultMap>

    <resultMap id="DeviceInfoMap" type="com.ti.specteam.vswr.vswr.domain.DeviceInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="sapMaterial" column="SAP_MATERIAL"/>
        <result property="specDevice" column="SPEC_DEVICE"/>
        <result property="sapBaseMaterial" column="SAP_BASE_MATERIAL"/>
        <result property="sbe" column="SBE"/>
        <result property="sbe1" column="SBE1"/>
        <result property="sbe2" column="SBE2"/>
        <result property="industrySector" column="INDUSTRY_SECTOR"/>
        <result property="pin" column="PIN"/>
        <result property="pkg" column="PKG"/>
        <result property="pkgGroup" column="PKG_GROUP"/>
        <result property="profitCenter" column="WW_ID"/>
        <result property="buildQuantity" column="BUILD_QTY"/>
        <result property="apl" column="APL"/>
        <result property="iso" column="ISO"/>
        <result property="mcm" column="MCM"/>
        <result property="offloadInfo" column="OFFLOAD_NAME"/>
        <result property="oldMaterial" column="OLD_MATERIAL"/>
        <result property="pdbFacility" column="PDB_FACILITY"/>
        <result property="niche" column="NICHE"/>
    </resultMap>

    <resultMap id="AssemblyInfoMap" type="com.ti.specteam.vswr.vswr.domain.AssemblyInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="assemblyReq" column="ASSEMBLY_REQUIRED"/>
        <result property="header" column="HEADER"/>
        <result property="baseOutline" column="BASE_OUTLINE"/>
        <result property="wireDiameter" column="WIRE_DIAMETER"/>
        <result property="bondPadMetalization" column="BOND_PAD_METALIZATION"/>
        <result property="mbPath" column="MB_FILE_LOC"/>
        <result property="mbOrArcPath" column="MB_FILE_OTHER_LOC"/>
        <result property="forecastedFlag" column="FORECAST_FLAG"/>
    </resultMap>

    <resultMap id="PackingRequirementsMap" type="com.ti.specteam.vswr.vswr.domain.PackingRequirements">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="stickerType" column="STICKERTYPE"/>
        <result property="eWaiver" column="WAIVER_NUMBER"/>
        <result property="finishedGoodsDispo" column="DISPOSITION_PARTIAL_FG"/>
        <result property="waferSkeleton" column="WAFER_SKELETON_DISPOSITION"/>
        <result property="plantCode" column="WAFER_SKELETON_RETURN_TO"/>
        <result property="isRetestRMR" column="RMR_RETEST"/>
        <result property="pdcUnrestrictedSale" column="PDC_UNRESTRICTED_SALE"/>
    </resultMap>

    <resultMap id="DieInfoMap" type="com.ti.specteam.vswr.vswr.domain.DieInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="sequence" column="SEQUENCE"/>
        <result property="priority" column="PRIORITY"/>
        <result property="plant" column="PLANT"/>
        <result property="matlMasterDieName" column="MATL_MASTER_DIE_NAME"/>
        <result property="selected" column="SELECTED" javaType="boolean"
                typeHandler="org.apache.ibatis.type.BooleanTypeHandler"/>
        <result property="dieRev" column="DIE_REV"/>
        <result property="dieDesignator" column="DIE_DESIGNATOR"/>
        <result property="scribeWidth" column="SCRIBE_WIDTH"/>
        <result property="dieSize" column="DIE_SIZE"/>
        <result property="fabCode" column="FAB_CODE"/>
        <result property="fabTechnology" column="FAB_TECHNOLOGY"/>
        <result property="waferDiameter" column="WAFER_DIAMETER"/>
        <result property="waferThickness" column="WAFER_THICKNESS"/>
        <result property="backgrindThickness" column="BACKGRIND_THICKNESS"/>
        <collection property="dieLots" ofType="com.ti.specteam.vswr.vswr.domain.DieLotInfo">
            <result property="vswrID" column="SWR_ID_DL"/>
            <result property="matlMasterDieName" column="MATL_MASTER_DIE_NAME_DL"/>
            <result property="dieLot" column="DIE_LOT"/>
            <result property="waferNumToUse" column="WAFER_NUMBER_TO_USE"/>
            <result property="probed" column="PROBED"/>
            <result property="isInkless" column="INKLESS"/>
            <result property="buildBy" column="INKLESS_BUILD_BY"/>
            <result property="stdMapLocation" column="MAP_LOCATION"/>
            <result property="pickupBin" column="BIN_NUM"/>
            <result property="useGecs" column="USE_GEC"/>
            <result property="plant" column="PLANT_DL"/>
            <result property="matShipStatus" column="INCOMING_MATL_SHIP_STATUS"/>
            <result property="dateShipped" column="INCOMING_MATL_SHIP_DATE"/>
            <result property="deliveryNote" column="INCOMING_MATL_DELIVERY_NOTE"/>
            <result property="invoice" column="INCOMING_MATL_INVOICE"/>
            <result property="qtyToShip" column="INCOMING_MATL_SHIP_QTY"/>
            <result property="location" column="LOCATION"/>
            <result property="sapWaybill" column="INCOMING_MATL_WAYBILL"/>
        </collection>
    </resultMap>

    <resultMap id="CommentsMap" type="com.ti.specteam.vswr.vswr.domain.Comment">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="operation" column="OPERATION"/>
        <result property="userID" column="USER_ID"/>
        <result property="userName" column="USER_NAME"/>
        <result property="dttm" column="DTTM"/>
        <result property="comment" column="COMMENT"/>
    </resultMap>

    <resultMap id="ShippingInfoMap" type="com.ti.specteam.vswr.vswr.domain.ShippingInfo">
        <result property="vswrID" column="VSWR_ID"/>
        <result property="attention" column="ATTENTION"/>
        <result property="mailStation" column="MAIL_STATION"/>
        <result property="plant" column="PLANT"/>
        <result property="address" column="ADDRESS"/>
        <result property="quantity" column="QUANTITY"/>
        <result property="stateOfFinish" column="STATE_OF_FINISH"/>
        <result property="shipDeviceName" column="SHIP_DEVICE_NAME"/>
    </resultMap>

    <resultMap id="DieLotStatusMap" type="com.ti.specteam.vswr.vswr.domain.DieLotStatus">
        <result property="material" column="MATERIAL"/>
        <result property="batch" column="BATCH"/>
        <result property="plant" column="PLANT"/>
        <result property="unrestricted" column="UNRESTRICTED"/>
        <result property="stock" column="STOCK_IN_TFR"/>
        <result property="qualInspection" column="QUAL_INSPECTION"/>
        <result property="restricted" column="RESTRICTED"/>
        <result property="blocked" column="BLOCKED"/>
        <result property="returns" column="RETURN"/>
    </resultMap>

    <resultMap id="IntransitStatusMap" type="com.ti.specteam.vswr.vswr.domain.IntransitStatus">
        <result property="shippingPlant" column="SHIPPING_PLANT"/>
        <result property="shippingFacility" column="SHIPPING_FACILITY"/>
        <result property="receivingPlant" column="RECEIVING_PLANT"/>
        <result property="receivingFacility" column="RECEIVING_FACILITY"/>
        <result property="material" column="MATERIAL"/>
        <result property="batch" column="BATCH"/>
        <result property="poNumber" column="PO_NUMBER"/>
        <result property="poLineItem" column="PO_LINE_ITEM"/>
        <result property="scheduleLine" column="SCHEDULE_LINE"/>
        <result property="waybill" column="WAYBILL"/>
        <result property="deliveryNumber" column="DELIVERY_NUMBER"/>
        <result property="deliveryItem" column="DELIVERY_ITEM"/>
        <result property="receiptDate" column="RECEIPT_DATE"/>
        <result property="shipDate" column="SHIP_DATE"/>
        <result property="qty" column="QTY"/>
        <result property="waferQty" column="WAFER_QTY"/>
    </resultMap>





    <select id="fetchSwrTypeOptions" resultType="HashMap">
        SELECT DISTINCT
            SWR_TYPE_CATEGORY "swrTypeCategory",
            SWR_TYPE "swrType"
        FROM VSWR_SWR_TYPE
        WHERE VYPER_BUILD_TYPE = #{vBuildType}
        ORDER BY SWR_TYPE_CATEGORY
    </select>

    <select id="fetchPriorityOptions" resultType="HashMap">
        SELECT
            FIELD_LABEL "label",
            FIELD_VALUE "value"
        FROM VSWR_PRIORITY_VW
    </select>
    
    <select id="fetchOffloadInfoOptions" resultType="HashMap">
        SELECT
            FIELD_LABEL "label",
            FIELD_VALUE "value"
        FROM VSWR_OFFLOAD_VW
    </select>
    
    <select id="fetchFinishedGoodOptions" resultType="HashMap">
        SELECT
            FIELD_LABEL "label",
            FIELD_VALUE "value"
        FROM VSWR_DISPOSITION_PARTIAL_FG_VW
    </select>

    <select id="fetchWaferSkeletonOptions" resultType="HashMap">
        SELECT
            FIELD_LABEL "label",
            FIELD_VALUE "value"
        FROM VSWR_WAFER_SKELETON_DISPOSITION_VW
    </select>

    <select id="fetchShippToPlantOptions" resultType="HashMap">
        SELECT 
            PLANT "plant",
            PLANT_DESC "plantDesc",
            PLANT_ADDRESS "plantAddress"
        FROM VSWR_SHIP_TO_PLANT_VW
    </select>

    <select id="fetchDieLocationOptions" resultType="HashMap">
        SELECT
            FIELD_LABEL "label",
            FIELD_VALUE "value"
        FROM VSWR_DIE_LOCATION_VW
    </select>

    <select id="fetchStateOfFinishOptions" resultType="HashMap">
        SELECT
            FIELD_LABEL "label",
            FIELD_VALUE "value"
        FROM VSWR_STATE_OF_FINISH_VW
    </select>



    <select id="fetchPkgGroupEmail" resultType="String">
        SELECT EMAIL FROM VSWR_NOTIFY
        WHERE PLANT = '1540' 
            AND CATEGORY = 'PKG_GROUP'
            AND NOTIFY_KEY = #{pkg}
    </select>

    <select id="fetchForecastedInfo" resultMap="ForecastedMap">
        SELECT
            SWR_ID,
            TITLE,
            PURPOSE,
            COMMENT_REQ,
            REQUESTOR_NAME,
            MATERIAL,
            CURRENT_STATUS,
            SBE1SITE,
            FORECASTED_FLAG,
            PURCHASE_ORDER,
            LINE_ITEM,
            IO_NUMBER,
            LEADFRAME,
            MOLD_COMPOUND,
            MOUNT_COMPOUND,
            WIRE,
            SOLDERBALL,
            CHIP_CAPACITOR,
            LID,
            CANISTER,
            DIE_BOAT,
            FRAME,
            PACK_FILLER_JEWEL_BOX,
            SHIP_TUBE,
            WAFER_VIAL,
            TRAY,
            REEL_TAPE_AND_REEL,
            TAPE_TAPE_AND_REEL,
            TAPE_COVER
        FROM VSWR_FORECAST_VW
        WHERE SWR_ID = #{forecastID}
    </select>
    
    <select id="fetchBomComponents" resultType="String">
        SELECT COMPONENT_NAME FROM VSWR_FORECAST_BOM_COMPONENT_VW
    </select>

    <select id="fetchPackComponents" resultType="String">
        SELECT COMPONENT_NAME FROM VSWR_PACK_COMPONENT_VW
    </select>

    <select id="fetchMaterialInfo" resultMap="MaterialMap">
        select
            traveler.COMPONENT_NAME,
            row_number() over (partition by traveler.COMPONENT_NAME order by traveler.COMPONENT_NAME) SEQUENCE,
            traveler.TRAVELER_COMPONENT_VALUE,
            mchb.MATERIAL,
            mchb.PLANT,
            sum(mchb.UNRESTRICTED) UNRESTRICTED,
            sum(mchb.STOCK_IN_TFR) STOCK_IN_TFR,
            sum(mchb.IN_QUAL_INSP_1) QUAL_INSPECTION,
            sum(mchb.RESTRICTED_USE) RESTRICTED,
            sum(mchb.BLOCKED) BLOCKED,
            sum(mchb.RETURNS) RETURNS,
            case
                when sum(mchb.UNRESTRICTED) != 0 or sum(mchb.RESTRICTED_USE) != 0 then
                'YES'
                else
                'NO'
            end COMPONENT_AVAILABLE
        FROM <EMAIL> mchb
        RIGHT JOIN (
                <foreach item="component" collection="components" separator="UNION">
                    SELECT
                        #{component.value} TRAVELER_COMPONENT_VALUE,
                        #{component.name} COMPONENT_NAME
                    FROM DUAL
                </foreach>
        ) traveler
        ON traveler.TRAVELER_COMPONENT_VALUE = mchb.MATERIAL
        WHERE NVL(MCHB.PLANT, #{plant}) = #{plant}
        GROUP BY
            traveler.COMPONENT_NAME,
            traveler.TRAVELER_COMPONENT_VALUE,
            mchb.MATERIAL,
            mchb.PLANT
    </select>
    
    <select id="fetchMaterialInfoNotInPlant" resultMap="MaterialMap">
        select
            traveler.COMPONENT_NAME,
            row_number() over (partition by traveler.COMPONENT_NAME order by traveler.COMPONENT_NAME) SEQUENCE,
            traveler.TRAVELER_COMPONENT_VALUE,
            mchb.MATERIAL,
            mchb.PLANT,
            sum(mchb.UNRESTRICTED) UNRESTRICTED,
            sum(mchb.STOCK_IN_TFR) STOCK_IN_TFR,
            sum(mchb.IN_QUAL_INSP_1) QUAL_INSPECTION,
            sum(mchb.RESTRICTED_USE) RESTRICTED,
            sum(mchb.BLOCKED) BLOCKED,
            sum(mchb.RETURNS) RETURNS,
            case
                when (sum(mchb.UNRESTRICTED) != 0 or sum(mchb.RESTRICTED_USE) != 0) and NVL(MCHB.PLANT, #{plant}) = #{plant} then
                'YES'
                else
                'NO'
            end COMPONENT_AVAILABLE
        FROM <EMAIL> mchb
        RIGHT JOIN (
                <foreach item="component" collection="components" separator="UNION">
                    SELECT
                        #{component.value} TRAVELER_COMPONENT_VALUE,
                        #{component.name} COMPONENT_NAME
                    FROM DUAL
                </foreach>
        ) traveler
        ON traveler.TRAVELER_COMPONENT_VALUE = mchb.MATERIAL
        GROUP BY
            traveler.COMPONENT_NAME,
            traveler.TRAVELER_COMPONENT_VALUE,
            mchb.MATERIAL,
            mchb.PLANT
    </select>

    <select id="fetchGeneralInfo" resultMap="GeneralInfoMap">
        SELECT 
            VSWR_ID,
            TITLE,
            VBUILD_ID,
            EXISTING_SCSWR_ID,
            CURRENT_STATUS,
            CURRENT_REQUESTOR,
            PURPOSE,
            PLANT,
            FACILITY,
            SWR_TYPE_CATEGORY,
            SWR_TYPE,
            SWR_TYPE_FLAG,
            PRIORITY,
            SPEC_DEVICE,
            PURCHASE_ORDER,
            LINE_ITEM,
            IO,
            SYSDATE REQUEST_DATE,
            PKG_GROUP_EMAIL,
            COPY_EMAIL,
            VBUILD_MATERIAL,
            SCSWR_MATERIAL,
            SCSWR_PLANT,
            SCSWR_FACILITY,
            SCSWR_SPEC_DEVICE,
            PIN,
            PKG,
            PKG_GROUP,
            BUILD_TYPE
        FROM VSWR_GENERAL_INFO
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchRequestorInfo" resultMap="RequestorInfoMap">
        SELECT 
            REQUESTOR_USER_ID,
            REQUESTOR_USER_NAME,
            REQUESTOR_EMAIL,
            REQUESTOR_PHONE,
            REQUESTOR_COST_CENTER,
            REQUESTOR_GROUP
        FROM VSWR_GENERAL_INFO
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchDeviceInfo" resultMap="DeviceInfoMap">
        SELECT 
            VSWR_ID,
            SAP_MATERIAL,
            SPEC_DEVICE,
            SAP_BASE_MATERIAL,
            SBE,
            SBE1,
            SBE2,
            INDUSTRY_SECTOR,
            PIN,
            PKG,
            PKG_GROUP,
            WW_ID,
            BUILD_QTY,
            APL,
            ISO,
            MCM,
            OFFLOAD_NAME,
            OLD_MATERIAL,
            PDB_FACILITY,
            NICHE
        FROM VSWR_DEVICE
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchAssemblyInfo" resultMap="AssemblyInfoMap">
        SELECT
            VSWR_ID,
            ASSEMBLY_REQUIRED,
            HEADER,
            BASE_OUTLINE,
            WIRE_DIAMETER,
            BOND_PAD_METALIZATION,
            MB_FILE_LOC,
            MB_FILE_OTHER_LOC,
            FORECAST_FLAG
        FROM VSWR_ASSEMBLY
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchBomInfo" resultMap="StoredMaterialMap">
        SELECT
            VSWR_ID,
            SEQUENCE,
            COMPONENT,
            TRAVELER_COMPONENT,
            UNRESTRICTED,
            STOCK_IN_TFR,
            QUAL_INSPE,
            RESTRICTED,
            BLOCKED,
            RETURNS,
            AVAILABLE,
            FORECASTED_COMPONENT
        FROM VSWR_BOM
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchPackingRequirements" resultMap="PackingRequirementsMap">
        SELECT
            VSWR_ID,
            STICKERTYPE,
            WAIVER_NUMBER,
            DISPOSITION_PARTIAL_FG,
            WAFER_SKELETON_DISPOSITION,
            WAFER_SKELETON_RETURN_TO,
            RMR_RETEST,
            PDC_UNRESTRICTED_SALE
        FROM VSWR_PACKING_REQUIREMENTS
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchPackingMaterials" resultMap="StoredMaterialMap">
        SELECT 
            VSWR_ID,
            SEQUENCE,
            COMPONENT,
            TRAVELER_COMPONENT,
            UNRESTRICTED,
            STOCK_IN_TFR,
            QUAL_INSPE,
            RESTRICTED,
            BLOCKED,
            RETURNS,
            AVAILABLE
        FROM VSWR_PACKING_MATERIAL
        WHERE VSWR_ID = #{vswrID}
    </select>

    <select id="fetchDieInfo" resultMap="DieInfoMap">
        SELECT
            d.VSWR_ID,
            d.SEQUENCE,
            d.PRIORITY,
            d.PLANT,
            d.MATL_MASTER_DIE_NAME,
            d.SELECTED,
            d.DIE_REV,
            d.DIE_DESIGNATOR,
            d.DIE_SIZE,
            d.SCRIBE_WIDTH,
            d.FAB_CODE,
            d.FAB_TECHNOLOGY,
            d.WAFER_DIAMETER,
            d.WAFER_THICKNESS,
            d.BACKGRIND_THICKNESS,
            dl.DIE_LOT DIE_LOT_DL,
            dl.MATL_MASTER_DIE_NAME MATL_MASTER_DIE_NAME_DL,
            dl.DIE_LOT,
            dl.WAFER_NUMBER_TO_USE,
            dl.PROBED,
            dl.INKLESS,
            dl.INKLESS_BUILD_BY,
            dl.MAP_REQUIRED,
            dl.MAP_LOCATION,
            dl.BIN_NUM,
            dl.USE_GEC,
            dl.PLANT PLANT_DL,
            dl.INCOMING_MATL_SHIP_STATUS,
            dl.INCOMING_MATL_SHIP_DATE,
            dl.INCOMING_MATL_DELIVERY_NOTE,
            dl.INCOMING_MATL_INVOICE,
            dl.INCOMING_MATL_SHIP_QTY,
            dl.LOCATION,
            dl.INCOMING_MATL_WAYBILL
        FROM VSWR_DIE d
        LEFT JOIN VSWR_DIE_LOT dl
        ON 
            d.VSWR_ID = dl.VSWR_ID
            AND d.MATL_MASTER_DIE_NAME = dl.MATL_MASTER_DIE_NAME
        WHERE d.VSWR_ID = #{vswrID}
    </select>

    <select id="fetchComments" resultMap="CommentsMap">
        SELECT
            VSWR_ID,
            OPERATION,
            USER_ID,
            USER_NAME,
            DTTM,
            "COMMENT"
        FROM VSWR_COMMENTS
        WHERE VSWR_ID = #{vswrID}
        ORDER BY DTTM DESC
    </select>

    <select id="fetchShippingInfo" resultMap="ShippingInfoMap">
        SELECT
            VSWR_ID,
            ATTENTION,
            MAIL_STATION,
            PLANT,
            ADDRESS,
            QUANTITY,
            STATE_OF_FINISH,
            SHIP_DEVICE_NAME
        FROM VSWR_SHIP
        WHERE VSWR_ID = #{vswrID}

    </select>
    
    <select id="fetchDieLotStatus" resultMap="DieLotStatusMap">
        SELECT
            MATERIAL,
            BATCH,
            PLANT,
            SUM(UNRESTRICTED) UNRESTRICTED,
            SUM(STOCK_IN_TFR) STOCK_IN_TFR,
            SUM(IN_QUAL_INSP_1) QUAL_INSPECTION,
            SUM(RESTRICTED_USE) RESTRICTED,
            SUM(BLOCKED) BLOCKED,
            SUM(RETURNS) RETURN
        FROM <EMAIL>
        WHERE MATERIAL = #{material}
            <choose>
                <when test="inPlant">
                    AND PLANT = #{plant}
                </when>
                <otherwise>
                    AND PLANT != #{plant}
                </otherwise>
            </choose>
        GROUP BY MATERIAL, BATCH, PLANT
    </select>

    <select id="fetchDieLotIntransitStatus" resultMap="IntransitStatusMap">
        SELECT
            SHIPPING_PLANT,
            SHIPPING_FACILITY,
            RECEIVING_PLANT,
            RECEIVING_FACILITY,
            MATERIAL,
            BATCH,
            PO_NUMBER,
            PO_LINE_ITEM,
            SCHEDULE_LINE,
            WAYBILL,
            DELIVERY_NUMBER,
            DELIVERY_ITEM,
            RECEIPT_DATE,
            SHIP_DATE,
            QTY,
            WAFER_QTY
        FROM VSWR_INTRANSIT_MATERIAL_MV
        WHERE
            RECEIVING_PLANT=#{plant}
            AND MATERIAL=#{material}
    </select>
</mapper>












