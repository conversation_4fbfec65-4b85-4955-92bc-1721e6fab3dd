package com.ti.specteam.vswr.vswr.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

import com.ti.specteam.vswr.vswr.domain.DeviceInfo;
import com.ti.specteam.vswr.vswr.domain.DieInfo;
import com.ti.specteam.vswr.vswr.domain.GeneralInfo;
import com.ti.specteam.vswr.vswr.domain.MaterialInfo;
import com.ti.specteam.vswr.vswr.repository.ATSWRDao;
import com.ti.specteam.vswr.vswr.repository.BDWDao;
import com.ti.specteam.vswr.vswr.service.FetchVyperService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.MaterialExtras;
import com.ti.specteam.vyper.build.model.MultiBuild;
import com.ti.specteam.vyper.build.model.Traveler;
import com.ti.specteam.vyper.build.model.TravelerAttribute;
import com.ti.specteam.vyper.build.model.TravelerComponent;
import com.ti.specteam.vyper.build.model.TravelerOperation;
import com.ti.specteam.vyper.entity.build.BuildEntity;
import com.ti.specteam.vyper.entity.build.BuildEntityService;


@Slf4j
@Service
public class FetchVyperServiceImpl implements FetchVyperService {
    final ObjectMapper mapper = new ObjectMapper();
    
    @Autowired
    private ATSWRDao atswrDao;

    @Autowired
    private BDWDao bdwDao;
    
    @Autowired
    private BuildEntityService buildEntityService;


    private Build fetchVyperBuild(String vbuildID){
        BuildEntity buildEntity = buildEntityService.findByBuildNumber(vbuildID);
        return buildEntityService.fromJson(buildEntity.getJson());
    }

    public HashMap<String, Object> fetchData(String vbuildID, List<String> attributesToFetch){
        log.info("fetchData called from FetchVyperServiceImpl");
        
        Build vyperBuild;
        HashMap<String, Object> swr = new HashMap<String, Object>();
        
        try{
            vyperBuild = fetchVyperBuild(vbuildID);
        } catch(Exception e){
            log.debug(e.toString());
            swr.put("validVBuild", false);
            return swr;
        }
        
        swr.put("state", vyperBuild.getState());
        swr.put("validVBuild", true);
        
        if(attributesToFetch.contains("generalInfo")){
            swr.put("generalInfo", getGeneralInfo(vyperBuild));
        }
        if(attributesToFetch.contains("deviceInfo")){
            swr.put("deviceInfo", getDeviceInfo(vyperBuild));
        }
        if(attributesToFetch.contains("dieInfo")){
            swr.put("dieInfo", getDieInfo(vyperBuild));
        }
        if(attributesToFetch.contains("bomData")){
            swr.put("bomData", getMaterialData(vyperBuild, atswrDao.fetchBomComponents()));
        }
        if(attributesToFetch.contains("packingMaterialData")){
            swr.put("packingMaterialData", getMaterialData(vyperBuild, atswrDao.fetchPackComponents()));
        }
        if(attributesToFetch.contains("symbolData")){
            swr.put("symbolData", getSymbolData(vyperBuild));
        }
        if(attributesToFetch.contains("traveler")){
            swr.put("traveler", vyperBuild.getTraveler());
        }

        return swr;
    }

    private List<TravelerComponent> getSymbolData(Build vyperBuild){
        List<TravelerOperation> operations = vyperBuild.getTraveler().getOperations();

        for(TravelerOperation operation : operations){
            if("Symbol".equals(operation.getName())){
                return operation.getComponents();
            }
        }

        return new ArrayList<TravelerComponent>();
    }

    private List<MaterialInfo> getMaterialData(Build vyperBuild, List<String> materialComponents){
        List<TravelerOperation> operations = vyperBuild.getTraveler().getOperations();
        List<HashMap<String, String>> commonComponents = new ArrayList<HashMap<String, String>>();
        int plant = (Integer) vyperBuild.getFacility().getObject().get("PlantCode");

        for(TravelerOperation operation : operations){
            for(TravelerComponent component : operation.getComponents()){
                if(materialComponents.contains(component.getName())){
                    HashMap<String, String> componentInfo = new HashMap<String, String>();
                    String value = component.getValue();
                    value = value == null ? "" : value;
                    componentInfo.put("name", component.getName());
                    componentInfo.put("value", value);
                    commonComponents.add(componentInfo);
                }
            }
        }

        if(commonComponents.isEmpty()){
            return new ArrayList<MaterialInfo>();
        }
        
        List<MaterialInfo> lMaterialData = atswrDao.fetchMaterialInfo(commonComponents, plant);
        
        //Materials not available in the plant needs to be displayed for review
        List<HashMap<String, String>> commonCompNotInPlant = new ArrayList<HashMap<String, String>>();
        for(HashMap comp : commonComponents){
            boolean bIsCompMissing = false;
            for (MaterialInfo matInfo : lMaterialData) {
                if (matInfo.getComponent().equals(comp.get("name"))) {
                    bIsCompMissing = false;
                    break;
                }
                else
                    bIsCompMissing = true;               
            }    
            if (bIsCompMissing)
                commonCompNotInPlant.add(comp);
        } 
        if(!commonCompNotInPlant.isEmpty()){
            List<MaterialInfo> lMaterialDataNotInPlant = atswrDao.fetchMaterialInfoNotInPlant(commonCompNotInPlant, plant);
            for (MaterialInfo matInfo : lMaterialDataNotInPlant) {
                matInfo.setVswrID(null);
                matInfo.setMaterial(null);
                matInfo.setPlant(null);
                matInfo.setForecastedComponent(null);
                matInfo.setMatch(null);
                matInfo.setUnrestricted(null);
                matInfo.setStock(null);
                matInfo.setQual(null);
                matInfo.setRestricted(null);
                matInfo.setBlocked(null);
                matInfo.setReturns(null);           
            }
            lMaterialData.addAll(lMaterialDataNotInPlant);
        }

        return lMaterialData;
    }

    private GeneralInfo getGeneralInfo(Build vyperBuild){
        log.info("getGeneralInfo called from FetchVyperServiceImpl");
        GeneralInfo generalInfo = new GeneralInfo();
        String pkg = vyperBuild.getMaterial().getObject().get("PackageGroup").toString();
        String groupEmail = atswrDao.fetchPkgGroupEmail(pkg);
        Map<String, Object> materialObject = vyperBuild.getMaterial().getObject();
        Map<String, Object> facilityObject = vyperBuild.getFacility().getObject();
        MultiBuild multiBuild = vyperBuild.getMultiBuild();
        String oldMaterial = materialObject.get("OldMaterial").toString();

        generalInfo.setSpecDevice(multiBuild.isMultiBuild() ? multiBuild.getSpecDevice() : oldMaterial);
        generalInfo.setPin(materialObject.get("PackagePin").toString());
        generalInfo.setPkg(materialObject.get("PackageDesignator").toString());
        generalInfo.setPkgGroup(materialObject.get("PackageGroup").toString());
        generalInfo.setVbuildMaterial(materialObject.get("Material").toString());
        generalInfo.setVbuildID(vyperBuild.getBuildNumber());
        generalInfo.setBuildType(vyperBuild.getBuildtype());
        generalInfo.setPlant(facilityObject.get("PlantCode").toString());
        generalInfo.setFacility(facilityObject.get("PDBFacility").toString());
        generalInfo.setGroupEmail(groupEmail);
        generalInfo.setScswrcontrolnumber(vyperBuild.getScswrcontrolnumber());

        return generalInfo;
    } 

    private DeviceInfo getDeviceInfo(Build vyperBuild){
        log.info("getDeviceInfo called from FetchVyperServiceImpl");
        
        Map<String, Object> facilityObject = vyperBuild.getFacility().getObject();
        Map<String, Object> materialObject = vyperBuild.getMaterial().getObject();
        MaterialExtras materialExtras = vyperBuild.getMaterialExtras();
        MultiBuild multiBuild = vyperBuild.getMultiBuild();
        String oldMaterial = materialObject.get("OldMaterial").toString();

        DeviceInfo deviceInfo = mapper.convertValue(materialObject, DeviceInfo.class);

        deviceInfo.setSpecDevice(multiBuild.isMultiBuild() ? multiBuild.getSpecDevice() : oldMaterial);
        deviceInfo.setSapBaseMaterial(materialExtras.getBasicMaterial());
        deviceInfo.setIndustrySector(materialExtras.getIndustrySector());
        deviceInfo.setNiche(materialExtras.getNiche());
        deviceInfo.setPdbFacility(facilityObject.get("PDBFacility").toString());
        deviceInfo.setProfitCenter(bdwDao.fetchWwid(oldMaterial));
        deviceInfo.setMcm(bdwDao.fetchMcm(oldMaterial));
        deviceInfo.setIso(bdwDao.fetchIso(oldMaterial));
        //temporarily disabled due to timeout issues
        // deviceInfo.setApl(bdwDao.fetchApl(oldMaterial));
        
        return deviceInfo;
    }

    private List<DieInfo> getDieInfo(Build vyperBuild){
        log.info("getDieInfo called from FetchVyperServiceImpl");
        
        List<DieInfo> dieInfoList = new ArrayList<DieInfo>();

        int setByPriority = Integer.MAX_VALUE;
        List<TravelerComponent> components = new ArrayList<TravelerComponent>();

        //prioritizes die prep, then wafer issue, then mount
        Traveler traveler = vyperBuild.getTraveler();
        for(TravelerOperation operation : traveler.getOperations()){
            String operationName = operation.getName();

            if("Die Prep".equalsIgnoreCase(operationName)){
                components = operation.getComponents();
                setByPriority = 0;
                break;
            }
            else if(setByPriority > 1 && "Wafer Issue".equalsIgnoreCase(operationName)){
                components = operation.getComponents();
                setByPriority = 1;
            }
            else if(setByPriority > 2 && "Mount".equalsIgnoreCase(operationName)){
                components = operation.getComponents();
                setByPriority = 2;
            }
        }

        List<TravelerComponent> dieComponents = new ArrayList<TravelerComponent>();
        for(TravelerComponent component : components){
            String componentName = component.getName();
            
            if("Die".equalsIgnoreCase(componentName)){
                dieComponents.add(component);
            }
        }

        for(int i = 0; i < dieComponents.size(); i++){
            TravelerComponent dieComponent = dieComponents.get(i);

            HashMap<String, String> newAttributes = new HashMap<String, String>();
            
            for(TravelerAttribute attribute : dieComponent.getAttributes()){
                newAttributes.put(attribute.getName(), attribute.getValue());
            }
            
            DieInfo dieInfo = mapper.convertValue(newAttributes, DieInfo.class);
            dieInfo.setMatlMasterDieName(dieComponent.getValue());

            if(dieInfo.getScribeWidth() == null){
                dieInfo.setScribeWidth(dieInfo.getXScribeWidth() + " " + dieInfo.getYScribeWidth());
            }else{
                dieInfo.setScribeWidth(dieInfo.getScribeWidth()+ " UM");
            }

            String dieSize = dieInfo.getXDieSize() + dieInfo.getYDieSize();
            String pattern = "\\([0-9]*\\.[0-9]*MILS\\)";
            dieSize = dieSize.replaceAll(pattern, "");
            
            dieInfo.setDieSize(dieSize);
            dieInfo.setSequence(i+1);
            dieInfo.setPriority(i+1);
            dieInfo.setPlant(vyperBuild.getFacility().getObject().get("PlantCode").toString());

            dieInfoList.add(dieInfo);
        }

        return dieInfoList;
    }
}
