package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.entity.vyper.VyperEntity;
import com.ti.specteam.vyper.entity.vyper.VyperEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CreateVyperAndCopyBuildAction {

    private final AddBuildCopyAction addBuildCopyAction;
    private final VyperEntityService vyperEntityService;
    private final BuildService buildService;
    private final VyperService vyperService;

    // we're creating a new vyper based on an existing build
    // there is nothing to validate
    public Vyper execute(CreateVyperAndCopyBuildForm createVyperAndCopyBuildForm) {

        // create new vyper entity
        VyperEntity vyperEntity = vyperEntityService.create();

        // get new Vyper
        Vyper vyper = vyperEntityService.fromJson(vyperEntity.getJson());

        // get the source build
        Build srcBuild = buildService.fetchBuild(createVyperAndCopyBuildForm.getSrcBuildNumber());

        // copy the build to the new vyper
        Build build = addBuildCopyAction.execute(
                vyper,
                createVyperAndCopyBuildForm.getSrcBuildNumber(),
                srcBuild.getMaterial().getMaterial(),
                srcBuild.getFacility().getPdbFacility(),
                createVyperAndCopyBuildForm.getBuildtype(),
                createVyperAndCopyBuildForm.getDescription(),
                srcBuild.getBuildFlowName(),
                srcBuild.getBuildFlow().getSymbolChoice(),
                srcBuild.getTemplateSource());

        buildService.saveBuild(build);

        return vyperService.saveVyper(vyper);
    }

}
