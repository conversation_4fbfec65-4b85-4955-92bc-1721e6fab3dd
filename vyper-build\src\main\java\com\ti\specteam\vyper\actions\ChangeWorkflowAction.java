package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.audit.AuditService;
import com.ti.specteam.vyper.build.SelectionsSnapshotService;
import com.ti.specteam.vyper.build.model.Build;
import com.ti.specteam.vyper.build.model.BuildState;
import com.ti.specteam.vyper.build.model.User;
import com.ti.specteam.vyper.build.model.Vyper;
import com.ti.specteam.vyper.build.util.FinalApproveService;
import com.ti.specteam.vyper.build.vyper.BuildService;
import com.ti.specteam.vyper.build.vyper.VyperService;
import com.ti.specteam.vyper.core.exception.VyperException;
import com.ti.specteam.vyper.core.exception.VyperInvalidActionException;
import com.ti.specteam.vyper.email.BuildStateEmailService;
import com.ti.specteam.vyper.security.SecurityService;
import com.ti.specteam.vyper.taskService.TaskServiceService;
import com.ti.specteam.vyper.validate.ValidateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.time.OffsetDateTime;

import static com.ti.specteam.vyper.audit.AuditActivity.CHANGE_WORKFLOW;

/**
 * <AUTHOR> Woods
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeWorkflowAction {

    private final VyperService vyperService;
    private final BuildService buildService;
    private final ValidateService validateService;
    private final SecurityService securityService;
    private final SelectionsSnapshotService selectionsSnapshotService;
    private final AuditService auditService;
    private final BuildStateEmailService buildStateEmailService;
    private final FinalApproveService finalApproveService;
    private final TaskServiceService taskServiceService;

    public Build execute(ChangeWorkflowForm changeWorkflowForm) throws MessagingException {
        log.debug("execute(workflowForm:{})", changeWorkflowForm);

        Vyper vyper = vyperService.fetchVyper(changeWorkflowForm);
        Build build = buildService.fetchBuild(changeWorkflowForm);

        User user = new User();
        user.setUsername(securityService.user().getUsername());
        user.setUserid(securityService.user().getUserid());

        switch (changeWorkflowForm.getAction().toLowerCase()) {
            case "test": // temporary - for testing purposes - moves a build back to new state
                log.debug("workflow - test");
                build.setState(BuildState.DRAFT);
                build.getSubmitter().setUser(user);
                build.getSubmitter().setWhen(OffsetDateTime.now());
                break;

            case "delete":
                log.debug("workflow - delete");
                validateService.checkOpen(vyper);
                validateService.checkOwner(vyper);
                validateService.checkState(vyper, build, BuildState.DRAFT);
                buildService.delete(build);

                // create the audit record
                auditService.createVyper(
                        vyper.getVyperNumber(),
                        CHANGE_WORKFLOW,
                        "workflow action: " + changeWorkflowForm.getAction() + " " + build.getBuildNumber()
                );

                // even-though we deleted it, return it.
                return build;

            case "submit":
                log.debug("workflow - submit");
                validateService.checkOpen(vyper);
                validateService.checkOwner(vyper);
                validateService.checkState(vyper, build, BuildState.DRAFT, BuildState.REWORK);
                build.setState(BuildState.AT_REVIEW_CHANGE);
                build.getSubmitter().setUser(user);
                build.getSubmitter().setWhen(OffsetDateTime.now());
                selectionsSnapshotService.create(build);
                buildStateEmailService.sendSubmitEmail(vyper, build);
                break;

            case "bu approve":
                log.debug("workflow - bu approve");
                validateService.checkOpen(vyper);
                validateService.checkOwner(vyper);
                validateService.checkState(vyper, build, BuildState.BU_REVIEW_CHANGE);
                build.setState(BuildState.FINAL_APPROVED);
                build.getBuApprover().setUser(user);
                build.getBuApprover().setWhen(OffsetDateTime.now());
                buildStateEmailService.sendBuFinalApproveEmail(vyper, build, BuildState.FINAL_APPROVED);
                break;

            case "rework":
                log.debug("workflow - rework");
                validateService.checkOpen(vyper);
                validateService.checkOwnerOrAt(vyper, build);
                if (build.getState() == BuildState.BU_REVIEW_CHANGE) {
                    log.debug("Current state is {} and changing to at review", build.getState());
                    build.setState(BuildState.AT_REVIEW_CHANGE);
                    buildStateEmailService.sendBuReworkEmail(vyper, build);
                } else {
                    log.debug("Current state is {} and changing to rework", build.getState());
                    build.setState(BuildState.REWORK);
                    copyReworkTraveler(build);
                    buildStateEmailService.sendAtReworkEmail(vyper, build, changeWorkflowForm);
                }
                break;

            case "bu rework":
                log.debug("workflow - bu rework");
                validateService.checkOpen(vyper);
                validateService.checkOwner(vyper);
                log.debug("Current state is {} and changing to rework", build.getState());
                build.setState(BuildState.REWORK);
                copyReworkTraveler(build);
                buildStateEmailService.sendAtReworkEmail(vyper, build, changeWorkflowForm);
                break;

            case "restore":
                log.debug("workflow - restore");
                validateService.checkOpen(vyper);
                validateService.checkOwner(vyper);
                validateService.checkState(vyper, build, BuildState.CANCELED);
                build.setState(BuildState.REWORK);
                break;

            case "cancel":
                log.debug("workflow - cancel");
                validateService.checkOpen(vyper);
                validateService.checkOwner(vyper);
                validateService.checkState(vyper, build, BuildState.REWORK, BuildState.DRAFT, BuildState.BU_REVIEW_CHANGE);
                build.setState(BuildState.CANCELED);
                break;

            default:
                log.debug("workflow - unhandled action:{}", changeWorkflowForm.getAction());
                throw new VyperInvalidActionException(changeWorkflowForm.getAction());
        }

        // create the audit record
        auditService.createBuild(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_WORKFLOW,
                "workflow action: " + changeWorkflowForm.getAction()
        );

        return buildService.saveBuild(build);
    }

    // called when an approver rejects, or all approver groups have approved.
    public Vyper atApproverCallBack(String buildNumber, String status) {
        log.debug("atApproverCallBack(buildNumber: {}, status: {})", buildNumber, status);

        String vyperNumber = Build.convertBuildNumbertoVyperNumber(buildNumber);

        Vyper vyper = vyperService.fetchVyper(vyperNumber);
        Build build = buildService.fetchBuild(buildNumber);

        boolean isBuReview = status.equalsIgnoreCase("bu review");
        boolean isRework = status.equalsIgnoreCase("at review"); // whenever user reworks it goes to at review

        if (!isBuReview && !isRework) {
            throw new VyperException("Task Status was not understood : " + status);
        }

        if (isBuReview) {
            log.debug("atApproverCallBack(Moving to: BU_REVIEW_CHANGE)");
            build.setState(BuildState.BU_REVIEW_CHANGE);
        }

        build.getAtApprover().setUser(new User("a0999998", "Task Service"));
        build.getAtApprover().setWhen(OffsetDateTime.now());

        // create the audit record
        auditService.createTaskService(
                vyper.getVyperNumber(),
                build.getBuildNumber(),
                CHANGE_WORKFLOW,
                "workflow action (via task service): " + status
        );

        // if this is an approval message, then if we can move to final approved, then move it
        if (isBuReview) {
            finalApproveService.execute(vyper, build);
        }

        try {
            if (build.getState() == BuildState.FINAL_APPROVED) {
                buildStateEmailService.sendBuFinalApproveEmail(vyper, build, BuildState.FINAL_APPROVED);
                taskServiceService.autoApprove(buildNumber);
            } else if (build.getState() == BuildState.BU_REVIEW_CHANGE) {
                buildStateEmailService.sendBuFinalApproveEmail(vyper, build, BuildState.BU_REVIEW_CHANGE);
            }
        } catch (MessagingException ex) {
            log.error("unable to send the email: " + ex.getMessage(), ex);
        }

        buildService.saveBuild(build);

        log.debug("atApproverCallBack(Build state after Callback: {})", build.getState());

        return vyper;
    }

    // Function that will trigger after any AT group approves 
    public Build notifyAtApproval(NotifyAtApprovalForm notifyAtApprovalForm) throws MessagingException {
        log.debug("notifyBuildStateChange(notifyAtApprovalForm:{})", notifyAtApprovalForm);

        Vyper vyper = vyperService.fetchVyper(notifyAtApprovalForm);
        Build build = buildService.fetchBuild(notifyAtApprovalForm);
        if (notifyAtApprovalForm.getRejectReason() == null) {
            buildStateEmailService.sendAtApproveEmail(vyper, build, notifyAtApprovalForm);
        }
        return build;
    }

    // copy the traveler to reworkedTraveler
    public void copyReworkTraveler(Build build) {
        build.getReworkedTraveler().clear();
        build.setReworkedTraveler(build.getTraveler().copy());
    }

}
