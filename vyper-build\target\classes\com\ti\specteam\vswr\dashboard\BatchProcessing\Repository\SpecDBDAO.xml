<?xml version="1.0" encoding="UTF-8" ?>
<!-- $Id: QuickReportsDao.xml,v 1.15 2017/10/19 17:19:19 a0748034 Exp $ -->
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ti.specteam.vswr.dashboard.BatchProcessing.Repository.SpecDBDAO">
    
    <select id="validateDeviceName" resultType="int">
        select count(distinct sd.spec_device)
            from spec_device sd, spec_facility sf
            where sf.facility_at = sd.facility_at
            and sd.sap_material = #{deviceName} and 
            sf.plant = #{atSite}
    </select>
    
    <select id="validateMatlSpecDevice" resultType="int">
        select count(distinct sd.spec_device)
            from spec_device sd, spec_facility sf
            where sf.facility_at = sd.facility_at
            and sd.sap_material = #{deviceName} and 
            sd.spec_device = #{specDevice}
    </select>
    
</mapper>
