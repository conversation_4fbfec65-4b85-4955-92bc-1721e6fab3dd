package com.ti.specteam.vyper.actions;

import com.ti.specteam.vyper.build.model.Die;
import com.ti.specteam.vyper.build.model.DieInstance;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChangeDiesForm extends BuildNumberForm {

    @NotNull
    List<DieInstance> dieInstances = new ArrayList<>();

    public String display() {

        List<String> names = new ArrayList<>();

        for (DieInstance i : dieInstances) {
            for (Die d : i.getDies()) {
                names.add(d.getName());
            }
        }

        return String.join(", ", names);
    }

}
