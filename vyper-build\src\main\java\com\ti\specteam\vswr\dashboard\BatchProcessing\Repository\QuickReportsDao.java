package com.ti.specteam.vswr.dashboard.BatchProcessing.Repository;

import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.spring.annotations.MyBatisRepository;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 * DAO interface for generic reports.
 *
 * <AUTHOR>
 */
@MyBatisRepository
public interface QuickReportsDao {

    /**
     * Fetch data of the whole table.
     *
     * @param tableName The table name.
     * @param params The filter parameters.
     * @return query results
     */
    List<Map<String, Object>> getRows(
            @Param("tableName") String tableName,
            @Param("params") DataTableParams params);

    /**
     * Fetch data of filtered rows.
     *
     * @param tableName The table name.
     * @param params The filter parameters.
     * @return query results
     */
    List<Map<String, Object>> getPage(
            @Param("tableName") String tableName,
            @Param("params") DataTableParams params,
            @Param("siteExt") List siteExt,
            @Param("columns") String listColumns,
            @Param("swrStat") String swrStat);

    /**
     * Fetch the row count of the whole table.
     *
     * @param tableName The table name.
     * @return query results
     */
    int getCount(
            @Param("tableName") String tableName);

    /**
     * Fetch the row count of filtered rows.
     *
     * @param tableName The table name.
     * @param params The filter parameters.
     * @return query results
     */
    int getCountBySearch(
            @Param("tableName") String tableName,
            @Param("params") DataTableParams params,
            @Param("siteExt") List<Map<String,String>> siteExt,
            @Param("swrStat") String swrStat);

    /**
     * Fetch the columns of a table.
     *
     * @param tableName The table name.
     * @return query results
     */
    List<Map<String, String>> getColumns(
            @Param("tableName") String tableName,
    @Param("filter") List<String> filter);

    List<Map<String, String>> getColumns(
            @Param("tableName") String tableName);
            
    List<Map<String, Object>> getPage(
            @Param("tableName") String tableName,
            @Param("params") DataTableParams params);
}