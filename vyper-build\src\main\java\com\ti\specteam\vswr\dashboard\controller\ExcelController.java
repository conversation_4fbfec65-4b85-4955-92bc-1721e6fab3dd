package com.ti.specteam.vswr.dashboard.controller;

// import com.ti.util.directoryservices.EmployeeDirectory;
// import com.ti.util.directoryservices.EmployeeInfo;
import com.ti.specteam.vswr.dashboard.BatchProcessing.QuickReports.QuickReportsServiceImpl;
import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTablePage;
import com.ti.specteam.vswr.dashboard.BatchProcessing.ReportUtils.DataTableParams;
import com.ti.specteam.vswr.dashboard.BatchProcessing.SCSWRService.SCSWRServiceImpl;
import com.ti.specteam.vswr.dashboard.domain.BpDiff;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellConfig;
import com.ti.specteam.vswr.dashboard.domain.ScswrExcelCellValueMap;
import com.ti.specteam.vswr.dashboard.utility.ScswrExcelUtility;
import com.ti.specteam.vyper.security.SecurityService;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequestMapping("/v1/batch")
public class ExcelController {

  private String filePath = "";

  @Autowired private SCSWRServiceImpl swrSvc;

  @Autowired private QuickReportsServiceImpl service;
  private String testUser = "a0297329";

  @Autowired private SecurityService securityService;

  @GetMapping("/excel-configs/constrainedConfigs")
  public ResponseEntity<List<ScswrExcelCellConfig>> getConstrainedSbeExcelConfigs() {
    return ResponseEntity.ok(swrSvc.getConstrainedSbeExcelConfigs());
  }

  @GetMapping("/excel-configs")
  public ResponseEntity<List<ScswrExcelCellConfig>> getExcelConfigs() {
    return ResponseEntity.ok(swrSvc.getSbeExcelCfgs());
  }

  @GetMapping(
      value = "/excel-configs",
      params = {"excelLabel"})
  public ResponseEntity<ScswrExcelCellConfig> getExcelConfigsByExcelLabel(
      @RequestParam(name = "excelLabel") String excelLabel) {
    return ResponseEntity.ok(swrSvc.getSbeExcelCfgByExcelLabel(excelLabel));
  }

  @GetMapping("/scswrDownload")
  public void buildExcelDocument(
      @RequestParam("swrIds") final String swrIds, HttpServletResponse response) {

    String uid = securityService.user().getUserid();

    XSSFWorkbook workbook = new XSSFWorkbook();
    DataTableParams params = new DataTableParams();
    params.addFilter("REQUESTOR_EMPID", uid);
    DataTablePage page = new DataTablePage(params.getsEcho());

    List<Map<String, String>> siteExt = new ArrayList<Map<String, String>>();
    List<Map<String, Object>> userSec = swrSvc.getUserSecurity(uid);
    String sitecode, sitetype, siteext, adminlvl = siteext = sitetype = sitecode = "";
    List<String> siteextlist = new ArrayList<String>();
    for (Map m : userSec) {
      sitecode = (m.get("SITECODE_OVR") != null ? m.get("SITECODE_OVR").toString() : null);
      sitetype = (m.get("SITE_TYPE_OVR") != null ? m.get("SITE_TYPE_OVR").toString() : null);
      siteext = (m.get("SITE_EXT") != null ? m.get("SITE_EXT").toString() : null);
      adminlvl = (m.get("ADMIN_LEVEL") != null ? m.get("ADMIN_LEVEL").toString() : null);
    }

    if (siteext != null) {
      siteextlist = new ArrayList<String>(Arrays.asList(siteext.split(";")));
    }

    if (adminlvl == null || (adminlvl != null && !"Admin".equals(adminlvl))) {
      if ("A/T".equals(sitetype) || "SUB".equals(sitetype)) {
        Map m = new HashMap<String, String>();
        m.put("atsite", sitecode);
        siteExt.add(m);
        if (siteext != null) {
          for (String se : siteextlist) {
            m = new HashMap<String, String>();
            m.put("atsite", se);
            siteExt.add(m);
          }
        }
      }
      if ("SBE".equals(sitetype)) {
        Map m = new HashMap<String, String>();
        m.put("sbe1site", sitecode);
        siteExt.add(m);
        if (siteext != null) {
          for (String se : siteextlist) {
            m = new HashMap<String, String>();
            m.put("sbe1site", se);
            siteExt.add(m);
          }
        }
      }
      if ("BTH".equals(sitetype)) {
        Map m = new HashMap<String, String>();
        if (swrSvc.confirmAtSite(sitecode) > 0) {
          m.put("atsite", sitecode);
          siteExt.add(m);
        } else if (swrSvc.confirmSbeSite(sitecode) > 0) {
          m = new HashMap<String, String>();
          m.put("sbe1site", sitecode);
          siteExt.add(m);
        }

        if (siteext != null) {
          for (String se : siteextlist) {
            m = new HashMap<String, String>();
            if (swrSvc.confirmAtSite(se) > 0) {
              m.put("atsite", se);
            } else if (swrSvc.confirmSbeSite(se) > 0) {
              m.put("sbe1site", se);
            }
            siteExt.add(m);
          }
        }
      }
    }
    try {

      System.out.println("request.getParameter(\"swrIds\") = " + swrIds);
      List<Map<String, Object>> list = swrSvc.getRequests(uid, siteExt, swrIds);
      page.setaaData(list);
      String fileName = "scswr_data_" + uid + ".xlsx";
      List<Map<String, Object>> data = (List<Map<String, Object>>) page.getaaData();

      // populate dropdown sheet
      List<ScswrExcelCellConfig> constrainedColumns = swrSvc.getConstrainedSbeExcelConfigs();
      XSSFSheet dropdownSheet = workbook.createSheet("DropdownSheet");
      for (int index = 0; index < constrainedColumns.size(); index++) {
        this.setDropdownValuesFromConfig(
            constrainedColumns.get(index), data.size(), dropdownSheet, index, workbook);
      }

      // populate data sheet
      List<ScswrExcelCellConfig> columnLabels = swrSvc.getSbeExcelCfgs();
      XSSFSheet scswrdataSheet = workbook.createSheet("scswrdata");
      XSSFCell tmp;
      int startAt = 0;
      XSSFCellStyle style = workbook.createCellStyle();
      XSSFFont font = workbook.createFont();
      font.setFontHeightInPoints((short) 11);
      font.setBold(true);
      style.setFont(font);

      XSSFRow tableHeader = scswrdataSheet.createRow(startAt);
      for (int colIndex = 0; colIndex < columnLabels.size(); colIndex++) {
        tmp = tableHeader.createCell(colIndex);
        tmp.setCellValue((columnLabels.get(colIndex).getExcelLabel()));
        tmp.setCellStyle(style);
      }

      int rowNum = startAt + 1;
      for (Map<String, Object> s : data) {
        XSSFRow rw = scswrdataSheet.createRow(rowNum++);
        for (int colNum = 0; colNum < columnLabels.size(); colNum++) {
          tmp = rw.createCell(colNum);
          // set cell value and formatting
          if (s.get(columnLabels.get(colNum).getDbColumnName()) == null
              || s.get(columnLabels.get(colNum).getDbColumnName()).equals("")) {
            tmp.setCellValue("");
          } else if (isInteger(s.get(columnLabels.get(colNum).getDbColumnName()).toString())
              && colNum > 0) { // colNum > 0 = not SWR_ID
            if (s.get(columnLabels.get(colNum).getDbColumnName()).toString().startsWith("0")) {
              tmp.setCellType(CellType.STRING);
              tmp.setCellValue(s.get(columnLabels.get(colNum).getDbColumnName()).toString());
            } else {
              tmp.setCellValue(
                  Integer.parseInt(s.get(columnLabels.get(colNum).getDbColumnName()).toString()));
            }
          } else if (isDouble(s.get(columnLabels.get(colNum).getDbColumnName()).toString())
              && colNum > 0) { // colNum > 0 = not SWR_ID
            tmp.setCellType(CellType.STRING);
            tmp.setCellValue(
                Double.parseDouble(s.get(columnLabels.get(colNum).getDbColumnName()).toString()));
          } else {
            tmp.setCellValue(
                StringEscapeUtils.unescapeXml(
                    s.get(columnLabels.get(colNum).getDbColumnName()).toString()));
          }
        }
      }

      scswrdataSheet.createFreezePane(0, 1, 0, 1);

      for (int colNum = 1; colNum < columnLabels.size(); colNum++) {
        if (colNum > 1) {
          scswrdataSheet.autoSizeColumn(colNum);
        } else {
          scswrdataSheet.setColumnWidth(colNum - 1, 4000);
        }
      }

      for (int index = 0; index < constrainedColumns.size(); index++) {
        this.setDropdownConstraintsFromConfig(
            constrainedColumns.get(index), data.size(), scswrdataSheet);
      }

      // hide the dropdown sheet and show data sheet
      workbook.setSheetHidden(0, true);
      workbook.setSheetHidden(1, false);
      // set data sheet as active
      workbook.setActiveSheet(1);

      OutputStream out = response.getOutputStream();
      response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
      response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      workbook.write(out);
      out.close();
      workbook.close();
    } catch (Exception e) {
      e.printStackTrace();
      throw new RuntimeException("An error occured while trying to create excel sheet.", e);
    }
  }

  public void setDropdownValuesFromConfig(
      ScswrExcelCellConfig scswrExcelCellConfig,
      int numRows,
      XSSFSheet sheet,
      int index,
      XSSFWorkbook workbook) {
    Row row;
    int r = 0;
    row = sheet.getRow(r);
    if (row == null) {
      row = sheet.createRow(r);
    }
    r++;

    String key = scswrExcelCellConfig.getExcelSpecialName();
    if (key == null) {
      throw new RuntimeException(
          "Excel special name is null for: " + scswrExcelCellConfig.getExcelLabel());
    }
    row.createCell(index).setCellValue(key);
    for (ScswrExcelCellValueMap item : scswrExcelCellConfig.getPossibleValues()) {
      row = sheet.getRow(r);
      if (row == null) {
        row = sheet.createRow(r);
      }
      r++;
      row.createCell(index).setCellValue(item.getExcelValue());
    }
    // create names for the item list constraints, each named from the current key
    Name namedRange;
    String colLetter;
    colLetter = CellReference.convertNumToColString(index);
    namedRange = workbook.createName();
    namedRange.setNameName(key);
    String reference;
    reference = "DropdownSheet!$" + colLetter + "$2:$" + colLetter + "$" + r;
    namedRange.setRefersToFormula(reference);
  }

  public void setDropdownConstraintsFromConfig(
      ScswrExcelCellConfig scswrExcelCellConfig, int numRows, XSSFSheet sheet) {
    String key = scswrExcelCellConfig.getExcelSpecialName();
    DataValidationHelper dvHelper = sheet.getDataValidationHelper();
    DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint(key);
    CellRangeAddressList addressList = null;
    DataValidation validation = null;
    int targetIndex = scswrExcelCellConfig.getExcelIndex();
    for (int i = 1; i <= numRows; i++) {
      addressList = new CellRangeAddressList(i, numRows, targetIndex, targetIndex);
      validation = dvHelper.createValidation(dvConstraint, addressList);
      sheet.addValidationData(validation);
    }
  }

  public boolean isInteger(String value) {
    try {
      Integer.parseInt(value);
    } catch (NumberFormatException e) {
      return false;
    }
    return true;
  }

  public boolean isDouble(String value) {
    try {
      Double.parseDouble(value);
    } catch (NumberFormatException e) {
      return false;
    }
    return true;
  }

  @RequestMapping(value = "/scswrUpload", method = RequestMethod.POST)
  public ResponseEntity<Object> uploadExcel(MultipartFile file) throws IOException {
    List<String> uploadErrorList = new ArrayList<String>();
    List<String> swrIdNoUpdate = new ArrayList<String>();
    List<String> swrIdUpdate = new ArrayList<String>();
    Map<String, List<String>> successResponse = new HashMap<String, List<String>>();
    String currentUserId = securityService.user().getUserid();
    List<ScswrExcelCellConfig> excelConfigs = swrSvc.getSbeExcelCfgs();
    ScswrExcelCellConfig materialShippedConfig =
        swrSvc.getSbeExcelCfgByExcelLabel("Material Shipped");
    ScswrExcelCellConfig asyReqConfig =
        swrSvc.getSbeExcelCfgByExcelLabel("Assembly/Bump Requirements");
    ScswrExcelCellConfig testReqConfig = swrSvc.getSbeExcelCfgByExcelLabel("Test Requirements");
    ScswrExcelCellConfig symbolSpecConfig =
        swrSvc.getSbeExcelCfgByExcelLabel("Symbol Specification");
    ScswrExcelCellConfig symbolTypeConfig = swrSvc.getSbeExcelCfgByExcelLabel("Symbol Type");
    ScswrExcelCellConfig mqTestsConfig =
        swrSvc.getSbeExcelCfgByExcelLabel("Tests required for Assembly or Test MQ");
    ScswrExcelCellConfig relTestsConfig =
        swrSvc.getSbeExcelCfgByExcelLabel("Tests required for Reliability");
    ScswrExcelCellConfig ddfabCodeConfig = swrSvc.getSbeExcelCfgByExcelLabel("Die 2 Fab Code");
    try {
      System.out.println("uploadExcel");

      InputStream excelFile = file.getInputStream();
      XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
      excelFile.close();
      // f.delete();
      XSSFSheet datatypeSheet = workbook.getSheetAt(1);
      Iterator<Row> iterator = datatypeSheet.iterator();
      String swrId = "";
      String device = "";
      String currentStatus = "";
      Calendar cal = new GregorianCalendar();
      SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
      SimpleDateFormat sdf2 = new SimpleDateFormat("dd-MMM-yyyy");

      int ctr = 0;
      String needDate = "";
      String swrInfo = "";
      String specDevice = "";
      int validDevice = 0;
      String plant = "";
      List<String> specDeviceList = new ArrayList<>();
      List<String> needDateList = new ArrayList<>();
      List<Map<String, Object>> swr = null;

      while (iterator.hasNext()) {
        String uploadError = "";
        Row currentRow = iterator.next();

        if (ctr == 0 && currentRow.getPhysicalNumberOfCells() != excelConfigs.size()) {
          uploadErrorList.add("Invalid number of columns");
          break;
        }

        if (ctr > 0) {
          for (int loop = 0; loop < currentRow.getPhysicalNumberOfCells(); loop++) {
            try {
              if (loop != 151) { // not need date
                DataFormatter dataFormatter = new DataFormatter();
                String value = dataFormatter.formatCellValue(currentRow.getCell(loop));
                currentRow.getCell(loop).setCellValue(value);
              }
            } catch (NullPointerException e) {
              continue;
            }
          }

          if (currentRow.getCell(0) != null && !"".equals(currentRow.getCell(0).toString())) {
            swrId = currentRow.getCell(0).toString();

            if (swrSvc.validateSwrId(swrId) == 0) {
              uploadError += "SWR ID " + swrId + ": " + "Invalid SWR ID<br/>";
              break;
            } else {
              swr = swrSvc.getRequests(currentUserId, null, swrId);
              if (currentRow.getCell(17) != null && !"".equals(currentRow.getCell(17).toString())) {
                device = currentRow.getCell(17).toString();
              }
              swrInfo = "<br/>SWR ID " + swrId + " " + device + ":<br/>";
              if (!swr.isEmpty()) {
                currentStatus = swr.get(0).get("CURRENT_STATUS").toString();
              }
            }
          }
          if (currentRow.getCell(151) != null && !"".equals(currentRow.getCell(151).toString())) {
            try {
              Date date = sdf2.parse(currentRow.getCell(151).toString());
              sdf2.applyPattern("MM/dd/yyyy");
              needDate = sdf2.format(date);
            } catch (ParseException p) {
              try {
                System.out.println("p = " + p);
                needDate = sdf.format(sdf.parse(currentRow.getCell(151).toString()));
              } catch (ParseException pp) {
                System.out.println("pp = " + pp);
                try {
                  needDate = sdf.format(currentRow.getCell(151).getDateCellValue());
                } catch (Exception e) {
                  System.out.println("e = " + e);
                  uploadError += "SBE Shipped Paperwork Updated Target Date invalid format<br/>";
                }
              }
            }
            needDateList.add(needDate);
          } else {
            needDateList.add("");
          }

          if (currentRow.getCell(4) == null
              || "".equals(currentRow.getCell(4).toString())) { // swrType
            uploadError += "SWR Type required<br/>";
          }
          if (currentRow.getCell(2) == null
              || "".equals(currentRow.getCell(2).toString())) { // title
            // required
            uploadError += "Title required<br/>";
          }
          if (currentRow.getCell(5) == null
              || "".equals(currentRow.getCell(5).toString())) { // priority
            // required
            uploadError += "Priority required<br/>";
          }
          if (currentRow.getCell(6) == null
              || "".equals(currentRow.getCell(6).toString())) { // market category
            // required
            uploadError += "Market Category required<br/>";
          }
          if (currentRow.getCell(7) == null
              || "".equals(currentRow.getCell(7).toString())) { // atsite
            uploadError += "A/T Site required<br/>";
          } else {
            plant = swrSvc.getPlantCode(currentRow.getCell(7).toString());
          }
          if (currentRow.getCell(8) == null
              || "".equals(currentRow.getCell(8).toString())) { // atsite
            uploadError += "SBE-1 required<br/>";
          }
          if (currentRow.getCell(14) == null
              || "".equals(currentRow.getCell(14).toString())) { // atsite
            uploadError += "PDB Facility required<br/>";
          }
          if (currentRow.getCell(23) != null
              && "".equals(currentRow.getCell(3).toString())
              && currentRow.getCell(23).getStringCellValue().length() > 1010) {
            uploadError += "Comment Purpose length should be < 1010<br/>";
          }
          if (currentRow.getCell(16) == null || "".equals(currentRow.getCell(16).toString())) {
            uploadError += "Traveler Stat required<br/>";
          }
          if (currentRow.getCell(17) == null
              || "".equals(currentRow.getCell(17).toString())) { // device name
            uploadError += "Matl Master Device Name required<br/>";
          } else {
            if (currentRow.getCell(7) != null && !"".equals(currentRow.getCell(7).toString())) {
              if (!"".equals(plant)) {
                validDevice = swrSvc.validateDeviceName(currentRow.getCell(17).toString(), plant);
                if (validDevice == 0) {
                  uploadError +=
                      "ATSS traveler does not exist for specified A/T and material"
                          + "("
                          + currentRow.getCell(7).toString()
                          + " - "
                          + currentRow.getCell(17).toString()
                          + ")<br/>";
                } else {
                  if (currentRow.getCell(18) == null
                      || "".equals(currentRow.getCell(18).toString())) {
                    if (validDevice > 1) {
                      uploadError += "Device is multi-build. Spec Device is required<br/>";
                    } else if (validDevice == 1) {
                      specDevice = currentRow.getCell(18).toString();
                    } else {
                      uploadError +=
                          "Matl Master Device Name - Spec Device combination does not exist<br/>";
                    }
                  } else {
                    validDevice =
                        swrSvc.validateMatlSpecDevice(
                            currentRow.getCell(17).toString(), currentRow.getCell(18).toString());
                    if (validDevice > 0) {
                      specDevice = currentRow.getCell(18).toString();
                    } else {
                      uploadError +=
                          "Matl Master Device Name - Spec Device combination does not exist<br/>";
                    }
                  }
                }
                specDeviceList.add(specDevice);
              }
            }
          }

          if (currentRow.getCell(19) == null
              || "".equals(currentRow.getCell(19).toString())) { // parent device
            uploadError += "Parent Device required<br/>";
          }
          if (currentRow.getCell(20) == null
              || "".equals(currentRow.getCell(20).toString())) { // bld qty
            uploadError += "Bld Qty required<br/>";
          } else if (Double.parseDouble(currentRow.getCell(20).toString()) <= 0) {
            uploadError += "Bld Qty should be > 0";
          }
          if (currentRow.getCell(21) == null
              || "".equals(currentRow.getCell(21).toString())) { // pin
            uploadError += "Pin required<br/>";
          }
          if (currentRow.getCell(22) == null
              || "".equals(currentRow.getCell(22).toString())) { // pkg
            uploadError += "Pkg required<br/>";
          }
          uploadError +=
              ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                  materialShippedConfig, currentRow);
          if (ScswrExcelUtility.isValuePresent(asyReqConfig, currentRow)) {
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    asyReqConfig, currentRow);
          }
          if (ScswrExcelUtility.isValuePresent(testReqConfig, currentRow)) {
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    testReqConfig, currentRow);
          }
          if (ScswrExcelUtility.isValuePresent(symbolSpecConfig, currentRow)) {
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    symbolSpecConfig, currentRow);
          }
          if (ScswrExcelUtility.isValuePresent(symbolTypeConfig, currentRow)) {
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    symbolTypeConfig, currentRow);
          }
          if (ScswrExcelUtility.isValuePresent(mqTestsConfig, currentRow)) {
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    mqTestsConfig, currentRow);
          }
          if (ScswrExcelUtility.isValuePresent(relTestsConfig, currentRow)) {
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    relTestsConfig, currentRow);
          }
          if (!"".equals(currentStatus)
              && (!currentStatus.contains("Forecast")
                  && !currentStatus.contains("Action_Required"))) {
            if (ScswrExcelUtility.isValuePresent(asyReqConfig, currentRow)
                && !ScswrExcelUtility.isDbValue("non", asyReqConfig, currentRow)
                && (currentRow.getCell(133) == null
                    || "".equals(currentRow.getCell(133).toString()))) { // bumpreq
              uploadError += "Bump Req required<br/>";
            }
          }
          if (currentRow.getCell(133) != null
              && !currentRow.getCell(133).toString().contentEquals("y")
              && !currentRow.getCell(133).toString().contentEquals("n")) { // bumpreq
            uploadError += "Bump Req must be 'y' or 'n'<br/>";
          }

          if (ScswrExcelUtility.isValuePresent(asyReqConfig, currentRow)
              && !ScswrExcelUtility.isDbValue("non", asyReqConfig, currentRow)) { // asyreq

            if (currentRow.getCell(29) == null
                || "".equals(currentRow.getCell(29).getStringCellValue())) { // if (dieRev.length() == 0) {
              uploadError += "Die Rev required<br/>";
            }
            if (currentRow.getCell(30) == null
                || "".equals(currentRow.getCell(30).getStringCellValue())) { // if (dieSize.length() == 0) {
              uploadError += "Die Size required<br/>";
            }
            if (currentRow.getCell(31) == null
                || "".equals(currentRow.getCell(31).getStringCellValue())) { // if (diedesig.length() == 0) {
              uploadError += "Die Designator required<br/>";
            }
            if (currentRow.getCell(32) == null || "".equals(currentRow.getCell(32).getStringCellValue())) { // fabcode
              uploadError += "Fab Code required<br/>";
            }
            if (currentRow.getCell(34) == null || "".equals(currentRow.getCell(34).getStringCellValue())) { // wafer use
              uploadError += "# Wafers to use required<br/>";
            }
            if (currentRow.getCell(35) == null || "".equals(currentRow.getCell(35).getStringCellValue())) { // die lot
              uploadError += "Die Lot required<br/>";
            }
            if (currentRow.getCell(36) == null || "".equals(currentRow.getCell(36).getStringCellValue())) { // wafer dia
              uploadError += "Wafer Diameter required<br/>";
            }
            if (currentRow.getCell(37) == null
                || "".equals(currentRow.getCell(37).getStringCellValue())) { // wafer thcik
              uploadError += "Wafer Thickness required<br/>";
            }
            if (currentRow.getCell(42) == null || "".equals(currentRow.getCell(42).getStringCellValue())) { // probed
              uploadError += "Probed required<br/>";
            } else if ("yes".equals(currentRow.getCell(42).getStringCellValue())
                && (currentRow.getCell(43) == null
                    || "".equals(currentRow.getCell(43).getStringCellValue()))) { // inkless
              uploadError += "If Probed, is the Lot Inkless? required<br/>";
            } else if (currentRow.getCell(43) != null
                && "yes".equals(currentRow.getCell(43).getStringCellValue())
                && (currentRow.getCell(44) == null
                    || "".equals(currentRow.getCell(44).getStringCellValue()))) { // inkless method
              uploadError += "If Inkless, Build by? required<br/>";
            } else if (currentRow.getCell(44) != null
                && "waferMapReq".equals(currentRow.getCell(44).getStringCellValue())
                && (currentRow.getCell(45) == null
                    || "".equals(currentRow.getCell(45).getStringCellValue()))) { // map avail
              uploadError += "If Wafer Mapping required, standard Map location? required<br/>";
            }

            if (currentRow.getCell(48) == null || "".equals(currentRow.getCell(48).getStringCellValue())) { // use gec
              uploadError += "Use GECs required<br/>";
            }
          }

          if (currentRow.getCell(50) != null
              && "y".equals(currentRow.getCell(50).getStringCellValue())) { // validate dual die
            if (currentRow.getCell(51) == null || "".equals(currentRow.getCell(41).getStringCellValue())) {
              uploadError += "Die 2 Matl Master Die Name required<br/>";
            }
            if (currentRow.getCell(52) == null || "".equals(currentRow.getCell(42).getStringCellValue())) {
              uploadError += "Die 2 Rev required<br/>";
            }
            if (currentRow.getCell(54) == null || "".equals(currentRow.getCell(54).getStringCellValue())) {
              uploadError += "Die 2 Die Size required<br/>";
            }
            if (currentRow.getCell(56) == null || "".equals(currentRow.getCell(56).getStringCellValue())) {
              uploadError += "Die 2 Die Lot/SAP Lot required<br/>";
            }
            uploadError +=
                ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                    ddfabCodeConfig, currentRow);
            if (currentRow.getCell(53) == null || "".equals(currentRow.getCell(53).getStringCellValue())) {
              uploadError += "Die 2 Die Designator required<br/>";
            }
            if (currentRow.getCell(57) == null || "".equals(currentRow.getCell(57).getStringCellValue())) {
              uploadError += "Die 2 Wafer Diameter required<br/>";
            }
            if (currentRow.getCell(58) == null || "".equals(currentRow.getCell(58).getStringCellValue())) {
              uploadError += "Die 2 Wafer Thickness required<br/>";
            }
            if (currentRow.getCell(121) == null || "".equals(currentRow.getCell(121).getStringCellValue())) {
              uploadError += "Die 2 Probed required<br/>";
            } else if (currentRow.getCell(121) != null
                && "yes".equals(currentRow.getCell(121).getStringCellValue())
                && (currentRow.getCell(122) == null || "".equals(currentRow.getCell(122).getStringCellValue()))) {
              uploadError += "Die 2 If Probed, is the Lot Inkless? required<br/>";
            } else if (currentRow.getCell(122) != null
                && "yes".equals(currentRow.getCell(122).getStringCellValue())
                && (currentRow.getCell(123) == null || "".equals(currentRow.getCell(123).getStringCellValue()))) {
              uploadError += "Die 2 If Inkless, Build by? required<br/>";
            } else if (currentRow.getCell(123) != null
                && "wafMapReq".equals(currentRow.getCell(123).getStringCellValue())
                && (currentRow.getCell(124) == null || "".equals(currentRow.getCell(124).getStringCellValue()))) {
              uploadError += "Die 2 Map Location required<br/>";
            }
            if (currentRow.getCell(126) == null || "".equals(currentRow.getCell(126).getStringCellValue())) {
              uploadError += "Die 2 Use GECs required<br/>";
            }
          } else {
            if (ScswrExcelUtility.isValuePresent(ddfabCodeConfig, currentRow)) {
              uploadError +=
                  ScswrExcelUtility.getErrorMessageIfValueNotPresentOrInvalid(
                      ddfabCodeConfig, currentRow);
            }
          }

          // if (commentBump != null && commentBump.length() > SwrConstants.COMMENTBUMP) {
          if (currentRow.getCell(138) != null
              && currentRow.getCell(138).getStringCellValue().length() > 1000) { // comment bump
            uploadError += "Bump Comment too large<br/>";
          }
          
         String assyBumpReqDbValue = "";
         try {
          assyBumpReqDbValue = ScswrExcelUtility.extractDbValue(asyReqConfig, currentRow);   
         }
         catch (Exception e){
          if(currentRow.getCell(asyReqConfig.getExcelIndex()) != null) {
            assyBumpReqDbValue = currentRow.getCell(asyReqConfig.getExcelIndex()).getStringCellValue();
          }
        }
          if ("".equals(assyBumpReqDbValue)
                && !"non".equals(assyBumpReqDbValue)
                && (currentRow.getCell(133) != null || "".equals(currentRow.getCell(133).getStringCellValue()))
                && ("n".equals(currentRow.getCell(133).getStringCellValue()))) {

            if (currentRow.getCell(71) == null
                || "".equals(currentRow.getCell(71).getStringCellValue())) { // Wire diameter
              uploadError += "Wire Diameter required<br/>";
            }
          }


          if (currentRow.getCell(61) != null
              && currentRow.getCell(61).getStringCellValue().length() > 256) { // leadframe1
            uploadError += "Leadframe Substrate text too large<br/>";
          }
          if (currentRow.getCell(62) != null
              && currentRow.getCell(62).getStringCellValue().length() > 256) { // mount compund
            uploadError += "Mount Compound text too large<br/>";
          }
          if (currentRow.getCell(63) != null
              && currentRow.getCell(63).getStringCellValue().length() > 256) { // mold
            uploadError += "Mold Compound text too large<br/>";
          }
          if (currentRow.getCell(64) != null
              && currentRow.getCell(64).getStringCellValue().length() > 256) { // mold
            uploadError += "Wire P/N text too large<br/>";
          }
          if (currentRow.getCell(65) != null
              && currentRow.getCell(65).getStringCellValue().length() > 256) { // lid
            uploadError += "Solderball text too large<br/>";
          }
          if (currentRow.getCell(67) != null
              && currentRow.getCell(67).getStringCellValue().length() > 256) { // lid
            uploadError += "Lid text too large<br/>";
          }
          if (currentRow.getCell(69) != null
              && currentRow.getCell(69).getStringCellValue().length() > 256) { // base outline
            uploadError += "Base Outline text too large<br/>";
          }
          if (currentRow.getCell(66) != null
              && currentRow.getCell(66).getStringCellValue().length() > 256) { // chipchap
            uploadError += "Chip Cap text too large<br/>";
          }

          if (currentRow.getCell(88) != null
              && currentRow.getCell(88).getStringCellValue().length() > 1000) { // comment mq
            uploadError += "MQ Comment too large<br/>";
          }

          if (currentRow.getCell(90) != null
              && currentRow.getCell(90).getStringCellValue().length() > 1000) { // comment rel
            uploadError += "Reliability Comment too large<br/>";
          }
          // TODO
          if (currentRow.getCell(91) != null
              && "drawing".equals(currentRow.getCell(91).getStringCellValue()) // symbol spec
          ) { // symbol format
            uploadError += "Symbol Spec required<br/>";
          }

          if (currentRow.getCell(143) == null
              || "".equals(currentRow.getCell(143).getStringCellValue())) { // rtp2 submitted
            uploadError += "Submitted for RTP2 Review & Signoff?<br/>";
          }

          if ((currentRow.getCell(143) != null && "yes".equals(currentRow.getCell(143).getStringCellValue()))
              && (currentRow.getCell(144) == null
                  || "".equals(currentRow.getCell(144).getStringCellValue()))) { // rtpBaseset.length
            uploadError += "eRTP Baseset Name required<br/>";
          }

          if (currentRow.getCell(105) != null
              && currentRow.getCell(105).getStringCellValue().length() > 1000) { // brd avail
            uploadError += "Comment Test requires<br/>";
          }
          if (uploadError.length() > 0) {
            uploadErrorList.add(swrInfo + uploadError);
            swrInfo = "";
            uploadError = "";
          }
        }
        ctr++;
      }
      if (uploadErrorList.size() == 0) {
        List<Map<String, Object>> finalSbeView = new ArrayList<Map<String, Object>>();
        Map<String, Object> tempSbeView = new HashMap<String, Object>();
        Map<String, Object> tempSbeSwrView = new HashMap<String, Object>();
        String colData = "";
        ctr = 0;
        String today = sdf.format(cal.getTime());
        iterator = datatypeSheet.iterator();
        while (iterator.hasNext()) {
          Row currentRow = iterator.next();
          if (ctr > 0) {
            // List scswrList = new ArrayList();
            for (int loop = 0; loop < currentRow.getPhysicalNumberOfCells(); loop++) {
              try {
                currentRow.getCell(loop).setCellType(CellType.STRING);
              } catch (NullPointerException e) {
                continue;
              }
            }

            Map<String, String> scswrMap = new HashMap<>();
            for (ScswrExcelCellConfig excelConfig : excelConfigs) {
              String extractedDbValue = ScswrExcelUtility.extractDbValue(excelConfig, currentRow);
              String dbColumnName = excelConfig.getDbColumnName();
              scswrMap.put(dbColumnName, extractedDbValue);
            }
            scswrMap.put("UPLOAD_DTTM", today);
            scswrMap.put("UPLOADED_BY", currentUserId);
            swrSvc.replaceRedbullRequest(scswrMap);

            swrId = currentRow.getCell(0).toString();
            if (!swrSvc.isBpDiff(currentUserId, swrId)) {
              swrSvc.delRedbullMax(swrId, currentUserId);
              swrIdNoUpdate.add(swrId);
            } else {
              swrIdUpdate.add(swrId);
            }
            finalSbeView = new ArrayList<Map<String, Object>>();
          }
          ctr++;
        }
      }

    } catch (FileNotFoundException e) {
      e.printStackTrace();
      String error = e.toString();
      Map<String, String> errorResponse = new HashMap<String, String>();
      errorResponse.put("error", error);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    if (uploadErrorList.size() > 0) {
      Map<String, List<String>> newUploadError = new HashMap<String, List<String>>();
      newUploadError.put("uploadErrors", uploadErrorList);

      log.info("uploadExcel(uploadError:{})", uploadErrorList);
      return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(newUploadError);
    }

    successResponse.put("swrIdNoUpdate", swrIdNoUpdate);
    successResponse.put("swrIdUpdate", swrIdUpdate);
    return ResponseEntity.ok(successResponse);
  }

  @RequestMapping(value = "/atViewDownload", method = RequestMethod.POST)
  public void atViewExcelDocument(HttpServletRequest request, HttpServletResponse response)
      throws Exception {

    XSSFWorkbook workbook = new XSSFWorkbook();
    UserDetails principal =
        (UserDetails) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    String uid = principal.getUsername();

    DataTableParams params = new DataTableParams(request.getParameterMap());
    List<Map<String, String>> columns = service.getColumns("AT_FORECAST_REVISED_VW", null);
    List<Map<String, Object>> userSec = swrSvc.getUserSecurity(uid);
    List<Map<String, String>> siteExt = new ArrayList();
    String sitecode, sitetype, siteext, adminlvl = siteext = sitetype = sitecode = "";
    List<String> siteextlist = new ArrayList<String>();
    for (Map m : userSec) {
      sitecode = (m.get("SITECODE_OVR") != null ? m.get("SITECODE_OVR").toString() : null);
      sitetype = (m.get("SITE_TYPE_OVR") != null ? m.get("SITE_TYPE_OVR").toString() : null);
      siteext = (m.get("SITE_EXT") != null ? m.get("SITE_EXT").toString() : null);
      adminlvl = (m.get("ADMIN_LEVEL") != null ? m.get("ADMIN_LEVEL").toString() : null);
    }

    if (siteext != null) {
      siteextlist = new ArrayList<String>(Arrays.asList(siteext.split(";")));
    }

    if (adminlvl == null || (adminlvl != null && !"Admin".equals(adminlvl))) {
      if ("A/T".equals(sitetype)) {
        Map m = new HashMap<String, String>();
        m.put("atsite", sitecode);
        siteExt.add(m);
        if (siteext != null) {
          for (String se : siteextlist) {
            m = new HashMap<String, String>();
            m.put("atsite", se);
            siteExt.add(m);
          }
        }
      }
      if ("BTH".equals(sitetype)) {
        if (swrSvc.confirmAtSite(sitecode) > 0) {
          Map m = new HashMap<String, String>();
          m.put("atsite", sitecode);
          siteExt.add(m);
        }

        if (siteext != null) {
          for (String se : siteextlist) {
            Map m = new HashMap<String, String>();
            if (swrSvc.confirmAtSite(se) > 0) {
              m.put("atsite", se);
            }
            siteExt.add(m);
          }
        }
      }
    }
    List<Map<String, Object>> data =
        service.getPage("AT_FORECAST_REVISED_VW", params, siteExt, "*", null);
    List<String> labels =
        Arrays.asList(
            "AT Site",
            "SWR ID",
            "SWR Title",
            "SWR Type",
            "Requestor  Name",
            "Status",
            "SBE",
            "SBE 1 Site",
            "Device Name",
            "Flow Type",
            "Travel Status Desc",
            "REDBULL",
            "Priority",
            "Pin",
            "Pkg",
            "Market Category",
            "ISO",
            "MCM",
            "Die Name",
            "Die Rev",
            "Fab Code",
            "*Die Lot/SAP Lot",
            "Wafer Diameter",
            "Which wafer# to use",
            "IO Number",
            "PO#&Line item",
            "Build Qty",
            "Leadframe Substrate",
            "Comment",
            "Wire PN",
            "Comment",
            "Mold Compound",
            "Comment",
            "Mount Compound",
            "Comment",
            "Solderball",
            "Comment",
            "Lid",
            "Comment",
            "Chip Capacitor",
            "Tester Config",
            "Packing Material",
            "Reel P/N",
            "Tape P/N",
            "Cover P/N",
            "Pack Requirements Comments",
            "e-WAIVER #",
            "SWR Sticker Type",
            "PARTIAL DISPOSITION ( If disposition = Ship Partial how should remaining FGs be"
                + " packed?)",
            "Attn",
            "Plant",
            "Address",
            "Qty",
            "Ship Type",
            "Ship Device Name",
            "SBE Target Forecast Submit Date",
            "SBE Updated Forecast Submit Date",
            "SBE Actual Forecast Submit Date",
            "SWR Purpose ",
            "SWR General Comments");

    XSSFSheet sheet = workbook.createSheet();
    XSSFCell tmp;
    // Create header now
    // Retrieve worksheet Header Values

    // set worksheet main header row
    int startAt = 0;

    XSSFCellStyle style = workbook.createCellStyle();
    XSSFFont font = workbook.createFont();
    font.setFontHeightInPoints((short) 11);
    font.setBold(true);
    style.setFont(font);

    CreationHelper helper = workbook.getCreationHelper();
    XSSFHyperlink link = (XSSFHyperlink) helper.createHyperlink(HyperlinkType.URL);
    XSSFCellStyle linkStyle = workbook.createCellStyle();
    XSSFFont linkFont = workbook.createFont();
    linkFont.setUnderline(XSSFFont.U_SINGLE);
    linkFont.setColor(IndexedColors.BLUE.getIndex());
    linkStyle.setFont(linkFont);

    // Create table header row
    XSSFRow tableHeader = sheet.createRow(startAt);
    for (int colIndex = 0; colIndex < labels.size(); colIndex++) {
      tmp = tableHeader.createCell(colIndex);
      tmp.setCellValue((labels.get(colIndex)));
      tmp.setCellStyle(style);
    }

    // Populate data rows
    // create table body
    int rowNum = startAt + 1;
    for (Map<String, Object> s : data) {
      XSSFRow rw = sheet.createRow(rowNum++);
      for (int colNum = 0; colNum < columns.size(); colNum++) {
        tmp = rw.createCell(colNum);
        // set cell value and formatting
        if (s.get(columns.get(colNum).get("COLUMN_NAME")) == null
            || s.get(columns.get(colNum).get("COLUMN_NAME")).equals("")) {
          tmp.setCellValue("");
        } else if (isInteger(s.get(columns.get(colNum).get("COLUMN_NAME")).toString())
            && colNum > 0) { // colNum > 0 = not SWR_ID
          //                        if(s.get(columnNames.get(colNum)).toString().startsWith("0")) {
          //                            tmp.setCellValue("'"+s.get(columnNames.get(colNum)));
          //                        } else {
          tmp.setCellValue(
              Integer.parseInt(s.get(columns.get(colNum).get("COLUMN_NAME")).toString()));
          //                        }
        } else if (isDouble(s.get(columns.get(colNum).get("COLUMN_NAME")).toString())
            && colNum > 0) { // colNum > 0 = not SWR_ID
          tmp.setCellValue(
              Double.parseDouble(s.get(columns.get(colNum).get("COLUMN_NAME")).toString()));
        } else {
          tmp.setCellValue(
              StringEscapeUtils.unescapeXml(
                  s.get(columns.get(colNum).get("COLUMN_NAME")).toString()));
        }
        if (columns.get(colNum).get("COLUMN_NAME").equals("SWR_ID")) {
          link.setAddress(
              "https://"
                  + request.getServerName()
                  + "/scswr/viewswr.do?swrId="
                  + s.get(columns.get(colNum).get("COLUMN_NAME"))
                  + "&action=viewSwr");
          tmp.setHyperlink((XSSFHyperlink) link);
          tmp.setCellStyle(linkStyle);
          link = (XSSFHyperlink) helper.createHyperlink(HyperlinkType.URL);
        }
      }
    }

    sheet.createFreezePane(0, 1, 0, 1);

    for (int colNum = 0; colNum < labels.size(); colNum++) {
      sheet.autoSizeColumn(colNum);
    }

    try {
      OutputStream out = response.getOutputStream();
      response.setHeader("Content-Disposition", "attachment; filename=AT_View_" + uid + ".xlsx");
      response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      workbook.write(out);
      out.close();
      workbook.close();

    } catch (Exception e) {
      System.out.println(" Write the output to a file exception");
      System.out.println(e);
    }
  }
}
