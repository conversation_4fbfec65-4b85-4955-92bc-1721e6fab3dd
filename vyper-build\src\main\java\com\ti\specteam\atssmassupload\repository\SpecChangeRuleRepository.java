package com.ti.specteam.atssmassupload.repository;

import com.ti.specteam.atssmassupload.domain.SpecChangeRule;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR>
 */

@Repository
@RequiredArgsConstructor
public class SpecChangeRuleRepository {

	private final NamedParameterJdbcTemplate jdbcTemplate;

	public List<SpecChangeRule> findAllSpecChangeRule() {
		String sql = "SELECT * FROM MU_SPEC_CHANGE_RULE";
		return jdbcTemplate.query(sql, BeanPropertyRowMapper.newInstance(SpecChangeRule.class));

	};

	public List<String> findAllowedComponentNames() {
		String sql = "SELECT distinct component_name FROM MU_SPEC_CHANGE_RULE";
		return jdbcTemplate.query(sql, BeanPropertyRowMapper.newInstance(String.class));

	};

}