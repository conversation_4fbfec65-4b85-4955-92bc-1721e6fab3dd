package com.ti.specteam.atssmassupload.domain;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "MU_PROJ")
@Getter
@Setter
public class ProjectEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private String id;

    @Column(length = 100)
    private String projName;

    @Column(length = 20, updatable = false)
    private String projNumber;

    @Column(length = 30, updatable = false)
    private String projType;

    @Column(length = 30)
    private String status;

    @Column(length = 10, updatable = false)
    private String facilityAt;

    @Column(columnDefinition="raw(16)", updatable = false)
    private String refSpecId;

    @Column(length = 30)
    private String cmsNumber;

    @Column(length = 10)
    private String ownerId;

    @Column(length = 10, updatable = false)
    private String createdBy;

    @Column(updatable = false)
    private Date createdDttm;

    @Column(length = 10)
    private String updatedBy;

    private Date updatedDttm;

    @OneToOne
    @JoinColumn(name = "refSpecId", referencedColumnName = "id",insertable = false, updatable = false)
    private ReferenceSpecEntity referenceSpec;

}
